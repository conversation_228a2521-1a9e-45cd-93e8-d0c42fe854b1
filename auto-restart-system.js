#!/usr/bin/env node

/**
 * Auto-Restart System for 24/7 Streaming
 * Automatically restarts streams when they stop unexpectedly
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Auto-Restart System for 24/7 Streaming');
console.log('==========================================\n');

class AutoRestartSystem {
  constructor() {
    this.monitoringActive = false;
    this.restartAttempts = new Map();
    this.maxRestartAttempts = 5;
    this.restartDelay = 30000; // 30 seconds
    this.logFile = path.join(__dirname, 'logs', 'auto-restart.log');
  }

  async startMonitoring() {
    console.log('🚀 Starting 24/7 auto-restart monitoring...\n');
    this.monitoringActive = true;

    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Monitor every 60 seconds
    const monitorInterval = setInterval(async () => {
      if (!this.monitoringActive) {
        clearInterval(monitorInterval);
        return;
      }

      try {
        await this.checkAndRestartStreams();
      } catch (error) {
        this.log(`ERROR: Monitoring failed: ${error.message}`);
      }
    }, 60000); // 60 seconds

    console.log('✅ Auto-restart monitoring started');
    console.log('📊 Checking every 60 seconds');
    console.log(`📝 Logs saved to: ${this.logFile}`);
    console.log('🛑 Press Ctrl+C to stop monitoring\n');

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping auto-restart monitoring...');
      this.monitoringActive = false;
      process.exit(0);
    });
  }

  async checkAndRestartStreams() {
    try {
      const { db } = require('./db/database');
      
      // Get streams that should be running 24/7 (duration = 0 or null)
      const streams247 = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, platform, rtmp_url, status, start_time, duration, user_id, video_id, stream_key, bitrate, resolution, fps, orientation, loop_video
          FROM streams 
          WHERE (duration IS NULL OR duration = 0)
          AND status IN ('offline', 'error', 'failed')
          AND created_at > datetime('now', '-1 days')
          ORDER BY created_at DESC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      for (const stream of streams247) {
        await this.handleStreamRestart(stream);
      }

    } catch (error) {
      this.log(`ERROR: Failed to check streams: ${error.message}`);
    }
  }

  async handleStreamRestart(stream) {
    const streamId = stream.id;
    const attempts = this.restartAttempts.get(streamId) || 0;

    // Check if we've exceeded max restart attempts
    if (attempts >= this.maxRestartAttempts) {
      this.log(`MAX_ATTEMPTS_REACHED: ${stream.title} (${streamId}) - ${attempts} attempts made`);
      return;
    }

    // Check if stream was recently stopped (within last 5 minutes)
    const now = new Date();
    const recentlyUpdated = stream.updated_at ? new Date(stream.updated_at) : new Date(stream.created_at);
    const timeSinceUpdate = now - recentlyUpdated;
    
    if (timeSinceUpdate < 5 * 60 * 1000) { // 5 minutes
      this.log(`ATTEMPTING_RESTART: ${stream.title} (${streamId}) - Attempt ${attempts + 1}/${this.maxRestartAttempts}`);
      
      try {
        // Import streaming service
        const streamingService = require('./services/streamingService');
        
        // Attempt to restart the stream
        const result = await streamingService.startStream(streamId);
        
        if (result.success) {
          this.log(`RESTART_SUCCESS: ${stream.title} (${streamId}) - Stream restarted successfully`);
          console.log(`✅ Successfully restarted: "${stream.title}"`);
          
          // Reset restart attempts on success
          this.restartAttempts.delete(streamId);
        } else {
          this.log(`RESTART_FAILED: ${stream.title} (${streamId}) - ${result.error}`);
          console.log(`❌ Failed to restart: "${stream.title}" - ${result.error}`);
          
          // Increment restart attempts
          this.restartAttempts.set(streamId, attempts + 1);
        }
        
      } catch (error) {
        this.log(`RESTART_ERROR: ${stream.title} (${streamId}) - ${error.message}`);
        console.log(`❌ Error restarting: "${stream.title}" - ${error.message}`);
        
        // Increment restart attempts
        this.restartAttempts.set(streamId, attempts + 1);
      }
    }
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  async generateUptimeReport() {
    console.log('📊 Generating 24/7 Uptime Report...\n');
    
    try {
      const { db } = require('./db/database');
      
      // Get 24/7 streams from last 7 days
      const streams247 = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, platform, status, start_time, end_time, duration, created_at
          FROM streams 
          WHERE (duration IS NULL OR duration = 0)
          AND created_at > datetime('now', '-7 days')
          ORDER BY created_at DESC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      console.log(`📈 24/7 Streaming Report (Last 7 Days)`);
      console.log(`====================================\n`);
      console.log(`Total 24/7 streams: ${streams247.length}\n`);

      let totalUptime = 0;
      let totalPossibleTime = 0;
      const uptimeStats = [];

      streams247.forEach(stream => {
        if (stream.start_time) {
          const startTime = new Date(stream.start_time);
          const endTime = stream.end_time ? new Date(stream.end_time) : new Date();
          const actualRuntime = (endTime - startTime) / 1000 / 60; // minutes
          
          // Calculate how long it should have been running (from start to now)
          const possibleRuntime = (new Date() - startTime) / 1000 / 60; // minutes
          
          const uptimePercentage = (actualRuntime / possibleRuntime) * 100;
          
          uptimeStats.push({
            title: stream.title,
            actualRuntime: actualRuntime,
            possibleRuntime: possibleRuntime,
            uptimePercentage: uptimePercentage
          });
          
          totalUptime += actualRuntime;
          totalPossibleTime += possibleRuntime;
        }
      });

      if (uptimeStats.length > 0) {
        const overallUptime = (totalUptime / totalPossibleTime) * 100;
        
        console.log(`📊 Overall 24/7 Uptime: ${overallUptime.toFixed(2)}%\n`);
        
        console.log('📋 Individual Stream Performance:');
        uptimeStats.forEach((stat, index) => {
          console.log(`${index + 1}. "${stat.title}"`);
          console.log(`   Uptime: ${stat.uptimePercentage.toFixed(2)}%`);
          console.log(`   Runtime: ${Math.round(stat.actualRuntime)} min (${Math.round(stat.actualRuntime/60)} hours)`);
          console.log('');
        });

        // Recommendations
        console.log('💡 RECOMMENDATIONS FOR BETTER 24/7 UPTIME:');
        
        if (overallUptime < 90) {
          console.log('   🔧 Consider implementing redundant streaming setup');
          console.log('   🔧 Monitor network stability more closely');
          console.log('   🔧 Set up alerts for stream failures');
        }
        
        if (overallUptime >= 90 && overallUptime < 95) {
          console.log('   ✅ Good uptime! Minor optimizations needed');
          console.log('   🔧 Fine-tune auto-restart parameters');
        }
        
        if (overallUptime >= 95) {
          console.log('   🎉 Excellent uptime! System is well optimized');
          console.log('   ✅ Continue current monitoring practices');
        }
      }

    } catch (error) {
      console.error('Failed to generate uptime report:', error);
    }
  }
}

// Command line interface
if (require.main === module) {
  const autoRestart = new AutoRestartSystem();
  
  const command = process.argv[2];
  
  if (command === 'report') {
    autoRestart.generateUptimeReport().catch(console.error);
  } else {
    autoRestart.startMonitoring().catch(console.error);
  }
}

module.exports = AutoRestartSystem;
