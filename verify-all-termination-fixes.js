#!/usr/bin/env node

/**
 * Comprehensive Termination Fix Verification
 * Verifies ALL potential stream termination mechanisms are disabled
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Comprehensive Stream Termination Fix Verification');
console.log('===================================================\n');

class TerminationFixVerifier {
  constructor() {
    this.results = {
      fixesVerified: [],
      criticalIssues: [],
      warnings: [],
      totalChecks: 0,
      passedChecks: 0
    };
  }

  log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${type}] ${message}`);
  }

  check(description, condition, critical = true) {
    this.results.totalChecks++;
    
    if (condition) {
      this.results.passedChecks++;
      this.results.fixesVerified.push(description);
      this.log(`✅ ${description}`, 'PASS');
    } else {
      if (critical) {
        this.results.criticalIssues.push(description);
        this.log(`❌ CRITICAL: ${description}`, 'CRITICAL');
      } else {
        this.results.warnings.push(description);
        this.log(`⚠️  WARNING: ${description}`, 'WARNING');
      }
    }
  }

  async verifyStreamingServiceFixes() {
    this.log('Verifying streamingService.js termination fixes...', 'INFO');
    
    try {
      const serviceFile = fs.readFileSync('./services/streamingService.js', 'utf8');
      
      // Check 1: Main health check disabled
      this.check(
        'Main health check function disabled',
        serviceFile.includes('EMERGENCY: Health check disabled to prevent automatic stream terminations'),
        true
      );
      
      // Check 2: Periodic health check calls disabled
      this.check(
        'Periodic health check calls disabled',
        serviceFile.includes('DISABLED: await performStreamHealthCheck()'),
        true
      );
      
      // Check 3: Long-duration health check disabled
      this.check(
        'Long-duration health check disabled (NEW FIX)',
        serviceFile.includes('EMERGENCY: Long-duration health monitoring DISABLED to prevent 1-hour terminations'),
        true
      );
      
      // Check 4: File handle health monitoring disabled
      this.check(
        'File handle health monitoring disabled (NEW FIX)',
        serviceFile.includes('EMERGENCY: File handle health monitoring DISABLED to prevent potential terminations'),
        true
      );
      
      // Check 5: Safe cleanup interval
      this.check(
        'Cleanup interval set to safe 2-hour value',
        serviceFile.includes('CLEANUP_INTERVAL = 2 * 60 * 60 * 1000'),
        false
      );
      
      // Check 6: No hardcoded 1-hour timeouts
      const oneHourTimeouts = (serviceFile.match(/60 \* 60 \* 1000/g) || []).length;
      this.check(
        'No hardcoded 1-hour (3600000ms) timeouts found',
        oneHourTimeouts === 0,
        true
      );
      
      // Check 7: No 3600 second timeouts
      const hardcodedTimeouts = serviceFile.includes('3600') && !serviceFile.includes('3600000');
      this.check(
        'No hardcoded 3600 second timeouts found',
        !hardcodedTimeouts,
        true
      );
      
    } catch (error) {
      this.results.criticalIssues.push(`Cannot read streamingService.js: ${error.message}`);
      this.log(`❌ Cannot verify streamingService.js: ${error.message}`, 'ERROR');
    }
  }

  async verifySchedulerServiceFixes() {
    this.log('Verifying schedulerService.js fixes...', 'INFO');
    
    try {
      const schedulerFile = fs.readFileSync('./services/schedulerService.js', 'utf8');
      
      // Check 1: Duration checking disabled
      this.check(
        'Scheduler duration checking disabled',
        schedulerFile.includes('EMERGENCY FIX: Disabled duration checking'),
        true
      );
      
      // Check 2: No active duration intervals
      this.check(
        'Duration check interval commented out',
        schedulerFile.includes('// const durationInterval = setInterval(checkStreamDurations') ||
        schedulerFile.includes('// checkStreamDurations()'),
        true
      );
      
    } catch (error) {
      this.results.warnings.push(`Cannot read schedulerService.js: ${error.message}`);
      this.log(`⚠️  Cannot verify schedulerService.js: ${error.message}`, 'WARNING');
    }
  }

  async verifyDatabaseConfiguration() {
    this.log('Verifying database configuration...', 'INFO');
    
    try {
      const { db } = require('./db/database');
      
      // Check for streams with duration limits
      const streamsWithDuration = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, duration 
          FROM streams 
          WHERE duration IS NOT NULL AND duration > 0
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
      
      this.check(
        'No streams with duration limits in database',
        streamsWithDuration.length === 0,
        true
      );
      
      if (streamsWithDuration.length > 0) {
        this.log(`Found ${streamsWithDuration.length} streams with duration limits:`, 'WARNING');
        streamsWithDuration.forEach(stream => {
          this.log(`  - "${stream.title}": ${stream.duration} minutes`, 'WARNING');
        });
      }
      
    } catch (error) {
      this.results.warnings.push(`Cannot verify database: ${error.message}`);
      this.log(`⚠️  Cannot verify database: ${error.message}`, 'WARNING');
    }
  }

  async verifyEnvironmentConfiguration() {
    this.log('Verifying environment configuration...', 'INFO');
    
    // Check for FFmpeg timeout environment variables
    const ffmpegTimeout = process.env.FFMPEG_TIMEOUT;
    if (ffmpegTimeout) {
      const timeoutMs = parseInt(ffmpegTimeout);
      this.check(
        'FFmpeg timeout not set to 1 hour (3600000ms)',
        timeoutMs !== 3600000,
        false
      );
      
      if (timeoutMs === 3600000) {
        this.log(`FFmpeg timeout is set to 1 hour: ${ffmpegTimeout}ms`, 'WARNING');
      }
    }
    
    // Check Node.js environment
    this.check(
      'Node.js environment configured',
      process.env.NODE_ENV !== undefined,
      false
    );
  }

  async verifyApplicationStatus() {
    this.log('Verifying application is running with fixes...', 'INFO');
    
    return new Promise((resolve) => {
      const { spawn } = require('child_process');
      const checkProcess = spawn('netstat', ['-an'], { shell: true });
      
      let output = '';
      checkProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      checkProcess.on('close', (code) => {
        const isRunning = output.includes(':3000');
        
        this.check(
          'StreamOnPod application is running on port 3000',
          isRunning,
          true
        );
        
        resolve();
      });
      
      checkProcess.on('error', () => {
        this.results.criticalIssues.push('Cannot check application status');
        resolve();
      });
    });
  }

  generateComprehensiveReport() {
    const successRate = this.results.totalChecks > 0 ? 
      Math.round((this.results.passedChecks / this.results.totalChecks) * 100) : 0;
    
    this.log('\n📋 COMPREHENSIVE TERMINATION FIX VERIFICATION REPORT', 'REPORT');
    this.log('====================================================', 'REPORT');
    this.log(`Total Checks: ${this.results.totalChecks}`, 'REPORT');
    this.log(`Passed Checks: ${this.results.passedChecks}`, 'REPORT');
    this.log(`Success Rate: ${successRate}%`, 'REPORT');
    
    if (this.results.fixesVerified.length > 0) {
      this.log('\n✅ FIXES VERIFIED:', 'SUCCESS');
      this.results.fixesVerified.forEach((fix, index) => {
        this.log(`${index + 1}. ${fix}`, 'SUCCESS');
      });
    }
    
    if (this.results.criticalIssues.length > 0) {
      this.log('\n🚨 CRITICAL ISSUES:', 'CRITICAL');
      this.results.criticalIssues.forEach((issue, index) => {
        this.log(`${index + 1}. ${issue}`, 'CRITICAL');
      });
    }
    
    if (this.results.warnings.length > 0) {
      this.log('\n⚠️  WARNINGS:', 'WARNING');
      this.results.warnings.forEach((warning, index) => {
        this.log(`${index + 1}. ${warning}`, 'WARNING');
      });
    }
    
    // Final assessment
    if (this.results.criticalIssues.length === 0 && successRate >= 90) {
      this.log('\n🎉 ASSESSMENT: ALL TERMINATION MECHANISMS DISABLED', 'SUCCESS');
      this.log('The 1-hour stream termination bug should now be completely resolved!', 'SUCCESS');
      
      this.log('\n🔄 REQUIRED ACTION: RESTART APPLICATION', 'INFO');
      this.log('The application must be restarted for the NEW fixes to take effect:', 'INFO');
      this.log('1. Stop current application (Ctrl+C or kill process)', 'INFO');
      this.log('2. Run: npm run dev', 'INFO');
      this.log('3. Look for EMERGENCY messages in console output', 'INFO');
      this.log('4. Start a test stream and monitor for 90+ minutes', 'INFO');
      
    } else if (this.results.criticalIssues.length > 0) {
      this.log('\n❌ ASSESSMENT: CRITICAL ISSUES FOUND', 'ERROR');
      this.log('The 1-hour termination bug may still occur until these issues are resolved.', 'ERROR');
      
    } else {
      this.log('\n✅ ASSESSMENT: MOSTLY READY', 'INFO');
      this.log('Minor issues found but should not prevent 24/7 streaming.', 'INFO');
    }
    
    this.log('\n📊 TESTING PROTOCOL:', 'INFO');
    this.log('1. Restart application with new fixes', 'INFO');
    this.log('2. Start a test stream immediately', 'INFO');
    this.log('3. Monitor at these critical time points:', 'INFO');
    this.log('   - 30 minutes (when long-duration health check would start)', 'INFO');
    this.log('   - 60 minutes (when termination previously occurred)', 'INFO');
    this.log('   - 90 minutes (to confirm fix is working)', 'INFO');
    this.log('4. Check logs for any EMERGENCY messages during startup', 'INFO');
    this.log('5. Verify no termination events in logs/real-time-stream-monitor.log', 'INFO');
  }

  async run() {
    try {
      this.log('Starting comprehensive termination fix verification...', 'START');
      
      await this.verifyStreamingServiceFixes();
      await this.verifySchedulerServiceFixes();
      await this.verifyDatabaseConfiguration();
      await this.verifyEnvironmentConfiguration();
      await this.verifyApplicationStatus();
      
      this.generateComprehensiveReport();
      
    } catch (error) {
      this.log(`Verification failed: ${error.message}`, 'ERROR');
      process.exit(1);
    }
  }
}

// Run the comprehensive verification
const verifier = new TerminationFixVerifier();
verifier.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal verification error:', error);
  process.exit(1);
});
