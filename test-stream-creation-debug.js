#!/usr/bin/env node

/**
 * Debug script to test stream creation functionality
 * Helps identify why the enhanced stream modal is not working
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 Debugging Stream Creation Issues\n');

// Test 1: Verify enhanced stream modal script is loaded
console.log('1. Checking enhanced stream modal script loading...');

const layoutPath = path.join(__dirname, 'views', 'layout.ejs');
if (fs.existsSync(layoutPath)) {
  const layoutContent = fs.readFileSync(layoutPath, 'utf8');
  
  const hasEnhancedScript = layoutContent.includes('enhanced-stream-modal.js');
  const hasStreamModalScript = layoutContent.includes('stream-modal.min.js');
  const hasNotificationsScript = layoutContent.includes('notifications.js');
  
  console.log(`   ✅ Layout file exists: ${layoutPath}`);
  console.log(`   ${hasEnhancedScript ? '✅' : '❌'} Enhanced stream modal script included`);
  console.log(`   ${hasStreamModalScript ? '✅' : '❌'} Stream modal script included`);
  console.log(`   ${hasNotificationsScript ? '✅' : '❌'} Notifications script included`);
  
  // Check script loading order
  const enhancedIndex = layoutContent.indexOf('enhanced-stream-modal.js');
  const streamModalIndex = layoutContent.indexOf('stream-modal.min.js');
  const notificationsIndex = layoutContent.indexOf('notifications.js');
  
  if (enhancedIndex > 0 && streamModalIndex > 0) {
    if (streamModalIndex < enhancedIndex) {
      console.log('   ✅ Script loading order: stream-modal.min.js → enhanced-stream-modal.js');
    } else {
      console.log('   ⚠️  Script loading order issue: enhanced script loads before base script');
    }
  }
} else {
  console.log(`   ❌ Layout file not found: ${layoutPath}`);
}

// Test 2: Verify enhanced stream modal file exists and has correct structure
console.log('\n2. Checking enhanced stream modal file structure...');

const enhancedModalPath = path.join(__dirname, 'public', 'js', 'enhanced-stream-modal.js');
if (fs.existsSync(enhancedModalPath)) {
  const modalContent = fs.readFileSync(enhancedModalPath, 'utf8');
  
  // Check for key components
  const hasStreamCreationHandler = modalContent.includes('class StreamCreationHandler');
  const hasEventListener = modalContent.includes('document.addEventListener(\'DOMContentLoaded\'');
  const hasFormHandler = modalContent.includes('streamForm.addEventListener(\'submit\'');
  const hasNotificationSystem = modalContent.includes('class NotificationSystem');
  const hasUIConfig = modalContent.includes('const UI_CONFIG');
  
  console.log(`   ✅ Enhanced modal file exists: ${enhancedModalPath}`);
  console.log(`   ${hasStreamCreationHandler ? '✅' : '❌'} StreamCreationHandler class present`);
  console.log(`   ${hasEventListener ? '✅' : '❌'} DOMContentLoaded event listener present`);
  console.log(`   ${hasFormHandler ? '✅' : '❌'} Form submit handler present`);
  console.log(`   ${hasNotificationSystem ? '✅' : '❌'} NotificationSystem class present`);
  console.log(`   ${hasUIConfig ? '✅' : '❌'} UI_CONFIG constants present`);
  
  // Check for potential syntax errors
  try {
    // Basic syntax check - try to parse as JavaScript
    const vm = require('vm');
    const context = {
      document: { addEventListener: () => {}, getElementById: () => null, querySelector: () => null },
      window: {},
      console: { error: () => {} },
      setTimeout: () => {},
      clearTimeout: () => {}
    };
    vm.createContext(context);
    vm.runInContext(modalContent, context);
    console.log('   ✅ JavaScript syntax appears valid');
  } catch (syntaxError) {
    console.log(`   ❌ JavaScript syntax error: ${syntaxError.message}`);
  }
} else {
  console.log(`   ❌ Enhanced modal file not found: ${enhancedModalPath}`);
}

// Test 3: Check global variable dependencies
console.log('\n3. Checking global variable dependencies...');

const streamModalPath = path.join(__dirname, 'public', 'js', 'stream-modal.js');
if (fs.existsSync(streamModalPath)) {
  const streamModalContent = fs.readFileSync(streamModalPath, 'utf8');
  
  const hasIsStreamKeyValid = streamModalContent.includes('window.isStreamKeyValid');
  const hasCurrentOrientation = streamModalContent.includes('window.currentOrientation');
  const hasGlobalVariables = hasIsStreamKeyValid && hasCurrentOrientation;
  
  console.log(`   ✅ Stream modal file exists: ${streamModalPath}`);
  console.log(`   ${hasIsStreamKeyValid ? '✅' : '❌'} window.isStreamKeyValid defined`);
  console.log(`   ${hasCurrentOrientation ? '✅' : '❌'} window.currentOrientation defined`);
  console.log(`   ${hasGlobalVariables ? '✅' : '❌'} All required global variables present`);
} else {
  console.log(`   ❌ Stream modal file not found: ${streamModalPath}`);
}

// Test 4: Check dashboard form structure
console.log('\n4. Checking dashboard form structure...');

const dashboardPath = path.join(__dirname, 'views', 'dashboard.ejs');
if (fs.existsSync(dashboardPath)) {
  const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
  
  const hasNewStreamForm = dashboardContent.includes('id="newStreamForm"');
  const hasSubmitButton = dashboardContent.includes('type="submit"') && dashboardContent.includes('form="newStreamForm"');
  const hasOldHandler = dashboardContent.includes('document.getElementById(\'newStreamForm\').addEventListener');
  const hasRequiredFields = dashboardContent.includes('id="streamTitle"') && 
                           dashboardContent.includes('id="selectedVideoId"') &&
                           dashboardContent.includes('id="rtmpUrl"') &&
                           dashboardContent.includes('id="streamKey"');
  
  console.log(`   ✅ Dashboard file exists: ${dashboardPath}`);
  console.log(`   ${hasNewStreamForm ? '✅' : '❌'} newStreamForm element present`);
  console.log(`   ${hasSubmitButton ? '✅' : '❌'} Submit button with form attribute present`);
  console.log(`   ${!hasOldHandler ? '✅' : '❌'} Old form handler removed`);
  console.log(`   ${hasRequiredFields ? '✅' : '❌'} Required form fields present`);
} else {
  console.log(`   ❌ Dashboard file not found: ${dashboardPath}`);
}

// Test 5: Check for potential conflicts
console.log('\n5. Checking for potential conflicts...');

// Check if there are multiple form handlers
let conflictCount = 0;
const filesToCheck = [
  { path: dashboardPath, name: 'dashboard.ejs' },
  { path: enhancedModalPath, name: 'enhanced-stream-modal.js' },
  { path: streamModalPath, name: 'stream-modal.js' }
];

filesToCheck.forEach(file => {
  if (fs.existsSync(file.path)) {
    const content = fs.readFileSync(file.path, 'utf8');
    const hasFormHandler = content.includes('newStreamForm') && 
                          (content.includes('addEventListener') || content.includes('submit'));
    if (hasFormHandler) {
      console.log(`   📝 Form handler found in: ${file.name}`);
      conflictCount++;
    }
  }
});

if (conflictCount === 1) {
  console.log('   ✅ Single form handler found (no conflicts)');
} else if (conflictCount > 1) {
  console.log(`   ⚠️  Multiple form handlers found (${conflictCount}) - potential conflict`);
} else {
  console.log('   ❌ No form handlers found');
}

console.log('\n📊 Summary of Potential Issues:');
console.log('   1. Script Loading: Enhanced modal script must load after base modal script');
console.log('   2. Global Variables: window.isStreamKeyValid and window.currentOrientation must be available');
console.log('   3. Form Structure: newStreamForm must exist with proper submit button');
console.log('   4. Event Handler: Only one form handler should be attached');
console.log('   5. Dependencies: NotificationSystem and UI_CONFIG must be properly initialized');

console.log('\n🔧 Debugging Steps to Try:');
console.log('   1. Open browser developer console and check for JavaScript errors');
console.log('   2. Verify that enhanced-stream-modal.js is loaded (check Network tab)');
console.log('   3. Check if StreamCreationHandler is properly instantiated');
console.log('   4. Verify form submission event is being captured');
console.log('   5. Test form validation by submitting with empty fields');

console.log('\n✅ Debug analysis complete!');
console.log('   If issues persist, check browser console for runtime errors.');
