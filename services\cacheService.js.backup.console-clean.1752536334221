class CacheService {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    
    // Upload optimization flag
    this.uploadMode = false;
    
    // Fallback in-memory cache
    this.memoryCache = new Map();
    this.ttlMap = new Map();
    this.defaultTTL = parseInt(process.env.CACHE_TTL) || 3600;
    this.maxSize = 1000; // Max items in memory cache

    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      memoryHits: 0,
      errors: 0
    };

    this.init();
  }

  async init() {
    if (!this.isProduction) {
      console.log('💾 [Cache] Using in-memory cache only');
    }

    // Start cleanup interval for memory cache
    this.startCleanupInterval();
  }

  // Set cache with TTL
  async set(key, value, ttl = this.defaultTTL) {
    try {
      this.stats.sets++;

      // Use memory cache
      this.memoryCache.set(key, value);
      if (ttl > 0) {
        this.ttlMap.set(key, Date.now() + (ttl * 1000));
      }

      return true;
      if (this.memoryCache.size > this.maxSize) {
        this.evictOldest();
      }

      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [Cache] Set error:', error.message);
      return false;
    }
  }

  // Enable upload optimization mode
  enableUploadMode() {
    this.uploadMode = true;
    if (!this.isProduction) {
      console.log('🚀 [Cache] Upload optimization mode enabled');
    }
  }

  // Disable upload optimization mode
  disableUploadMode() {
    this.uploadMode = false;
    if (!this.isProduction) {
      console.log('💾 [Cache] Normal cache mode restored');
    }
  }

  // Get from cache
  async get(key) {
    try {
      if (this.memoryCache.has(key)) {
        const expiry = this.ttlMap.get(key);
        if (expiry && Date.now() > expiry) {
          this.memoryCache.delete(key);
          this.ttlMap.delete(key);
          this.stats.misses++;
          return null;
        }
        this.stats.hits++;
        this.stats.memoryHits++;
        return this.memoryCache.get(key);
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [Cache] Get error:', error.message);
      return null;
    }
  }

  // Delete cache key
  async delete(key) {
    try {
      this.stats.deletes++;

      // Delete from memory cache
      this.memoryCache.delete(key);
      this.ttlMap.delete(key);

      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [Cache] Delete error:', error.message);
      return false;
    }
  }

  // Check if key is expired
  isExpired(key) {
    const expiry = this.ttlMap.get(key);
    return expiry && Date.now() > expiry;
  }

  // Clear all cache
  async clear() {
    try {

      // Clear memory cache
      this.memoryCache.clear();
      this.ttlMap.clear();

      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [Cache] Clear error:', error.message);
      return false;
    }
  }

  // Evict oldest entry from memory cache
  evictOldest() {
    if (this.memoryCache.size === 0) return;

    const firstKey = this.memoryCache.keys().next().value;
    this.memoryCache.delete(firstKey);
    this.ttlMap.delete(firstKey);
    this.stats.evictions++;
  }

  // Start cleanup interval for memory cache
  startCleanupInterval() {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  }

  // Clean up expired entries from memory cache
  cleanupExpired() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, expiry] of this.ttlMap.entries()) {
      if (expiry && now > expiry) {
        this.memoryCache.delete(key);
        this.ttlMap.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0 && !this.isProduction) {
      console.log(`🧹 [Cache] Cleaned ${cleaned} expired entries`);
    }
  }

  // Get cache statistics
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? ((this.stats.hits / totalRequests) * 100).toFixed(2) : 0;

    return {
      ...this.stats,
      size: this.memoryCache.size,
      maxSize: this.maxSize,
      hitRate: `${hitRate}%`,
      memoryUsage: this.getMemoryUsage(),
      status: 'memory'
    };
  }

  // Estimate memory usage
  getMemoryUsage() {
    let totalSize = 0;
    for (const [key, value] of this.memoryCache.entries()) {
      totalSize += this.estimateSize(key) + this.estimateSize(value);
    }
    return totalSize;
  }

  // Estimate object size in bytes
  estimateSize(obj) {
    if (obj === null || obj === undefined) return 0;
    if (typeof obj === 'string') return obj.length * 2; // UTF-16
    if (typeof obj === 'number') return 8;
    if (typeof obj === 'boolean') return 4;
    if (typeof obj === 'object') {
      return JSON.stringify(obj).length * 2;
    }
    return 0;
  }

  // Cache wrapper for database queries
  async cacheQuery(key, queryFunction, ttl = this.defaultTTL) {
    try {
      // Try to get from cache first
      const cached = await this.get(key);
      if (cached !== null) {
        return cached;
      }

      // Execute query and cache result
      const result = await queryFunction();
      if (result !== null && result !== undefined) {
        await this.set(key, result, ttl);
      }

      return result;
    } catch (error) {
      console.error('[Cache] Error in cacheQuery:', error);
      throw error;
    }
  }

  // Invalidate cache by pattern
  async invalidatePattern(pattern) {
    try {
      let deletedCount = 0;

      // Invalidate from memory cache
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
          this.ttlMap.delete(key);
          deletedCount++;
        }
      }

      return deletedCount;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [Cache] Pattern invalidation error:', error.message);
      return 0;
    }
  }

  // Preload common data
  async preloadCommonData() {
    try {
      if (!this.isProduction) {
        console.log('🔄 [Cache] Preloading common data...');
      }

      // Preload subscription plans
      const Subscription = require('../models/Subscription');
      try {
        const plans = await Subscription.getAllPlans();
        await this.set(this.constructor.keys.subscriptionPlans(), plans, 3600); // 1 hour
      } catch (error) {
        console.error('❌ [Cache] Error preloading subscription plans:', error.message);
      }

      // Preload system stats
      try {
        const systemMonitor = require('./systemMonitor');
        const stats = await systemMonitor.getSystemStats();
        await this.set(this.constructor.keys.systemStats(), stats, 60); // 1 minute
      } catch (error) {
        console.error('❌ [Cache] Error preloading system stats:', error.message);
      }

      if (!this.isProduction) {
        console.log('✅ [Cache] Common data preloaded');
      }
    } catch (error) {
      console.error('❌ [Cache] Error in preloadCommonData:', error.message);
    }
  }

  // Cache keys for different data types
  static keys = {
    user: (id) => `user:${id}`,
    userStats: (id) => `user:stats:${id}`,
    userStreams: (id) => `user:streams:${id}`,
    userVideos: (id) => `user:videos:${id}`,
    stream: (id) => `stream:${id}`,
    streamWithVideo: (id) => `stream:video:${id}`,
    video: (id) => `video:${id}`,
    systemStats: () => 'system:stats',
    adminStats: () => 'admin:stats',
    subscriptionPlans: () => 'subscription:plans',
    loadBalancerStatus: () => 'loadbalancer:status',
    loadBalancerMetrics: () => 'loadbalancer:metrics'
  };

  // Invalidate user-related cache
  async invalidateUser(userId) {
    try {
      const patterns = [
        this.constructor.keys.user(userId),
        this.constructor.keys.userStats(userId),
        this.constructor.keys.userStreams(userId),
        this.constructor.keys.userVideos(userId)
      ];

      let totalDeleted = 0;
      for (const pattern of patterns) {
        await this.delete(pattern);
        totalDeleted++;
      }

      if (!this.isProduction) {
        console.log(`🗑️ [Cache] Invalidated ${totalDeleted} user cache entries for user ${userId}`);
      }
    } catch (error) {
      console.error('❌ [Cache] Error invalidating user cache:', error.message);
    }
  }

  // Invalidate stream-related cache
  async invalidateStream(streamId, userId = null) {
    try {
      const patterns = [
        this.constructor.keys.stream(streamId),
        this.constructor.keys.streamWithVideo(streamId)
      ];

      if (userId) {
        patterns.push(this.constructor.keys.userStreams(userId));
      }

      let totalDeleted = 0;
      for (const pattern of patterns) {
        await this.delete(pattern);
        totalDeleted++;
      }

      if (!this.isProduction) {
        console.log(`🗑️ [Cache] Invalidated ${totalDeleted} stream cache entries for stream ${streamId}`);
      }
    } catch (error) {
      console.error('❌ [Cache] Error invalidating stream cache:', error.message);
    }
  }

  // Invalidate video-related cache
  async invalidateVideo(videoId, userId = null) {
    try {
      const patterns = [
        this.constructor.keys.video(videoId)
      ];

      if (userId) {
        patterns.push(this.constructor.keys.userVideos(userId));
      }

      let totalDeleted = 0;
      for (const pattern of patterns) {
        await this.delete(pattern);
        totalDeleted++;
      }

      if (!this.isProduction) {
        console.log(`🗑️ [Cache] Invalidated ${totalDeleted} video cache entries for video ${videoId}`);
      }
    } catch (error) {
      console.error('❌ [Cache] Error invalidating video cache:', error.message);
    }
  }

  // Invalidate system cache
  async invalidateSystem() {
    try {
      const patterns = [
        this.constructor.keys.systemStats(),
        this.constructor.keys.adminStats(),
        this.constructor.keys.loadBalancerStatus(),
        this.constructor.keys.loadBalancerMetrics()
      ];

      let totalDeleted = 0;
      for (const pattern of patterns) {
        await this.delete(pattern);
        totalDeleted++;
      }

      if (!this.isProduction) {
        console.log(`🗑️ [Cache] Invalidated ${totalDeleted} system cache entries`);
      }
    } catch (error) {
      console.error('❌ [Cache] Error invalidating system cache:', error.message);
    }
  }

  // Health check for cache service
  async healthCheck() {
    try {
      const stats = this.getStats();

      return {
        status: 'healthy',
        cache: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
