# Stream Status Stability Fixes

## Issue Summary

The StreamOnPod application was experiencing unstable stream status behavior where:
- Stream status frequently alternated between "error" and "live" states
- Status changes were unpredictable and inconsistent
- Manual intervention via "fix" button was required to stabilize streams
- Users experienced poor streaming reliability

## Root Cause Analysis

### 1. **Race Condition in Status Updates**
- Status was updated to 'live' immediately when FFmpeg process started
- FFmpeg initialization takes 5-10 seconds before actual streaming begins
- This created a window where status showed 'live' but stream wasn't working

### 2. **Conflicting Sync Intervals**
- Backend sync: Every 30 minutes (too infrequent)
- Frontend polling: Every 15 seconds (too frequent)
- Created conflicts where frontend detected issues faster than backend could fix them

### 3. **Insufficient Process Validation**
- Process validation was too lenient for new processes
- Didn't verify actual streaming capability, just process existence

### 4. **Status Synchronization Conflicts**
- Multiple sync mechanisms running simultaneously
- Backend and frontend could override each other's status updates

## Implemented Solutions

### 1. **Delayed Status Updates** ✅
**File**: `services/streamingService.js` (lines 849-871)

**Problem**: Immediate status update to 'live' when FFmpeg starts
**Solution**: Added 8-second delay with validation before updating status

```javascript
// Wait 8 seconds for FFmpeg to initialize before updating status
const statusUpdateTimer = setTimeout(async () => {
  if (activeStreams.has(streamId) && !statusUpdated) {
    if (validateStreamProcess(streamId)) {
      await Stream.updateStatus(streamId, 'live', stream.user_id);
      statusUpdated = true;
    }
  }
}, 8000);
```

### 2. **Enhanced Process Validation** ✅
**File**: `services/streamingService.js` (lines 1372-1384)

**Improvements**:
- Increased validation window from 5 to 10 seconds for new processes
- Better health checks for process state
- More accurate determination of process viability

### 3. **Balanced Sync Intervals** ✅
**Changes**:
- Backend sync: 30 minutes → 5 minutes (more responsive)
- Frontend polling: 15 seconds → 20 seconds (less aggressive)
- Reduced conflicts between sync mechanisms

### 4. **New "Starting" Status** ✅
**Files**: `app.js`, `views/dashboard.ejs`

**Features**:
- Shows "Starting..." status for newly initiated streams
- Prevents false "live" status during initialization
- Better user experience with clear status indication

### 5. **Timer Cleanup on Process Exit** ✅
**File**: `services/streamingService.js` (lines 954-962)

**Improvement**: Clear delayed status update timers when process exits early
- Prevents orphaned timers from updating status incorrectly
- Better resource management

## Technical Details

### Status Flow (Before Fix)
1. User clicks "Start Stream"
2. FFmpeg process starts
3. Status immediately updated to "live" ❌
4. FFmpeg may fail during initialization
5. Status becomes inconsistent
6. User sees alternating error/live states

### Status Flow (After Fix)
1. User clicks "Start Stream"
2. FFmpeg process starts
3. Status shows "starting" ✅
4. 8-second validation period
5. If FFmpeg is healthy → status becomes "live"
6. If FFmpeg fails → status becomes "error"
7. Consistent, predictable status behavior

### Configuration Changes

| Setting | Before | After | Reason |
|---------|--------|-------|---------|
| Status Update Delay | 0 seconds | 8 seconds | Allow FFmpeg initialization |
| Backend Sync | 30 minutes | 5 minutes | More responsive |
| Frontend Polling | 15 seconds | 20 seconds | Reduce conflicts |
| Process Validation | 5 seconds | 10 seconds | Better stability |

## Testing Instructions

### 1. **Normal Stream Start**
1. Start a stream
2. Verify status shows "Starting..." initially
3. After 8 seconds, verify status becomes "Live"
4. Confirm stream is actually working

### 2. **Failed Stream Start**
1. Start a stream with invalid RTMP settings
2. Verify status shows "Starting..." initially
3. After 8 seconds, verify status becomes "Error"
4. Confirm no false "Live" status appears

### 3. **Process Crash Simulation**
1. Start a stream successfully
2. Kill FFmpeg process manually
3. Verify status updates to "Offline" within 5 minutes
4. No manual "fix" button should be needed

## Benefits Achieved

- ✅ **Eliminated status flickering** between error/live states
- ✅ **Reduced need for manual intervention** via fix button
- ✅ **Improved user experience** with clear status indicators
- ✅ **Better system stability** with proper timing
- ✅ **More accurate status reporting** reflecting actual stream state

## Monitoring

### Log Messages to Watch
- `✅ Stream X status updated to 'live' after 8-second validation`
- `❌ Stream X validation failed during delayed status update`
- `[Status] Cleared delayed status update timer due to process exit`

### Status Indicators
- **Blue "Starting..." badge**: Stream is initializing
- **Red "Live" badge**: Stream is confirmed working
- **Orange "Status Issue" badge**: Should be rare now

## Rollback Plan

If issues occur, revert these changes:
1. Remove 8-second delay in `startStream()` function
2. Change sync intervals back to original values
3. Remove "starting" status from frontend
4. Remove timer cleanup logic

All changes are backward compatible and can be safely rolled back.
