#!/usr/bin/env node

/**
 * Final Fix Verification
 * Demonstrates that PodLite users can now start their created streams
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const { v4: uuidv4 } = require('uuid');

async function verifyFix() {
  try {
    console.log('🎯 StreamOnPod: Final Fix Verification');
    console.log('='.repeat(60));
    console.log('Demonstrating the complete fix for PodLite stream starting issue\n');

    // Use our test user
    const testUser = await new Promise((resolve, reject) => {
      db.get('SELECT id, username FROM users WHERE username = ?', ['podlite_test_user'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!testUser) {
      console.log('❌ Test user not found. Run create-test-user.js first.');
      process.exit(1);
    }

    console.log(`👤 Testing with: ${testUser.username} (PodLite plan - 1 streaming slot)`);

    // Clean up
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [testUser.id], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    // SCENARIO: Complete user workflow
    console.log('\n📋 COMPLETE USER WORKFLOW TEST');
    console.log('='.repeat(60));

    // Step 1: User creates a stream
    console.log('\n1️⃣ User creates a stream');
    console.log('-'.repeat(30));

    const streamId = uuidv4();
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [streamId, testUser.id, 'My Podcast Stream', 'offline', 'rtmp://youtube.com/live', 'my_stream_key'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`✅ Stream created successfully`);
    console.log(`   Stream ID: ${streamId}`);
    console.log(`   Status: offline`);
    console.log(`   User has: 1 offline stream, 0 live streams`);

    // Step 2: User tries to start the stream (this was failing before)
    console.log('\n2️⃣ User clicks "Start Stream" (this was failing before the fix)');
    console.log('-'.repeat(30));

    // Test the OLD logic (what was causing the issue)
    const oldQuotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`❌ OLD LOGIC: hasLimit=${oldQuotaCheck.hasLimit} (${oldQuotaCheck.currentSlots}/${oldQuotaCheck.maxSlots})`);
    console.log(`   This counts ALL streams including offline ones`);
    console.log(`   Result: User would get "Streaming Limit Reached" error`);

    // Test the NEW logic (the fix)
    const newQuotaCheck = await Subscription.checkStreamingSlotLimitForStarting(testUser.id, streamId);
    console.log(`✅ NEW LOGIC: hasLimit=${newQuotaCheck.hasLimit} (${newQuotaCheck.currentSlots}/${newQuotaCheck.maxSlots})`);
    console.log(`   This counts only LIVE streams`);
    console.log(`   Result: User can start their stream!`);

    // Step 3: Simulate successful stream start
    console.log('\n3️⃣ Stream starts successfully');
    console.log('-'.repeat(30));

    // Update stream to live status
    await new Promise((resolve, reject) => {
      db.run('UPDATE streams SET status = ? WHERE id = ?', ['live', streamId], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log(`✅ Stream is now live`);
    console.log(`   User has: 1 live stream`);

    // Step 4: Test that limits still work correctly
    console.log('\n4️⃣ User tries to start another stream (should be blocked)');
    console.log('-'.repeat(30));

    const secondStreamId = uuidv4();
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [secondStreamId, testUser.id, 'Second Stream', 'offline', 'rtmp://youtube.com/live2', 'key2'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    const limitCheck = await Subscription.checkStreamingSlotLimitForStarting(testUser.id, secondStreamId);
    console.log(`🚫 LIMIT CHECK: hasLimit=${limitCheck.hasLimit} (${limitCheck.currentSlots}/${limitCheck.maxSlots})`);
    
    if (limitCheck.hasLimit) {
      console.log(`✅ Correctly blocked: User already has 1 live stream (at limit)`);
    } else {
      console.log(`❌ ERROR: Should be blocked but isn't`);
    }

    // Clean up
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [testUser.id], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    // SUMMARY
    console.log('\n📊 FIX VERIFICATION SUMMARY');
    console.log('='.repeat(60));

    const fixWorking = !newQuotaCheck.hasLimit && limitCheck.hasLimit;

    if (fixWorking) {
      console.log('🎉 FIX VERIFIED SUCCESSFUL!');
      console.log('');
      console.log('✅ BEFORE FIX:');
      console.log('   - User creates stream (offline)');
      console.log('   - User clicks "Start Stream"');
      console.log('   - Gets "Streaming Limit Reached" error');
      console.log('   - Cannot use their PodLite plan');
      console.log('');
      console.log('✅ AFTER FIX:');
      console.log('   - User creates stream (offline)');
      console.log('   - User clicks "Start Stream"');
      console.log('   - Stream starts successfully');
      console.log('   - User can use their full PodLite plan (1 concurrent stream)');
      console.log('');
      console.log('🔧 TECHNICAL CHANGES:');
      console.log('   - Added checkStreamingSlotLimitForStarting() method');
      console.log('   - Counts only LIVE streams for starting validation');
      console.log('   - Added checkStreamingQuotaForStarting() middleware');
      console.log('   - Updated /api/streams/:id/status endpoint');
      console.log('');
      console.log('🛡️ SECURITY MAINTAINED:');
      console.log('   - Users still cannot exceed their plan limits');
      console.log('   - Multiple live streams still blocked correctly');
      console.log('   - Stream creation logic unchanged');
    } else {
      console.log('❌ FIX VERIFICATION FAILED');
      console.log('   The fix may need additional adjustments');
    }

    console.log('\n' + '='.repeat(60));

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Verification interrupted by user');
  process.exit(1);
});

// Run the verification
verifyFix().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
