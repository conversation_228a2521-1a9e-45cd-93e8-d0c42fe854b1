/**
 * Test script to verify subscription validation for stream operations
 * This script tests the new middleware added to stream starting endpoints
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_CONFIG = {
  // Test user credentials (you may need to adjust these)
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  // Test stream data
  testStream: {
    streamTitle: 'Test Stream for Validation',
    rtmpUrl: 'rtmp://test.example.com/live',
    streamKey: 'test-stream-key-123',
    videoId: null // Will be set if needed
  }
};

class StreamValidationTester {
  constructor() {
    this.sessionCookie = null;
    this.testStreamId = null;
  }

  async login() {
    try {
      console.log('🔐 Attempting to login...');
      const response = await axios.post(`${BASE_URL}/login`, {
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password
      }, {
        withCredentials: true,
        validateStatus: () => true // Accept all status codes
      });

      if (response.status === 200 && response.headers['set-cookie']) {
        this.sessionCookie = response.headers['set-cookie'];
        console.log('✅ Login successful');
        return true;
      } else {
        console.log('❌ Login failed:', response.status, response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ Login error:', error.message);
      return false;
    }
  }

  async createTestStream() {
    try {
      console.log('📝 Creating test stream...');
      const response = await axios.post(`${BASE_URL}/api/streams`, TEST_CONFIG.testStream, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || '',
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        validateStatus: () => true
      });

      if (response.status === 200 && response.data.success) {
        this.testStreamId = response.data.stream.id;
        console.log('✅ Test stream created:', this.testStreamId);
        return true;
      } else {
        console.log('❌ Stream creation failed:', response.status, response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ Stream creation error:', error.message);
      return false;
    }
  }

  async testStreamStartValidation() {
    if (!this.testStreamId) {
      console.log('❌ No test stream available for validation test');
      return false;
    }

    try {
      console.log('🧪 Testing stream start validation...');
      const response = await axios.post(`${BASE_URL}/api/streams/${this.testStreamId}/status`, {
        status: 'live'
      }, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || '',
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        validateStatus: () => true
      });

      console.log('📊 Stream start validation result:');
      console.log('   Status:', response.status);
      console.log('   Response:', JSON.stringify(response.data, null, 2));

      // Check if validation is working
      if (response.status === 403) {
        const errorData = response.data;
        if (errorData.error && (
          errorData.error.includes('subscription') || 
          errorData.error.includes('expired') ||
          errorData.error.includes('Preview plan') ||
          errorData.error.includes('Streaming not allowed')
        )) {
          console.log('✅ Subscription validation is working correctly');
          return true;
        }
      } else if (response.status === 200) {
        console.log('⚠️  Stream started successfully - validation may not be working or user has valid subscription');
        return true;
      }

      console.log('❓ Unexpected response - validation status unclear');
      return false;
    } catch (error) {
      console.error('❌ Stream start validation test error:', error.message);
      return false;
    }
  }

  async testStreamUpdateValidation() {
    if (!this.testStreamId) {
      console.log('❌ No test stream available for update validation test');
      return false;
    }

    try {
      console.log('🧪 Testing stream update validation...');
      const response = await axios.put(`${BASE_URL}/api/streams/${this.testStreamId}`, {
        streamTitle: 'Updated Test Stream Title',
        bitrate: 3000 // This should trigger restart if stream is live
      }, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || '',
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        validateStatus: () => true
      });

      console.log('📊 Stream update validation result:');
      console.log('   Status:', response.status);
      console.log('   Response:', JSON.stringify(response.data, null, 2));

      // Check if validation is working
      if (response.status === 403) {
        const errorData = response.data;
        if (errorData.error && (
          errorData.error.includes('subscription') || 
          errorData.error.includes('expired') ||
          errorData.error.includes('Preview plan') ||
          errorData.error.includes('Streaming not allowed')
        )) {
          console.log('✅ Stream update subscription validation is working correctly');
          return true;
        }
      } else if (response.status === 200) {
        console.log('⚠️  Stream updated successfully - validation may not be working or user has valid subscription');
        return true;
      }

      console.log('❓ Unexpected response - validation status unclear');
      return false;
    } catch (error) {
      console.error('❌ Stream update validation test error:', error.message);
      return false;
    }
  }

  async cleanup() {
    if (this.testStreamId) {
      try {
        console.log('🧹 Cleaning up test stream...');
        await axios.delete(`${BASE_URL}/api/streams/${this.testStreamId}`, {
          headers: {
            'Cookie': this.sessionCookie?.join('; ') || ''
          },
          withCredentials: true,
          validateStatus: () => true
        });
        console.log('✅ Test stream cleaned up');
      } catch (error) {
        console.log('⚠️  Cleanup error (non-critical):', error.message);
      }
    }
  }

  async runTests() {
    console.log('🚀 Starting Stream Validation Tests');
    console.log('=====================================');

    // Step 1: Login
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without login');
      return;
    }

    // Step 2: Create test stream (this will test stream creation validation)
    const streamCreated = await this.createTestStream();
    
    // Step 3: Test stream start validation
    await this.testStreamStartValidation();

    // Step 4: Test stream update validation
    await this.testStreamUpdateValidation();

    // Step 5: Cleanup
    await this.cleanup();

    console.log('=====================================');
    console.log('🏁 Stream Validation Tests Complete');
  }
}

// Run the tests
if (require.main === module) {
  const tester = new StreamValidationTester();
  tester.runTests().catch(error => {
    console.error('❌ Test suite error:', error);
    process.exit(1);
  });
}

module.exports = StreamValidationTester;
