#!/usr/bin/env node

/**
 * YouTube Stream Termination Diagnostic Tool
 * Comprehensive analysis of unexpected YouTube stream stops
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 YouTube Stream Termination Diagnostic Tool');
console.log('==============================================\n');

class YouTubeStreamDiagnostic {
  constructor() {
    this.findings = {
      durationSettings: [],
      autoStopMechanisms: [],
      youtubeSpecific: [],
      resourceIssues: [],
      logAnalysis: []
    };
  }

  async runDiagnostic() {
    console.log('🚀 Starting comprehensive YouTube stream termination analysis...\n');

    try {
      // 1. Stream Duration Analysis
      await this.analyzeDurationSettings();
      
      // 2. Auto-Stop Mechanism Investigation
      await this.analyzeAutoStopMechanisms();
      
      // 3. YouTube-Specific Issues
      await this.analyzeYouTubeSpecificIssues();
      
      // 4. Resource Management Analysis
      await this.analyzeResourceManagement();
      
      // 5. Log Analysis
      await this.analyzeLogFiles();
      
      // 6. Generate comprehensive report
      this.generateDiagnosticReport();
      
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      process.exit(1);
    }
  }

  async analyzeDurationSettings() {
    console.log('📊 1. STREAM DURATION ANALYSIS');
    console.log('==============================\n');

    try {
      // Check for duration-based termination settings
      const streamingServicePath = './services/streamingService.js';
      const schedulerServicePath = './services/schedulerService.js';
      
      if (fs.existsSync(streamingServicePath)) {
        const streamingContent = fs.readFileSync(streamingServicePath, 'utf8');
        
        // Check for duration-based termination
        if (streamingContent.includes('stream.duration')) {
          this.findings.durationSettings.push({
            type: 'DURATION_TERMINATION_FOUND',
            location: 'services/streamingService.js',
            details: 'Stream duration-based termination is implemented',
            impact: 'HIGH - Streams will auto-terminate after specified duration'
          });
        }
      }

      if (fs.existsSync(schedulerServicePath)) {
        const schedulerContent = fs.readFileSync(schedulerServicePath, 'utf8');
        
        // Check scheduler termination logic
        if (schedulerContent.includes('scheduleStreamTermination')) {
          this.findings.durationSettings.push({
            type: 'SCHEDULER_TERMINATION_FOUND',
            location: 'services/schedulerService.js',
            details: 'Scheduler-based stream termination is active',
            impact: 'HIGH - Scheduler will terminate streams based on duration settings'
          });
        }
      }

      // Check database for streams with duration settings
      try {
        const { db } = require('./db/database');
        const streamsWithDuration = await new Promise((resolve, reject) => {
          db.all(`
            SELECT id, title, duration, platform, status, created_at
            FROM streams 
            WHERE duration IS NOT NULL AND duration > 0
            ORDER BY created_at DESC
            LIMIT 10
          `, [], (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          });
        });

        if (streamsWithDuration.length > 0) {
          this.findings.durationSettings.push({
            type: 'STREAMS_WITH_DURATION_FOUND',
            location: 'Database',
            details: `Found ${streamsWithDuration.length} streams with duration limits`,
            impact: 'MEDIUM - These streams will auto-terminate',
            data: streamsWithDuration
          });
        }

      } catch (dbError) {
        this.findings.durationSettings.push({
          type: 'DATABASE_CHECK_FAILED',
          location: 'Database',
          details: `Could not check database: ${dbError.message}`,
          impact: 'LOW - Unable to verify duration settings in database'
        });
      }

      console.log(`✅ Duration analysis complete - Found ${this.findings.durationSettings.length} findings\n`);

    } catch (error) {
      console.error('❌ Duration analysis failed:', error);
    }
  }

  async analyzeAutoStopMechanisms() {
    console.log('🛑 2. AUTO-STOP MECHANISM INVESTIGATION');
    console.log('======================================\n');

    try {
      const streamingServicePath = './services/streamingService.js';
      
      if (fs.existsSync(streamingServicePath)) {
        const content = fs.readFileSync(streamingServicePath, 'utf8');
        
        // Check current auto-stop configuration
        const autoStopMatch = content.match(/AUTO_STOP_CONFIG\s*=\s*{([^}]+)}/);
        if (autoStopMatch) {
          const configText = autoStopMatch[1];
          
          // Extract configuration values
          const maxFailures = configText.match(/MAX_CONSECUTIVE_FAILURES:\s*(\d+)/);
          const failureWindow = configText.match(/FAILURE_WINDOW_MINUTES:\s*(\d+)/);
          const ioThreshold = configText.match(/I_O_ERROR_THRESHOLD:\s*(\d+)/);
          const connectionThreshold = configText.match(/CONNECTION_ERROR_THRESHOLD:\s*(\d+)/);
          
          this.findings.autoStopMechanisms.push({
            type: 'AUTO_STOP_CONFIG_FOUND',
            location: 'services/streamingService.js',
            details: 'Auto-stop configuration detected',
            impact: 'HIGH - May cause premature stream termination',
            config: {
              maxConsecutiveFailures: maxFailures ? parseInt(maxFailures[1]) : 'unknown',
              failureWindowMinutes: failureWindow ? parseInt(failureWindow[1]) : 'unknown',
              ioErrorThreshold: ioThreshold ? parseInt(ioThreshold[1]) : 'unknown',
              connectionErrorThreshold: connectionThreshold ? parseInt(connectionThreshold[1]) : 'unknown'
            }
          });
        }

        // Check for auto-stop decision logic
        if (content.includes('shouldAutoStopStream')) {
          this.findings.autoStopMechanisms.push({
            type: 'AUTO_STOP_LOGIC_FOUND',
            location: 'services/streamingService.js',
            details: 'Auto-stop decision logic is implemented',
            impact: 'HIGH - Streams may be automatically stopped based on error patterns'
          });
        }

        // Check for stream age protection
        if (content.includes('streamAge') && content.includes('2 * 60 * 1000')) {
          this.findings.autoStopMechanisms.push({
            type: 'STREAM_AGE_PROTECTION_FOUND',
            location: 'services/streamingService.js',
            details: 'Stream age protection prevents auto-stop for streams younger than 2 minutes',
            impact: 'POSITIVE - Reduces false positives for new streams'
          });
        }
      }

      console.log(`✅ Auto-stop analysis complete - Found ${this.findings.autoStopMechanisms.length} findings\n`);

    } catch (error) {
      console.error('❌ Auto-stop analysis failed:', error);
    }
  }

  async analyzeYouTubeSpecificIssues() {
    console.log('📺 3. YOUTUBE-SPECIFIC ISSUES ANALYSIS');
    console.log('=====================================\n');

    try {
      // Check YouTube RTMP configuration
      const streamKeyValidatorPath = './utils/streamKeyValidator.js';
      
      if (fs.existsSync(streamKeyValidatorPath)) {
        const content = fs.readFileSync(streamKeyValidatorPath, 'utf8');
        
        // Check YouTube RTMP pattern
        const youtubePattern = content.match(/youtube:\s*{([^}]+)}/);
        if (youtubePattern) {
          this.findings.youtubeSpecific.push({
            type: 'YOUTUBE_VALIDATION_FOUND',
            location: 'utils/streamKeyValidator.js',
            details: 'YouTube RTMP validation is configured',
            impact: 'POSITIVE - Ensures proper YouTube RTMP format'
          });
        }
      }

      // Check FFmpeg YouTube-specific settings
      const streamingServicePath = './services/streamingService.js';
      if (fs.existsSync(streamingServicePath)) {
        const content = fs.readFileSync(streamingServicePath, 'utf8');
        
        // Check for YouTube-optimized settings
        if (content.includes('h264_mp4toannexb')) {
          this.findings.youtubeSpecific.push({
            type: 'YOUTUBE_BITSTREAM_FILTER_FOUND',
            location: 'services/streamingService.js',
            details: 'H.264 bitstream filter for YouTube compatibility',
            impact: 'POSITIVE - Improves YouTube streaming compatibility'
          });
        }

        if (content.includes('flvflags')) {
          this.findings.youtubeSpecific.push({
            type: 'FLV_FLAGS_FOUND',
            location: 'services/streamingService.js',
            details: 'FLV flags configured for streaming',
            impact: 'POSITIVE - Optimizes FLV format for RTMP streaming'
          });
        }

        // Check buffer settings that might affect long streams
        if (content.includes('bufsize')) {
          const bufferMatches = content.match(/bufsize['"]\s*,\s*[`'"]([^`'"]+)[`'"]/g);
          if (bufferMatches) {
            this.findings.youtubeSpecific.push({
              type: 'BUFFER_SETTINGS_FOUND',
              location: 'services/streamingService.js',
              details: 'Buffer size settings detected',
              impact: 'MEDIUM - Buffer settings may affect long-running streams',
              bufferSettings: bufferMatches
            });
          }
        }
      }

      // Check for YouTube-specific error patterns
      this.findings.youtubeSpecific.push({
        type: 'YOUTUBE_KNOWN_ISSUES',
        location: 'Analysis',
        details: 'Known YouTube streaming issues',
        impact: 'MEDIUM - May cause unexpected disconnections',
        knownIssues: [
          'YouTube may disconnect streams after 12-24 hours for maintenance',
          'YouTube RTMP servers may reset connections during peak hours',
          'Bitrate fluctuations can cause YouTube to drop connections',
          'YouTube requires consistent keyframe intervals (recommended: 2-4 seconds)'
        ]
      });

      console.log(`✅ YouTube-specific analysis complete - Found ${this.findings.youtubeSpecific.length} findings\n`);

    } catch (error) {
      console.error('❌ YouTube-specific analysis failed:', error);
    }
  }

  async analyzeResourceManagement() {
    console.log('💾 4. RESOURCE MANAGEMENT ANALYSIS');
    console.log('==================================\n');

    try {
      // Check memory management settings
      const streamingServicePath = './services/streamingService.js';
      
      if (fs.existsSync(streamingServicePath)) {
        const content = fs.readFileSync(streamingServicePath, 'utf8');
        
        // Check cleanup intervals
        const cleanupInterval = content.match(/CLEANUP_INTERVAL\s*=\s*(\d+)/);
        if (cleanupInterval) {
          const intervalMs = parseInt(cleanupInterval[1]);
          const intervalHours = intervalMs / (60 * 60 * 1000);
          
          this.findings.resourceIssues.push({
            type: 'CLEANUP_INTERVAL_FOUND',
            location: 'services/streamingService.js',
            details: `Memory cleanup runs every ${intervalHours} hours`,
            impact: intervalHours > 2 ? 'MEDIUM - Long cleanup interval may cause memory buildup' : 'LOW - Reasonable cleanup interval'
          });
        }

        // Check for memory leak prevention
        if (content.includes('performStreamHealthCheck')) {
          this.findings.resourceIssues.push({
            type: 'HEALTH_CHECK_FOUND',
            location: 'services/streamingService.js',
            details: 'Stream health check system is implemented',
            impact: 'POSITIVE - Helps detect and fix stream issues'
          });
        }

        // Check CPU management
        if (content.includes('cpuManager')) {
          this.findings.resourceIssues.push({
            type: 'CPU_MANAGEMENT_FOUND',
            location: 'services/streamingService.js',
            details: 'CPU allocation management is active',
            impact: 'POSITIVE - Prevents CPU resource conflicts'
          });
        }
      }

      // Check current system resources
      const os = require('os');
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const memoryUsagePercent = ((totalMemory - freeMemory) / totalMemory) * 100;
      
      this.findings.resourceIssues.push({
        type: 'CURRENT_MEMORY_USAGE',
        location: 'System',
        details: `Memory usage: ${memoryUsagePercent.toFixed(2)}%`,
        impact: memoryUsagePercent > 80 ? 'HIGH - High memory usage may cause stream issues' : 'LOW - Memory usage is acceptable',
        stats: {
          total: Math.round(totalMemory / 1024 / 1024 / 1024) + ' GB',
          free: Math.round(freeMemory / 1024 / 1024 / 1024) + ' GB',
          used: Math.round((totalMemory - freeMemory) / 1024 / 1024 / 1024) + ' GB'
        }
      });

      console.log(`✅ Resource management analysis complete - Found ${this.findings.resourceIssues.length} findings\n`);

    } catch (error) {
      console.error('❌ Resource management analysis failed:', error);
    }
  }

  async analyzeLogFiles() {
    console.log('📋 5. LOG FILE ANALYSIS');
    console.log('=======================\n');

    try {
      const logDir = './logs';
      const logFiles = ['app.log', 'error.log', 'scheduler.log'];
      
      for (const logFile of logFiles) {
        const logPath = path.join(logDir, logFile);
        
        if (fs.existsSync(logPath)) {
          const logContent = fs.readFileSync(logPath, 'utf8');
          const lines = logContent.split('\n');
          
          // Look for stream termination patterns
          const terminationPatterns = [
            /stream.*stopped/i,
            /stream.*terminated/i,
            /auto.*stop/i,
            /exceeded.*duration/i,
            /ffmpeg.*exit/i,
            /connection.*refused/i,
            /i\/o.*error/i
          ];
          
          const relevantLines = lines.filter(line => 
            terminationPatterns.some(pattern => pattern.test(line))
          ).slice(-20); // Last 20 relevant entries
          
          if (relevantLines.length > 0) {
            this.findings.logAnalysis.push({
              type: 'TERMINATION_LOGS_FOUND',
              location: logFile,
              details: `Found ${relevantLines.length} stream termination entries`,
              impact: 'HIGH - Contains information about stream stops',
              entries: relevantLines
            });
          }
        }
      }

      console.log(`✅ Log analysis complete - Found ${this.findings.logAnalysis.length} findings\n`);

    } catch (error) {
      console.error('❌ Log analysis failed:', error);
    }
  }

  generateDiagnosticReport() {
    console.log('📋 COMPREHENSIVE DIAGNOSTIC REPORT');
    console.log('==================================\n');

    const allFindings = [
      ...this.findings.durationSettings,
      ...this.findings.autoStopMechanisms,
      ...this.findings.youtubeSpecific,
      ...this.findings.resourceIssues,
      ...this.findings.logAnalysis
    ];

    const highImpactFindings = allFindings.filter(f => f.impact && f.impact.startsWith('HIGH'));
    const mediumImpactFindings = allFindings.filter(f => f.impact && f.impact.startsWith('MEDIUM'));

    console.log(`📊 SUMMARY:`);
    console.log(`   Total Findings: ${allFindings.length}`);
    console.log(`   High Impact: ${highImpactFindings.length}`);
    console.log(`   Medium Impact: ${mediumImpactFindings.length}\n`);

    // High Impact Issues
    if (highImpactFindings.length > 0) {
      console.log('🚨 HIGH IMPACT FINDINGS (Likely Causes):');
      highImpactFindings.forEach((finding, index) => {
        console.log(`\n${index + 1}. ${finding.type}`);
        console.log(`   Location: ${finding.location}`);
        console.log(`   Details: ${finding.details}`);
        console.log(`   Impact: ${finding.impact}`);
        
        if (finding.config) {
          console.log(`   Configuration:`, finding.config);
        }
        if (finding.data) {
          console.log(`   Data: ${finding.data.length} items found`);
        }
      });
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    const durationFindings = this.findings.durationSettings.filter(f => f.type.includes('DURATION'));
    if (durationFindings.length > 0) {
      console.log('\n1. DURATION-BASED TERMINATION:');
      console.log('   ⚠️  Your streams have duration limits set');
      console.log('   🔧 Solution: Set duration to null or 0 for indefinite streaming');
      console.log('   📝 Check: Stream creation form and database entries');
    }

    const autoStopFindings = this.findings.autoStopMechanisms.filter(f => f.type.includes('AUTO_STOP'));
    if (autoStopFindings.length > 0) {
      console.log('\n2. AUTO-STOP MECHANISMS:');
      console.log('   ⚠️  Auto-stop mechanisms may be too aggressive');
      console.log('   🔧 Solution: Increase thresholds or disable for YouTube');
      console.log('   📝 Recent fixes: Thresholds have been made less aggressive');
    }

    console.log('\n3. YOUTUBE-SPECIFIC RECOMMENDATIONS:');
    console.log('   🔧 Use consistent bitrate (avoid fluctuations)');
    console.log('   🔧 Set keyframe interval to 2-4 seconds');
    console.log('   🔧 Monitor for YouTube maintenance windows');
    console.log('   🔧 Consider using YouTube\'s recommended RTMP settings');

    console.log('\n4. MONITORING RECOMMENDATIONS:');
    console.log('   📊 Enable detailed logging for stream terminations');
    console.log('   📊 Monitor memory usage during long streams');
    console.log('   📊 Set up alerts for unexpected stream stops');
    console.log('   📊 Track stream duration vs termination patterns');

    // Next Steps
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Check your stream settings for duration limits');
    console.log('2. Monitor logs during next stream session');
    console.log('3. Test with a shorter stream first (1-2 hours)');
    console.log('4. Verify YouTube RTMP settings are optimal');
    console.log('5. Consider implementing stream restart logic for YouTube');
  }
}

// Run the diagnostic
if (require.main === module) {
  const diagnostic = new YouTubeStreamDiagnostic();
  diagnostic.runDiagnostic().catch(console.error);
}

module.exports = YouTubeStreamDiagnostic;
