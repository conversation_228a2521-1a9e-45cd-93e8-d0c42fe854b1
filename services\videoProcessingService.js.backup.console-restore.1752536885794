const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const Video = require('../models/Video');
const { getDetailedVideoInfo } = require('../utils/videoProcessor');
const cpuManager = require('../utils/cpuManager');
const jobQueue = require('../utils/jobQueue');

class VideoProcessingService {
  constructor() {
    this.progressStore = new Map(); // Store progress for each video
  }

  /**
   * Add video to processing queue
   */
  async addToQueue(videoId) {
    return jobQueue.add(async () => {
      try {
        const video = await Video.findById(videoId);
        if (!video) {
          throw new Error('Video not found');
        }

        if (video.processing_status === 'completed' || video.processing_status === 'processing') {
          // console.log(`[VideoProcessing] Video ${videoId} already processed or in progress`); // Removed for production
          return { success: true, message: 'Video already processed or in progress' };
        }

        await Video.updateProcessingStatus(videoId, 'pending');
        // console.log(`[VideoProcessing] Added video ${videoId} to processing queue`); // Removed for production
        await this.processVideo(videoId);

        return { success: true, message: 'Video processed successfully' };
      } catch (error) {
        console.error(`[VideoProcessing] Error adding video to queue:`, error);
        await Video.updateProcessingStatus(videoId, 'failed');
        throw error;
      }
    });
  }


  /**
   * Process a single video
   */
  async processVideo(videoId) {
    try {
      // console.log(`[VideoProcessing] Starting processing for video ${videoId}`); // Removed for production
      await Video.updateProcessingStatus(videoId, 'processing');

      // Initialize progress tracking
      this.updateProgress(videoId, 0, 'processing', 'Starting video processing...');

      const video = await Video.findById(videoId);
      if (!video) {
        throw new Error('Video not found');
      }

      // Check if video needs processing
      const needsProcessing = this.needsProcessing(video);
      
      if (!needsProcessing) {
        // console.log(`[VideoProcessing] Video ${videoId} is already streaming-ready, marking as completed`); // Removed for production
        // Update progress to completed immediately
        this.updateProgress(videoId, 100, 'completed', 'Video is already streaming-ready');

        await Video.updateProcessingStatus(videoId, 'completed', video.filepath);

        // Clean up progress after a short delay
        setTimeout(() => {
          this.progressStore.delete(videoId);
        }, 3000);

        return { success: true, message: 'Video already streaming-ready' };
      }

      // Process the video
      const result = await this.convertToStreamingReady(video);
      
      if (result.success) {
        // Update video with processed information
        await Video.updateProcessingStatus(
          videoId, 
          'completed', 
          result.streamingReadyPath,
          result.metadata
        );
        
        // console.log(`[VideoProcessing] Successfully processed video ${videoId}`); // Removed for production
      } else {
        throw new Error(result.error || 'Processing failed');
      }

    } catch (error) {
      console.error(`[VideoProcessing] Error processing video ${videoId}:`, error);

      // Update progress to failed
      this.updateProgress(videoId, 0, 'failed', `Processing failed: ${error.message}`);

      // Update status to failed
      await Video.updateProcessingStatus(videoId, 'failed');

      // Clean up progress after error (keep for longer to show error)
      setTimeout(() => {
        this.progressStore.delete(videoId);
      }, 10000);

    } finally {
    }
  }

  /**
   * Check if video needs processing
   */
  needsProcessing(video) {
    // If already has streaming-ready path, no need to process
    if (video.streaming_ready_path) {
      return false;
    }

    // Check codec compatibility
    if (video.codec) {
      const codecLower = video.codec.toLowerCase();
      
      // HEVC/H.265, VP9, AV1 need re-encoding
      if (codecLower.includes('hevc') || codecLower.includes('h265') ||
          codecLower.includes('vp9') || codecLower.includes('av1')) {
        return true;
      }
    }

    // Check container format
    if (video.format && video.format.toLowerCase() === 'mkv') {
      return true;
    }

    // Check if video parameters are too high for streaming
    if (video.bitrate && video.bitrate > 5000) { // > 5Mbps
      return true;
    }

    if (video.resolution) {
      const [width, height] = video.resolution.split('x').map(Number);
      if (width > 1920 || height > 1080) { // > 1080p
        return true;
      }
    }

    // If H.264 and reasonable parameters, no processing needed
    if (video.codec && video.codec.toLowerCase().includes('h264')) {
      return false;
    }

    // Default: process if codec is unknown or not H.264
    return true;
  }

  /**
   * Convert video to streaming-ready format
   */
  async convertToStreamingReady(video) {
    return new Promise((resolve, reject) => {
      try {
        const inputPath = path.join(process.cwd(), 'public', video.filepath);
        const outputFilename = `processed_${uuidv4()}.mp4`;
        const outputPath = path.join(process.cwd(), 'public', 'uploads', 'videos', outputFilename);
        const outputRelativePath = `/uploads/videos/${outputFilename}`;

        // Ensure input file exists
        if (!fs.existsSync(inputPath)) {
          return reject(new Error('Input video file not found'));
        }

        // console.log(`[VideoProcessing] Converting ${video.filepath} to streaming-ready format`); // Removed for production
        // Get original video bitrate to preserve quality
        const originalBitrate = video.bitrate || 4000; // Default to 4Mbps if unknown
        const targetBitrate = Math.min(Math.max(originalBitrate, 2000), 8000); // Clamp between 2-8 Mbps

        // console.log(`[VideoProcessing] Original bitrate: ${originalBitrate}kbps, Target bitrate: ${targetBitrate}kbps`); // Removed for production
        // Get CPU allocation info for encoding
        const cpuInfo = cpuManager.getAllocationInfo();

        // Build FFmpeg command with CPU allocation
        const ffmpegCommand = ffmpeg(inputPath)
          .inputOptions([
            '-hwaccel', 'auto',
            '-thread_queue_size', '1024'  // Input option for threading
          ])
          .outputOptions([
            '-c:v', 'libx264',           // H.264 codec
            '-preset', 'medium',         // Balanced speed/quality
            '-crf', '20',                // Higher quality (lower CRF)
            '-maxrate', `${Math.round(targetBitrate * 1.2)}k`, // Allow 20% headroom
            '-bufsize', `${Math.round(targetBitrate * 2)}k`,   // Buffer size
            '-pix_fmt', 'yuv420p',       // Compatible pixel format
            '-profile:v', 'high',        // H.264 high profile
            '-level', '4.0',             // H.264 level 4.0
            '-g', '60',                  // GOP size
            '-keyint_min', '60',         // Min keyframe interval
            '-sc_threshold', '0',        // Scene change threshold
            '-threads', cpuInfo.encoding.threads.toString(), // CPU allocation for encoding
            '-filter_threads', cpuInfo.encoding.threads.toString(), // Filter threads
            '-c:a', 'aac',               // AAC audio codec
            '-b:a', '128k',              // Audio bitrate
            '-ar', '44100',              // Audio sample rate
            '-ac', '2',                  // Stereo audio
            '-movflags', '+faststart'    // Web-optimized MP4
          ]);

        console.log(`[VideoProcessing] Using ${cpuInfo.encoding.threads} threads for encoding (cores: ${cpuInfo.encoding.range})`);

        ffmpegCommand
          .output(outputPath)
          .on('start', (commandLine) => {
            // console.log(`[VideoProcessing] FFmpeg command: ${commandLine}`); // Removed for production
          })
          .on('progress', (progress) => {
            if (progress.percent) {
              const progressPercent = Math.round(progress.percent);
              // console.log(`[VideoProcessing] Processing ${video.id}: ${progressPercent}%`); // Removed for production
              // Store progress for API access
              this.updateProgress(video.id, progressPercent, 'processing', `Processing video: ${progressPercent}%`);
            }
          })
          .on('end', async () => {
            try {
              // Update progress to 100% before completion
              this.updateProgress(video.id, 100, 'completed', 'Processing completed successfully');

              // Get metadata of processed video
              const processedMetadata = await getDetailedVideoInfo(outputPath);

              // console.log(`[VideoProcessing] Successfully converted ${video.filepath} to ${outputRelativePath}`); // Removed for production
              // Clean up progress store after completion
              setTimeout(() => {
                this.progressStore.delete(video.id);
              }, 5000); // Keep progress for 5 seconds after completion

              resolve({
                success: true,
                streamingReadyPath: outputRelativePath,
                metadata: {
                  codec: 'h264',
                  resolution: processedMetadata.resolution || video.resolution,
                  bitrate: processedMetadata.bitrate || targetBitrate,
                  fps: processedMetadata.fps || video.fps
                }
              });
            } catch (metadataError) {
              console.error('[VideoProcessing] Error getting processed video metadata:', metadataError);
              resolve({
                success: true,
                streamingReadyPath: outputRelativePath,
                metadata: {
                  codec: 'h264',
                  resolution: video.resolution,
                  bitrate: targetBitrate,
                  fps: video.fps || 30
                }
              });
            }
          })
          .on('error', (error) => {
            console.error(`[VideoProcessing] FFmpeg error for ${video.id}:`, error);

            // Update progress to failed
            this.updateProgress(video.id, 0, 'failed', `Processing failed: ${error.message}`);

            // Clean up failed output file
            if (fs.existsSync(outputPath)) {
              try {
                fs.unlinkSync(outputPath);
              } catch (cleanupError) {
                console.error('[VideoProcessing] Error cleaning up failed file:', cleanupError);
              }
            }

            // Clean up progress store after error
            setTimeout(() => {
              this.progressStore.delete(video.id);
            }, 10000); // Keep error message for 10 seconds

            reject(new Error(`Video processing failed: ${error.message}`));
          })
          .run();

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Get processing queue status
   */
  getQueueStatus() {
    return {
      queueLength: jobQueue.size,
      isProcessing: jobQueue.running > 0
    };
  }

  /**
   * Get detailed queue information for admin/monitoring
   */

  /**
   * Get processing statistics
   */
  async getProcessingStats() {
    try {
      const videos = await Video.getAll();
      const stats = {
        total: videos.length,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        streamingReady: 0
      };

      videos.forEach(video => {
        if (video.processing_status) {
          stats[video.processing_status]++;
        }
        if (video.streaming_ready_path) {
          stats.streamingReady++;
        }
      });

      return stats;
    } catch (error) {
      console.error('[VideoProcessing] Error getting processing stats:', error);
      return null;
    }
  }

  /**
   * Update progress for a video
   */
  updateProgress(videoId, percent, status, message) {
    this.progressStore.set(videoId, {
      percent: Math.min(Math.max(percent, 0), 100), // Clamp between 0-100
      status: status,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get progress for a video
   */
  getProgress(videoId) {
    return this.progressStore.get(videoId) || null;
  }

  /**
   * Get all current progress
   */
  getAllProgress() {
    const progress = {};
    for (const [videoId, data] of this.progressStore.entries()) {
      progress[videoId] = data;
    }
    return progress;
  }

  /**
   * Clear progress for a video
   */
  clearProgress(videoId) {
    this.progressStore.delete(videoId);
  }

  /**
   * Update global concurrent job limit
   */

  /**
   * Update per-user concurrent job limit
   */

  /**
   * Get current configuration
   */

  /**
   * Remove user from all queues (for cleanup/admin purposes)
   */
}

// Create singleton instance
const videoProcessingService = new VideoProcessingService();

module.exports = videoProcessingService;
