#!/usr/bin/env node

/**
 * Test Stream Starting Fix
 * Verifies that the fix allows PodLite users to start their created streams
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const { v4: uuidv4 } = require('uuid');

async function testFix() {
  try {
    console.log('🧪 StreamOnPod: Testing Stream Starting Fix');
    console.log('='.repeat(60));
    console.log(`Time: ${new Date().toISOString()}\n`);

    // Use our test user
    const testUser = await new Promise((resolve, reject) => {
      db.get('SELECT id, username FROM users WHERE username = ?', ['podlite_test_user'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!testUser) {
      console.log('❌ Test user not found. Run create-test-user.js first.');
      process.exit(1);
    }

    console.log(`🧪 Testing with user: ${testUser.username} (${testUser.id})`);

    // Clean up any existing streams
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [testUser.id], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Cleaned up existing streams');

    // TEST 1: Verify original quota check still works for creation
    console.log('\n📋 TEST 1: Original Quota Check (Stream Creation Logic)');
    console.log('-'.repeat(50));

    let quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`Creation quota: hasLimit=${quotaCheck.hasLimit}, slots=${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);

    if (quotaCheck.hasLimit) {
      console.log('❌ UNEXPECTED: User blocked for creation with 0 streams!');
      return;
    } else {
      console.log('✅ EXPECTED: User can create streams (0 streams)');
    }

    // TEST 2: Create a stream
    console.log('\n📋 TEST 2: Create Stream');
    console.log('-'.repeat(50));

    const streamId = uuidv4();
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [streamId, testUser.id, 'Test Stream', 'offline', 'rtmp://test.example.com/live', 'test_key_123'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`✅ Stream created: ${streamId} (status: offline)`);

    // TEST 3: Test original quota check after creation (should fail)
    console.log('\n📋 TEST 3: Original Quota Check After Creation');
    console.log('-'.repeat(50));

    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`Creation quota: hasLimit=${quotaCheck.hasLimit}, slots=${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);

    if (quotaCheck.hasLimit) {
      console.log('✅ EXPECTED: Original logic still blocks (counts offline streams)');
    } else {
      console.log('❌ UNEXPECTED: Original logic should block but doesn\'t');
    }

    // TEST 4: Test NEW quota check for starting (should pass)
    console.log('\n📋 TEST 4: NEW Quota Check For Starting');
    console.log('-'.repeat(50));

    const startingQuotaCheck = await Subscription.checkStreamingSlotLimitForStarting(testUser.id, streamId);
    console.log(`Starting quota: hasLimit=${startingQuotaCheck.hasLimit}, slots=${startingQuotaCheck.currentSlots}/${startingQuotaCheck.maxSlots}`);
    console.log(`Check type: ${startingQuotaCheck.checkType}`);

    if (!startingQuotaCheck.hasLimit) {
      console.log('🎉 SUCCESS: New logic allows starting (counts only live streams)');
    } else {
      console.log('❌ FAILED: New logic still blocks');
    }

    // TEST 5: Test with a live stream to ensure blocking still works
    console.log('\n📋 TEST 5: Test Blocking With Live Stream');
    console.log('-'.repeat(50));

    // Create another stream and set it to live
    const liveStreamId = uuidv4();
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [liveStreamId, testUser.id, 'Live Stream', 'live', 'rtmp://test.example.com/live2', 'test_key_456'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`✅ Live stream created: ${liveStreamId} (status: live)`);

    const quotaWithLive = await Subscription.checkStreamingSlotLimitForStarting(testUser.id, streamId);
    console.log(`Starting quota with live stream: hasLimit=${quotaWithLive.hasLimit}, slots=${quotaWithLive.currentSlots}/${quotaWithLive.maxSlots}`);

    if (quotaWithLive.hasLimit) {
      console.log('✅ EXPECTED: New logic correctly blocks when at live stream limit');
    } else {
      console.log('❌ FAILED: New logic should block when at limit');
    }

    // TEST 6: Test middleware logic simulation
    console.log('\n📋 TEST 6: Middleware Logic Simulation');
    console.log('-'.repeat(50));

    // Simulate the middleware request for starting a stream
    const mockRequest = {
      session: { userId: testUser.id },
      params: { id: streamId },
      body: { status: 'live' }
    };

    console.log('Simulating request to start offline stream:');
    console.log(`   User: ${mockRequest.session.userId}`);
    console.log(`   Stream: ${mockRequest.params.id}`);
    console.log(`   Action: ${mockRequest.body.status}`);

    // Remove the live stream first to test starting the offline one
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE id = ?', [liveStreamId], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    const finalQuotaCheck = await Subscription.checkStreamingSlotLimitForStarting(
      mockRequest.session.userId, 
      mockRequest.params.id
    );

    console.log(`Final quota check: hasLimit=${finalQuotaCheck.hasLimit}, slots=${finalQuotaCheck.currentSlots}/${finalQuotaCheck.maxSlots}`);

    if (!finalQuotaCheck.hasLimit) {
      console.log('🎉 SUCCESS: User can start their offline stream!');
      console.log('✅ The "Streaming Limit Reached" error should no longer occur');
    } else {
      console.log('❌ FAILED: User still cannot start their stream');
    }

    // Clean up
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [testUser.id], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('\n✅ Test streams cleaned up');

    // SUMMARY
    console.log('\n📊 TEST SUMMARY');
    console.log('='.repeat(60));
    
    // Check individual test results - need to use the right variables from each test
    // Get the first quota check result (before creating stream)
    const initialQuotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);

    const test1Pass = !initialQuotaCheck.hasLimit; // Creation works (should be false - no limit)
    const test3Pass = quotaCheck.hasLimit; // Original logic blocks after creation (should be true)
    const test4Pass = !startingQuotaCheck.hasLimit; // New logic allows starting (should be false - no limit)
    const test5Pass = quotaWithLive.hasLimit; // New logic blocks when at limit (should be true)
    const test6Pass = !finalQuotaCheck.hasLimit; // Final test passes (should be false - no limit)

    console.log(`Test 1 (Creation with 0 streams): ${test1Pass ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Test 3 (Original blocks after creation): ${test3Pass ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Test 4 (New logic allows starting): ${test4Pass ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Test 5 (New logic blocks at limit): ${test5Pass ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Test 6 (Final starting test): ${test6Pass ? '✅ PASS' : '❌ FAIL'}`);

    const allTestsPassed = test1Pass && test3Pass && test4Pass && test5Pass && test6Pass;

    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED!');
      console.log('✅ Stream creation quota logic: Working');
      console.log('✅ Stream starting quota logic: Fixed');
      console.log('✅ Limit enforcement: Still working');
      console.log('✅ PodLite users can now start their streams');
    } else {
      console.log('❌ SOME TESTS FAILED');
      console.log('⚠️  The fix may need additional adjustments');
    }

    console.log('\n' + '='.repeat(60));

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Test interrupted by user');
  process.exit(1);
});

// Run the test
testFix().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
