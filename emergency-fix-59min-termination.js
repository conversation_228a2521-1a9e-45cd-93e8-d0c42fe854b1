#!/usr/bin/env node

/**
 * EMERGENCY FIX: 59-Minute Stream Termination
 * Immediate investigation and fix for the critical 1-hour termination bug
 */

const fs = require('fs');
const path = require('path');
const { db } = require('./db/database');

console.log('🚨 EMERGENCY: 59-Minute Termination Fix');
console.log('=====================================\n');

class EmergencyTerminationFix {
  constructor() {
    this.findings = [];
    this.fixes = [];
  }

  async investigate() {
    console.log('🔍 INVESTIGATING 59-MINUTE TERMINATION...\n');

    // 1. Check for duration limits in database
    await this.checkDurationLimits();
    
    // 2. Check scheduler service configuration
    await this.checkSchedulerConfig();
    
    // 3. Check cleanup interval settings
    await this.checkCleanupSettings();
    
    // 4. Check for any cron jobs or scheduled tasks
    await this.checkScheduledTasks();
    
    // 5. Generate immediate fixes
    await this.generateFixes();
  }

  async checkDurationLimits() {
    console.log('1️⃣ Checking database for duration limits...');
    
    try {
      const streamsWithDuration = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, duration, status, start_time, created_at
          FROM streams 
          WHERE duration IS NOT NULL AND duration > 0
          ORDER BY created_at DESC
          LIMIT 10
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      if (streamsWithDuration.length > 0) {
        console.log(`🚨 CRITICAL: Found ${streamsWithDuration.length} streams with duration limits!`);
        
        streamsWithDuration.forEach(stream => {
          console.log(`   - "${stream.title}": ${stream.duration} minutes`);
          if (stream.duration >= 55 && stream.duration <= 65) {
            this.findings.push({
              type: 'DURATION_LIMIT_59MIN',
              severity: 'CRITICAL',
              stream: stream,
              message: `Stream has ~60 minute duration limit causing termination`
            });
          }
        });
        
        this.fixes.push({
          type: 'REMOVE_DURATION_LIMITS',
          priority: 'IMMEDIATE',
          action: 'Remove all duration limits from streams'
        });
      } else {
        console.log('✅ No duration limits found in database');
      }
    } catch (error) {
      console.log(`❌ Database check failed: ${error.message}`);
      this.findings.push({
        type: 'DATABASE_ERROR',
        severity: 'HIGH',
        message: error.message
      });
    }
  }

  async checkSchedulerConfig() {
    console.log('\n2️⃣ Checking scheduler service configuration...');
    
    try {
      const schedulerPath = './services/schedulerService.js';
      if (fs.existsSync(schedulerPath)) {
        const content = fs.readFileSync(schedulerPath, 'utf8');
        
        // Check for duration checking logic
        if (content.includes('checkStreamDurations')) {
          console.log('⚠️  Found duration checking in scheduler service');
          this.findings.push({
            type: 'SCHEDULER_DURATION_CHECK',
            severity: 'HIGH',
            message: 'Scheduler service actively checks and terminates streams based on duration'
          });
          
          this.fixes.push({
            type: 'DISABLE_SCHEDULER_DURATION_CHECK',
            priority: 'IMMEDIATE',
            action: 'Disable duration-based termination in scheduler'
          });
        }
        
        // Check for scheduled termination function
        if (content.includes('scheduleStreamTermination')) {
          console.log('🚨 CRITICAL: Found scheduled termination function');
          this.findings.push({
            type: 'SCHEDULED_TERMINATION',
            severity: 'CRITICAL',
            message: 'Scheduler can schedule automatic stream terminations'
          });
        }
      }
    } catch (error) {
      console.log(`❌ Scheduler check failed: ${error.message}`);
    }
  }

  async checkCleanupSettings() {
    console.log('\n3️⃣ Checking cleanup interval settings...');
    
    try {
      const streamingServicePath = './services/streamingService.js';
      if (fs.existsSync(streamingServicePath)) {
        const content = fs.readFileSync(streamingServicePath, 'utf8');
        
        // Check current cleanup interval
        const cleanupMatch = content.match(/CLEANUP_INTERVAL\s*=\s*(\d+)/);
        if (cleanupMatch) {
          const intervalMs = parseInt(cleanupMatch[1]);
          const intervalHours = intervalMs / (1000 * 60 * 60);
          console.log(`📊 Current cleanup interval: ${intervalHours} hours`);
          
          if (intervalHours <= 1) {
            this.findings.push({
              type: 'SHORT_CLEANUP_INTERVAL',
              severity: 'HIGH',
              message: `Cleanup interval is ${intervalHours} hours - may cause false terminations`
            });
            
            this.fixes.push({
              type: 'INCREASE_CLEANUP_INTERVAL',
              priority: 'HIGH',
              action: 'Increase cleanup interval to prevent false terminations'
            });
          }
        }
      }
    } catch (error) {
      console.log(`❌ Cleanup settings check failed: ${error.message}`);
    }
  }

  async checkScheduledTasks() {
    console.log('\n4️⃣ Checking for scheduled tasks or cron jobs...');
    
    // Check for any files that might contain scheduled tasks
    const taskFiles = [
      './crontab',
      './package.json',
      './app.js'
    ];
    
    for (const file of taskFiles) {
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          if (content.includes('setInterval') || content.includes('setTimeout') || content.includes('cron')) {
            console.log(`⚠️  Found scheduled tasks in ${file}`);
          }
        } catch (error) {
          // Ignore file read errors
        }
      }
    }
  }

  async generateFixes() {
    console.log('\n🔧 GENERATING IMMEDIATE FIXES...\n');
    
    if (this.findings.length === 0) {
      console.log('✅ No critical issues found - the 59-minute termination may be from another source');
      return;
    }
    
    console.log('📋 CRITICAL FINDINGS:');
    this.findings.forEach((finding, index) => {
      console.log(`${index + 1}. [${finding.severity}] ${finding.type}`);
      console.log(`   ${finding.message}`);
      if (finding.stream) {
        console.log(`   Stream: "${finding.stream.title}" (${finding.stream.duration} min)`);
      }
      console.log('');
    });
    
    console.log('🚀 IMMEDIATE FIXES TO APPLY:');
    this.fixes.forEach((fix, index) => {
      console.log(`${index + 1}. [${fix.priority}] ${fix.type}`);
      console.log(`   Action: ${fix.action}`);
      console.log('');
    });
  }

  async applyEmergencyFixes() {
    console.log('🚨 APPLYING EMERGENCY FIXES...\n');
    
    let fixesApplied = 0;
    
    // Fix 1: Remove all duration limits
    try {
      const result = await new Promise((resolve, reject) => {
        db.run(`
          UPDATE streams 
          SET duration = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE duration IS NOT NULL AND duration > 0
        `, [], function(err) {
          if (err) reject(err);
          else resolve(this.changes);
        });
      });
      
      if (result > 0) {
        console.log(`✅ Fixed: Removed duration limits from ${result} streams`);
        fixesApplied++;
      }
    } catch (error) {
      console.log(`❌ Failed to remove duration limits: ${error.message}`);
    }
    
    // Fix 2: Disable scheduler duration checking (temporary)
    try {
      const schedulerPath = './services/schedulerService.js';
      if (fs.existsSync(schedulerPath)) {
        let content = fs.readFileSync(schedulerPath, 'utf8');
        
        // Comment out the duration checking logic
        const originalContent = content;
        content = content.replace(
          /checkStreamDurations\(\);/g,
          '// checkStreamDurations(); // EMERGENCY FIX: Disabled to prevent 59-min terminations'
        );
        
        if (content !== originalContent) {
          // Create backup first
          fs.writeFileSync(schedulerPath + '.backup.emergency', originalContent);
          fs.writeFileSync(schedulerPath, content);
          console.log('✅ Fixed: Temporarily disabled scheduler duration checking');
          console.log('📁 Backup created: schedulerService.js.backup.emergency');
          fixesApplied++;
        }
      }
    } catch (error) {
      console.log(`❌ Failed to disable scheduler: ${error.message}`);
    }
    
    console.log(`\n🎉 Applied ${fixesApplied} emergency fixes`);
    
    if (fixesApplied > 0) {
      console.log('\n⚠️  IMPORTANT: Restart your StreamOnPod server for fixes to take effect');
      console.log('   Command: npm run dev');
    }
  }
}

// Main execution
async function main() {
  const fixer = new EmergencyTerminationFix();
  
  try {
    await fixer.investigate();
    
    // Ask user if they want to apply fixes
    if (process.argv.includes('--fix')) {
      await fixer.applyEmergencyFixes();
    } else {
      console.log('\n💡 To apply emergency fixes automatically, run:');
      console.log('   node emergency-fix-59min-termination.js --fix');
    }
    
  } catch (error) {
    console.error('❌ Emergency fix failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node emergency-fix-59min-termination.js [--fix]');
  console.log('');
  console.log('Options:');
  console.log('  --fix    Apply emergency fixes automatically');
  console.log('  --help   Show this help message');
  process.exit(0);
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
