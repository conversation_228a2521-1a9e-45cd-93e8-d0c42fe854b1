#!/usr/bin/env node

/**
 * Test PodLite Scenario
 * Simulates the reported issue scenario to verify the fix is working
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');

class PodLiteScenarioTester {
  constructor() {
    this.testResults = [];
  }

  async run() {
    try {
      console.log('🧪 StreamOnPod: PodLite Scenario Testing');
      console.log('='.repeat(50));
      console.log(`Time: ${new Date().toISOString()}\n`);

      // Test 1: User with 0 streams should be able to create
      await this.testZeroStreamsScenario();
      
      // Test 2: User with 1 stream should be blocked
      await this.testOneStreamScenario();
      
      // Test 3: Test the middleware response
      await this.testMiddlewareResponse();
      
      // Summary
      await this.printResults();

    } catch (error) {
      console.error('❌ Test failed:', error.message);
      process.exit(1);
    }
  }

  async testZeroStreamsScenario() {
    console.log('📋 Test 1: PodLite User with 0 Streams');
    console.log('-'.repeat(40));

    // Find a PodLite user or create test scenario
    const podliteUser = await new Promise((resolve, reject) => {
      db.get(`
        SELECT u.id, u.username
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE sp.name = 'PodLite' AND us.status = 'active'
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!podliteUser) {
      console.log('⚠️  No active PodLite users found for testing');
      this.testResults.push({
        test: 'Zero Streams Scenario',
        status: 'SKIPPED',
        reason: 'No PodLite users available'
      });
      return;
    }

    console.log(`Testing with user: ${podliteUser.username}`);

    // Get current stream count
    const currentStreams = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [podliteUser.id], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    console.log(`Current streams: ${currentStreams}`);

    // Test quota check
    try {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(podliteUser.id);
      
      console.log('Quota check result:');
      console.log(`  - Has limit: ${quotaCheck.hasLimit}`);
      console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
      console.log(`  - Current slots: ${quotaCheck.currentSlots}`);

      // Expected behavior: If user has < 1 stream, should be allowed
      const shouldBeAllowed = currentStreams < 1;
      const isAllowed = !quotaCheck.hasLimit;

      if (shouldBeAllowed === isAllowed) {
        console.log('✅ Test PASSED: Quota logic working correctly');
        this.testResults.push({
          test: 'Zero Streams Scenario',
          status: 'PASSED',
          expected: shouldBeAllowed ? 'ALLOWED' : 'BLOCKED',
          actual: isAllowed ? 'ALLOWED' : 'BLOCKED'
        });
      } else {
        console.log('❌ Test FAILED: Quota logic incorrect');
        this.testResults.push({
          test: 'Zero Streams Scenario',
          status: 'FAILED',
          expected: shouldBeAllowed ? 'ALLOWED' : 'BLOCKED',
          actual: isAllowed ? 'ALLOWED' : 'BLOCKED',
          quotaResult: quotaCheck
        });
      }

    } catch (error) {
      console.log(`❌ Error during quota check: ${error.message}`);
      this.testResults.push({
        test: 'Zero Streams Scenario',
        status: 'ERROR',
        error: error.message
      });
    }

    console.log('');
  }

  async testOneStreamScenario() {
    console.log('📋 Test 2: PodLite User at Limit (1 Stream)');
    console.log('-'.repeat(40));

    // Find the user we know has 1 stream (kimdogi from our previous check)
    const userWithOneStream = await new Promise((resolve, reject) => {
      db.get(`
        SELECT u.id, u.username, COUNT(s.id) as stream_count
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        LEFT JOIN streams s ON u.id = s.user_id
        WHERE sp.name = 'PodLite' AND us.status = 'active'
        GROUP BY u.id, u.username
        HAVING stream_count = 1
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!userWithOneStream) {
      console.log('⚠️  No PodLite users with exactly 1 stream found');
      this.testResults.push({
        test: 'One Stream Scenario',
        status: 'SKIPPED',
        reason: 'No suitable test user found'
      });
      return;
    }

    console.log(`Testing with user: ${userWithOneStream.username} (${userWithOneStream.stream_count} streams)`);

    try {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(userWithOneStream.id);
      
      console.log('Quota check result:');
      console.log(`  - Has limit: ${quotaCheck.hasLimit}`);
      console.log(`  - Max slots: ${quotaCheck.maxSlots}`);
      console.log(`  - Current slots: ${quotaCheck.currentSlots}`);

      // Expected behavior: User with 1 stream should be blocked from creating more
      const shouldBeBlocked = true;
      const isBlocked = quotaCheck.hasLimit;

      if (shouldBeBlocked === isBlocked) {
        console.log('✅ Test PASSED: User correctly blocked at limit');
        this.testResults.push({
          test: 'One Stream Scenario',
          status: 'PASSED',
          expected: 'BLOCKED',
          actual: 'BLOCKED'
        });
      } else {
        console.log('❌ Test FAILED: User should be blocked but is allowed');
        this.testResults.push({
          test: 'One Stream Scenario',
          status: 'FAILED',
          expected: 'BLOCKED',
          actual: 'ALLOWED',
          quotaResult: quotaCheck
        });
      }

    } catch (error) {
      console.log(`❌ Error during quota check: ${error.message}`);
      this.testResults.push({
        test: 'One Stream Scenario',
        status: 'ERROR',
        error: error.message
      });
    }

    console.log('');
  }

  async testMiddlewareResponse() {
    console.log('📋 Test 3: Middleware Error Response');
    console.log('-'.repeat(40));

    // Simulate what the middleware would return for a blocked user
    const blockedUser = await new Promise((resolve, reject) => {
      db.get(`
        SELECT u.id, u.username
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE sp.name = 'PodLite' AND us.status = 'active'
        AND (SELECT COUNT(*) FROM streams WHERE user_id = u.id) >= sp.max_streaming_slots
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!blockedUser) {
      console.log('⚠️  No blocked PodLite users found for middleware testing');
      this.testResults.push({
        test: 'Middleware Response',
        status: 'SKIPPED',
        reason: 'No blocked users available'
      });
      return;
    }

    console.log(`Testing middleware response for: ${blockedUser.username}`);

    try {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(blockedUser.id);
      
      if (quotaCheck.hasLimit) {
        // Simulate the middleware response
        const expectedMessage = `You have reached your streaming limit of ${quotaCheck.maxSlots} concurrent streams. Please upgrade your plan or stop an existing stream.`;
        
        const mockResponse = {
          success: false,
          error: expectedMessage,
          code: 'STREAMING_SLOT_LIMIT',
          details: {
            currentSlots: quotaCheck.currentSlots,
            maxSlots: quotaCheck.maxSlots,
            isPreviewPlan: quotaCheck.maxSlots === 0,
            planType: quotaCheck.maxSlots === 0 ? 'Preview' : 'Paid'
          }
        };

        console.log('Expected middleware response:');
        console.log(JSON.stringify(mockResponse, null, 2));

        console.log('✅ Test PASSED: Middleware would return correct error message');
        this.testResults.push({
          test: 'Middleware Response',
          status: 'PASSED',
          response: mockResponse
        });
      } else {
        console.log('❌ Test FAILED: User should be blocked but quota check allows');
        this.testResults.push({
          test: 'Middleware Response',
          status: 'FAILED',
          reason: 'User not blocked as expected'
        });
      }

    } catch (error) {
      console.log(`❌ Error testing middleware: ${error.message}`);
      this.testResults.push({
        test: 'Middleware Response',
        status: 'ERROR',
        error: error.message
      });
    }

    console.log('');
  }

  async printResults() {
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));

    let passed = 0;
    let failed = 0;
    let skipped = 0;
    let errors = 0;

    for (const result of this.testResults) {
      const status = result.status;
      const icon = status === 'PASSED' ? '✅' : 
                   status === 'FAILED' ? '❌' : 
                   status === 'SKIPPED' ? '⏭️' : '🔥';
      
      console.log(`${icon} ${result.test}: ${status}`);
      
      if (result.expected && result.actual) {
        console.log(`   Expected: ${result.expected}, Actual: ${result.actual}`);
      }
      
      if (result.reason) {
        console.log(`   Reason: ${result.reason}`);
      }
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }

      switch (status) {
        case 'PASSED': passed++; break;
        case 'FAILED': failed++; break;
        case 'SKIPPED': skipped++; break;
        case 'ERROR': errors++; break;
      }
    }

    console.log('\n' + '-'.repeat(30));
    console.log(`Total Tests: ${this.testResults.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Skipped: ${skipped}`);
    console.log(`Errors: ${errors}`);

    if (failed === 0 && errors === 0) {
      console.log('\n🎉 ALL TESTS SUCCESSFUL!');
      console.log('PodLite streaming limits are working correctly.');
    } else {
      console.log('\n⚠️  ISSUES DETECTED!');
      console.log('Some tests failed or encountered errors.');
    }

    console.log('='.repeat(50));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Test interrupted by user');
  process.exit(1);
});

// Run the tests
const tester = new PodLiteScenarioTester();
tester.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
