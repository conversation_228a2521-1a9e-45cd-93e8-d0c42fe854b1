#!/usr/bin/env node

/**
 * Restart StreamOnPod with 1-Hour Termination Fix
 * Comprehensive script to restart the application and verify fixes are active
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 StreamOnPod Application Restart with 1-Hour Fix');
console.log('==================================================\n');

class ApplicationRestart {
  constructor() {
    this.logFile = path.join(__dirname, 'logs', 'restart-verification.log');
    this.restartResults = {
      startTime: new Date(),
      fixesVerified: [],
      issues: [],
      applicationStatus: 'unknown'
    };
    
    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${type}] ${message}`;
    console.log(logEntry);
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async killExistingProcesses() {
    this.log('Checking for existing Node.js processes...', 'INFO');
    
    return new Promise((resolve) => {
      exec('tasklist | findstr node', (error, stdout, stderr) => {
        if (stdout && stdout.trim()) {
          this.log('Found existing Node.js processes:', 'WARNING');
          this.log(stdout.trim(), 'INFO');
          
          // Kill existing processes
          exec('taskkill /f /im node.exe', (killError, killStdout, killStderr) => {
            if (killError) {
              this.log(`Error killing processes: ${killError.message}`, 'WARNING');
            } else {
              this.log('Existing Node.js processes terminated', 'SUCCESS');
            }
            resolve();
          });
        } else {
          this.log('No existing Node.js processes found', 'INFO');
          resolve();
        }
      });
    });
  }

  async verifyFixesInCode() {
    this.log('Verifying emergency fixes are present in code...', 'INFO');
    
    try {
      const serviceFile = fs.readFileSync('./services/streamingService.js', 'utf8');
      
      // Check critical fixes
      const fixes = [
        {
          check: serviceFile.includes('EMERGENCY: Health check disabled'),
          name: 'Health check function disabled',
          critical: true
        },
        {
          check: serviceFile.includes('DISABLED: await performStreamHealthCheck()'),
          name: 'Periodic health check calls disabled',
          critical: true
        },
        {
          check: serviceFile.includes('CLEANUP_INTERVAL = 2 * 60 * 60 * 1000'),
          name: 'Safe cleanup interval (2 hours)',
          critical: false
        }
      ];
      
      let criticalIssues = 0;
      fixes.forEach(fix => {
        if (fix.check) {
          this.restartResults.fixesVerified.push(fix.name);
          this.log(`✅ ${fix.name}`, 'SUCCESS');
        } else {
          this.restartResults.issues.push(`Missing fix: ${fix.name}`);
          this.log(`❌ Missing fix: ${fix.name}`, 'ERROR');
          if (fix.critical) criticalIssues++;
        }
      });
      
      if (criticalIssues > 0) {
        this.log(`🚨 ${criticalIssues} critical fixes missing - termination bug will persist!`, 'CRITICAL');
        return false;
      }
      
      return true;
      
    } catch (error) {
      this.log(`Cannot verify fixes: ${error.message}`, 'ERROR');
      return false;
    }
  }

  async startApplication() {
    this.log('Starting StreamOnPod application...', 'INFO');
    
    return new Promise((resolve) => {
      // Start the application
      const appProcess = spawn('npm', ['run', 'dev'], {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: true
      });
      
      let startupOutput = '';
      let errorOutput = '';
      
      appProcess.stdout.on('data', (data) => {
        const output = data.toString();
        startupOutput += output;
        
        // Log important startup messages
        if (output.includes('EMERGENCY') || output.includes('Health check disabled')) {
          this.log(`🔧 STARTUP: ${output.trim()}`, 'SUCCESS');
        }
        
        // Check if server started successfully
        if (output.includes('Server running on') || output.includes('listening on')) {
          this.log('✅ Application started successfully', 'SUCCESS');
          this.restartResults.applicationStatus = 'running';
          resolve(true);
        }
      });
      
      appProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        this.log(`STDERR: ${data.toString().trim()}`, 'WARNING');
      });
      
      appProcess.on('error', (error) => {
        this.log(`Failed to start application: ${error.message}`, 'ERROR');
        this.restartResults.applicationStatus = 'failed';
        resolve(false);
      });
      
      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.restartResults.applicationStatus === 'unknown') {
          this.log('Application startup timeout - may still be starting', 'WARNING');
          this.restartResults.applicationStatus = 'timeout';
          resolve(false);
        }
      }, 30000);
    });
  }

  async verifyApplicationRunning() {
    this.log('Verifying application is accessible...', 'INFO');
    
    return new Promise((resolve) => {
      // Wait a moment for the server to fully start
      setTimeout(() => {
        exec('netstat -an | findstr :3000', (error, stdout, stderr) => {
          if (stdout && stdout.includes('3000')) {
            this.log('✅ Application is listening on port 3000', 'SUCCESS');
            resolve(true);
          } else {
            this.log('❌ Application not accessible on port 3000', 'ERROR');
            resolve(false);
          }
        });
      }, 5000);
    });
  }

  async generateRestartReport() {
    const duration = new Date() - this.restartResults.startTime;
    const durationSeconds = Math.round(duration / 1000);
    
    this.log('\n📋 APPLICATION RESTART REPORT', 'REPORT');
    this.log('==============================', 'REPORT');
    this.log(`Restart Duration: ${durationSeconds} seconds`, 'REPORT');
    this.log(`Application Status: ${this.restartResults.applicationStatus}`, 'REPORT');
    this.log(`Fixes Verified: ${this.restartResults.fixesVerified.length}`, 'REPORT');
    this.log(`Issues Found: ${this.restartResults.issues.length}`, 'REPORT');
    
    if (this.restartResults.fixesVerified.length > 0) {
      this.log('\n✅ FIXES ACTIVE:', 'SUCCESS');
      this.restartResults.fixesVerified.forEach((fix, index) => {
        this.log(`${index + 1}. ${fix}`, 'SUCCESS');
      });
    }
    
    if (this.restartResults.issues.length > 0) {
      this.log('\n❌ ISSUES FOUND:', 'ERROR');
      this.restartResults.issues.forEach((issue, index) => {
        this.log(`${index + 1}. ${issue}`, 'ERROR');
      });
    }
    
    // Final assessment
    if (this.restartResults.applicationStatus === 'running' && this.restartResults.issues.length === 0) {
      this.log('\n🎉 SUCCESS: Application restarted with 1-hour termination fixes active!', 'SUCCESS');
      this.log('The 1-hour termination bug should now be resolved.', 'SUCCESS');
      
      this.log('\n📊 NEXT STEPS:', 'INFO');
      this.log('1. Start a test stream immediately', 'INFO');
      this.log('2. Monitor for at least 90 minutes (past the critical 1-hour mark)', 'INFO');
      this.log('3. Check logs/real-time-stream-monitor.log for termination events', 'INFO');
      this.log('4. If no termination at 60 minutes, the fix is successful!', 'INFO');
      
    } else {
      this.log('\n⚠️  WARNING: Issues detected during restart', 'WARNING');
      this.log('The 1-hour termination bug may still occur.', 'WARNING');
      
      if (this.restartResults.applicationStatus !== 'running') {
        this.log('\n🔧 MANUAL RESTART REQUIRED:', 'ERROR');
        this.log('1. Open terminal in StreamOnPod directory', 'ERROR');
        this.log('2. Run: npm run dev', 'ERROR');
        this.log('3. Wait for "Server running on" message', 'ERROR');
        this.log('4. Verify emergency fix messages appear in console', 'ERROR');
      }
    }
    
    this.log(`\n📄 Detailed log saved to: ${this.logFile}`, 'INFO');
  }

  async run() {
    try {
      this.log('Starting application restart process...', 'START');
      
      // Step 1: Verify fixes are in code
      const fixesPresent = await this.verifyFixesInCode();
      if (!fixesPresent) {
        this.log('🚨 CRITICAL: Emergency fixes not found in code!', 'CRITICAL');
        this.log('Please re-apply the emergency fixes before restarting.', 'CRITICAL');
        process.exit(1);
      }
      
      // Step 2: Kill existing processes
      await this.killExistingProcesses();
      
      // Step 3: Start application
      const started = await this.startApplication();
      
      // Step 4: Verify application is running
      if (started) {
        await this.verifyApplicationRunning();
      }
      
      // Step 5: Generate report
      await this.generateRestartReport();
      
    } catch (error) {
      this.log(`Restart process failed: ${error.message}`, 'ERROR');
      process.exit(1);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Restart process interrupted by user');
  process.exit(1);
});

// Run the restart process
const restart = new ApplicationRestart();
restart.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal restart error:', error);
  process.exit(1);
});
