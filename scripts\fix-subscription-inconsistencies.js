const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

console.log('🔧 StreamOnPod Subscription Data Migration Tool');
console.log('='.repeat(50));

async function main() {
  try {
    console.log('\n📊 Step 1: Analyzing current subscription inconsistencies...');
    
    // Get all inconsistencies
    const inconsistencies = await Subscription.getSubscriptionInconsistencies();
    
    if (inconsistencies.length === 0) {
      console.log('✅ No subscription inconsistencies found! All data is consistent.');
      process.exit(0);
    }
    
    console.log(`\n⚠️  Found ${inconsistencies.length} subscription inconsistencies:`);
    console.log('-'.repeat(80));
    
    // Group by inconsistency type
    const groupedInconsistencies = inconsistencies.reduce((acc, item) => {
      if (!acc[item.inconsistency_type]) {
        acc[item.inconsistency_type] = [];
      }
      acc[item.inconsistency_type].push(item);
      return acc;
    }, {});
    
    // Display summary
    for (const [type, items] of Object.entries(groupedInconsistencies)) {
      console.log(`\n${getInconsistencyIcon(type)} ${type.toUpperCase().replace('_', ' ')}: ${items.length} users`);
      
      // Show first few examples
      const examples = items.slice(0, 3);
      examples.forEach(item => {
        console.log(`   - ${item.username}: user_plan=${item.user_plan}(${item.user_slots} slots) vs subscription=${item.subscription_plan || 'none'}(${item.subscription_slots || 'none'} slots)`);
      });
      
      if (items.length > 3) {
        console.log(`   ... and ${items.length - 3} more users`);
      }
    }
    
    console.log('\n🔧 Step 2: Fixing subscription inconsistencies...');
    console.log('-'.repeat(50));
    
    // Ask for confirmation in production
    if (process.env.NODE_ENV === 'production') {
      console.log('⚠️  PRODUCTION MODE DETECTED');
      console.log('This script will modify user subscription data.');
      console.log('Please ensure you have a database backup before proceeding.');
      console.log('\nTo proceed, set CONFIRM_MIGRATION=true environment variable');
      
      if (process.env.CONFIRM_MIGRATION !== 'true') {
        console.log('❌ Migration cancelled. Set CONFIRM_MIGRATION=true to proceed.');
        process.exit(1);
      }
    }
    
    // Perform bulk fix
    const fixResult = await Subscription.fixAllSubscriptionInconsistencies();
    
    if (fixResult.success) {
      console.log(`\n✅ Successfully processed ${fixResult.totalProcessed} users:`);
      
      // Group results by action
      const resultsByAction = fixResult.results.reduce((acc, result) => {
        if (!acc[result.action]) {
          acc[result.action] = [];
        }
        acc[result.action].push(result);
        return acc;
      }, {});
      
      for (const [action, results] of Object.entries(resultsByAction)) {
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;
        
        console.log(`\n${getActionIcon(action)} ${action.toUpperCase().replace('_', ' ')}: ${successCount} successful, ${failCount} failed`);
        
        // Show failures if any
        const failures = results.filter(r => !r.success);
        if (failures.length > 0) {
          console.log('   Failures:');
          failures.forEach(failure => {
            console.log(`   ❌ ${failure.username}: ${failure.message}`);
          });
        }
      }
      
    } else {
      console.log(`❌ Bulk fix failed: ${fixResult.message}`);
      process.exit(1);
    }
    
    console.log('\n📊 Step 3: Verifying fixes...');
    console.log('-'.repeat(30));
    
    // Check for remaining inconsistencies
    const remainingInconsistencies = await Subscription.getSubscriptionInconsistencies();
    
    if (remainingInconsistencies.length === 0) {
      console.log('✅ All subscription inconsistencies have been resolved!');
    } else {
      console.log(`⚠️  ${remainingInconsistencies.length} inconsistencies still remain:`);
      remainingInconsistencies.forEach(item => {
        console.log(`   - ${item.username}: ${item.inconsistency_type}`);
      });
    }
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('='.repeat(50));
    
    // Generate summary report
    console.log('\n📋 MIGRATION SUMMARY REPORT:');
    console.log(`   • Initial inconsistencies: ${inconsistencies.length}`);
    console.log(`   • Users processed: ${fixResult.totalProcessed}`);
    console.log(`   • Remaining inconsistencies: ${remainingInconsistencies.length}`);
    console.log(`   • Success rate: ${Math.round(((inconsistencies.length - remainingInconsistencies.length) / inconsistencies.length) * 100)}%`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ Migration failed with error:', error);
    process.exit(1);
  }
}

function getInconsistencyIcon(type) {
  const icons = {
    'no_subscription': '🚫',
    'plan_mismatch': '🔄',
    'slots_mismatch': '🎰',
    'storage_mismatch': '💾'
  };
  return icons[type] || '⚠️';
}

function getActionIcon(action) {
  const icons = {
    'updated_user_to_preview': '👤',
    'synced_user_to_subscription': '🔄'
  };
  return icons[action] || '✅';
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Migration interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️  Migration terminated');
  process.exit(1);
});

// Run the migration
main();
