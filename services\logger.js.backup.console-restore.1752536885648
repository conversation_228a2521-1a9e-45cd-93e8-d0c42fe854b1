const fs = require('fs');
const path = require('path');
const util = require('util');

// Production logging configuration
const isProduction = process.env.NODE_ENV === 'production';
const logLevel = process.env.LOG_LEVEL || (isProduction ? 'error' : 'info');
const enableFileLogging = process.env.ENABLE_FILE_LOGGING !== 'false';
const enableConsoleLogging = process.env.ENABLE_CONSOLE_LOGGING !== 'false';

// Log levels (higher number = more verbose)
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3
};

const currentLogLevel = LOG_LEVELS[logLevel] || LOG_LEVELS.info;

// Setup log directory and files
const logDir = path.join(process.cwd(), 'logs');
const logFilePath = path.join(logDir, 'app.log');
const errorLogPath = path.join(logDir, 'error.log');

if (enableFileLogging && !fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Store original console methods
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleInfo = console.info;
const originalConsoleDebug = console.debug;

// Async file writing to avoid blocking
const writeQueue = [];
let isWriting = false;

async function processWriteQueue() {
  if (isWriting || writeQueue.length === 0) return;

  isWriting = true;
  const batch = writeQueue.splice(0, 10); // Process in batches

  try {
    for (const { filePath, content } of batch) {
      await fs.promises.appendFile(filePath, content);
    }
  } catch (err) {
    originalConsoleError('Failed to write to log file:', err);
  }

  isWriting = false;

  // Process remaining queue
  if (writeQueue.length > 0) {
    setImmediate(processWriteQueue);
  }
}

function shouldLog(level) {
  return LOG_LEVELS[level] <= currentLogLevel;
}

function sanitizeMessage(arg) {
  if (typeof arg === 'string') {
    // Remove sensitive data patterns
    return arg
      .replace(/password[=:]\s*[^\s]+/gi, 'password=***')
      .replace(/api[_-]?key[=:]\s*[^\s]+/gi, 'api_key=***')
      .replace(/token[=:]\s*[^\s]+/gi, 'token=***')
      .replace(/secret[=:]\s*[^\s]+/gi, 'secret=***');
  }
  return util.inspect(arg, { depth: 2, colors: false });
}

function writeToLogFile(level, filePath, ...args) {
  if (!enableFileLogging || !shouldLog(level)) return;

  const timestamp = new Date().toISOString();
  const message = args.map(sanitizeMessage).join(' ');
  const logEntry = `${timestamp} [${level.toUpperCase()}] ${message}\n`;

  writeQueue.push({ filePath, content: logEntry });
  setImmediate(processWriteQueue);
}

// Enhanced console methods with production optimizations
console.log = (...args) => {
  if (shouldLog('info')) {
    if (enableConsoleLogging && !isProduction) {
      originalConsoleLog.apply(console, args);
    }
    writeToLogFile('info', logFilePath, ...args);
  }
};

console.error = (...args) => {
  if (shouldLog('error')) {
    if (enableConsoleLogging) {
      originalConsoleError.apply(console, args);
    }
    writeToLogFile('error', errorLogPath, ...args);
    writeToLogFile('error', logFilePath, ...args);
  }
};

console.warn = (...args) => {
  if (shouldLog('warn')) {
    if (enableConsoleLogging && !isProduction) {
      originalConsoleWarn.apply(console, args);
    }
    writeToLogFile('warn', logFilePath, ...args);
  }
};

console.info = (...args) => {
  if (shouldLog('info')) {
    if (enableConsoleLogging && !isProduction) {
      originalConsoleInfo.apply(console, args);
    }
    writeToLogFile('info', logFilePath, ...args);
  }
};

console.debug = (...args) => {
  if (shouldLog('debug')) {
    if (enableConsoleLogging && !isProduction) {
      originalConsoleDebug.apply(console, args);
    }
    writeToLogFile('debug', logFilePath, ...args);
  }
};

// Log rotation function
function rotateLogFiles() {
  if (!enableFileLogging) return;

  const maxSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 5;

  [logFilePath, errorLogPath].forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        if (stats.size > maxSize) {
          // Rotate files
          for (let i = maxFiles - 1; i > 0; i--) {
            const oldFile = `${filePath}.${i}`;
            const newFile = `${filePath}.${i + 1}`;
            if (fs.existsSync(oldFile)) {
              if (i === maxFiles - 1) {
                fs.unlinkSync(oldFile); // Delete oldest
              } else {
                fs.renameSync(oldFile, newFile);
              }
            }
          }
          fs.renameSync(filePath, `${filePath}.1`);
        }
      }
    } catch (err) {
      originalConsoleError('Error rotating log file:', err);
    }
  });
}

// Setup log rotation interval (daily)
if (enableFileLogging) {
  setInterval(rotateLogFiles, 24 * 60 * 60 * 1000);
}

// Graceful shutdown
process.on('SIGINT', async () => {
  if (writeQueue.length > 0) {
    await processWriteQueue();
  }
});

process.on('SIGTERM', async () => {
  if (writeQueue.length > 0) {
    await processWriteQueue();
  }
});

if (shouldLog('info')) {
  // console.info(`Logger initialized - Level: ${logLevel}, Production: ${isProduction}, File: ${enableFileLogging}, Console: ${enableConsoleLogging}`); // Removed for production
}