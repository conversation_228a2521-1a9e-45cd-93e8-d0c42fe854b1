const { db } = require('../db/database');

console.log('🔧 StreamOnPod: Fix Duplicate Active Subscriptions');
console.log('='.repeat(60));

async function fixDuplicateSubscriptions() {
  try {
    console.log('\n📊 Step 1: Identifying users with duplicate active subscriptions...');
    
    // Get users with multiple active subscriptions
    const duplicateUsers = await new Promise((resolve, reject) => {
      const query = `
        SELECT 
          us.user_id,
          u.username,
          COUNT(*) as active_count,
          GROUP_CONCAT(us.id ORDER BY us.created_at DESC) as subscription_ids,
          GROUP_CONCAT(sp.name ORDER BY us.created_at DESC) as plan_names,
          GROUP_CONCAT(us.created_at ORDER BY us.created_at DESC) as created_dates
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active'
        GROUP BY us.user_id, u.username
        HAVING COUNT(*) > 1
        ORDER BY active_count DESC
      `;
      
      db.all(query, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (duplicateUsers.length === 0) {
      console.log('✅ No duplicate active subscriptions found!');
      process.exit(0);
    }
    
    console.log(`\n⚠️  Found ${duplicateUsers.length} users with duplicate active subscriptions:`);
    console.log('-'.repeat(80));
    
    for (const user of duplicateUsers) {
      console.log(`\n👤 User: ${user.username} (${user.active_count} active subscriptions)`);
      
      const subscriptionIds = user.subscription_ids.split(',');
      const planNames = user.plan_names.split(',');
      const createdDates = user.created_dates.split(',');
      
      console.log('   Subscriptions (newest first):');
      for (let i = 0; i < subscriptionIds.length; i++) {
        console.log(`   ${i + 1}. ID: ${subscriptionIds[i]}, Plan: ${planNames[i]}, Created: ${createdDates[i]}`);
      }
      
      // Keep the newest subscription, mark others as 'superseded'
      const newestSubscriptionId = subscriptionIds[0];
      const oldSubscriptionIds = subscriptionIds.slice(1);
      
      console.log(`   ✅ Keeping newest: ${newestSubscriptionId} (${planNames[0]})`);
      console.log(`   ❌ Marking as superseded: ${oldSubscriptionIds.join(', ')}`);
    }
    
    // Ask for confirmation in production
    if (process.env.NODE_ENV === 'production') {
      console.log('\n⚠️  PRODUCTION MODE DETECTED');
      console.log('This script will modify subscription data.');
      console.log('To proceed, set CONFIRM_DUPLICATE_FIX=true environment variable');
      
      if (process.env.CONFIRM_DUPLICATE_FIX !== 'true') {
        console.log('❌ Fix cancelled. Set CONFIRM_DUPLICATE_FIX=true to proceed.');
        process.exit(1);
      }
    }
    
    console.log('\n🔧 Step 2: Fixing duplicate subscriptions...');
    console.log('-'.repeat(50));
    
    let totalFixed = 0;
    let totalErrors = 0;
    
    for (const user of duplicateUsers) {
      try {
        const subscriptionIds = user.subscription_ids.split(',');
        const newestSubscriptionId = subscriptionIds[0];
        const oldSubscriptionIds = subscriptionIds.slice(1);
        
        // Mark old subscriptions as 'superseded'
        for (const oldId of oldSubscriptionIds) {
          await new Promise((resolve, reject) => {
            db.run(
              'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
              ['superseded', oldId],
              function (err) {
                if (err) reject(err);
                else resolve();
              }
            );
          });
          
          console.log(`   ✅ ${user.username}: Marked subscription ${oldId} as superseded`);
          totalFixed++;
        }
        
      } catch (error) {
        console.log(`   ❌ ${user.username}: Error fixing subscriptions - ${error.message}`);
        totalErrors++;
      }
    }
    
    console.log('\n📊 Step 3: Verification...');
    console.log('-'.repeat(30));
    
    // Verify no more duplicates exist
    const remainingDuplicates = await new Promise((resolve, reject) => {
      db.all(`
        SELECT user_id, COUNT(*) as count 
        FROM user_subscriptions 
        WHERE status = 'active' 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    if (remainingDuplicates.length === 0) {
      console.log('✅ All duplicate active subscriptions have been resolved!');
    } else {
      console.log(`⚠️  ${remainingDuplicates.length} users still have duplicate subscriptions`);
    }
    
    console.log('\n🎉 Fix completed successfully!');
    console.log('='.repeat(60));
    
    console.log('\n📋 SUMMARY:');
    console.log(`   • Users with duplicates: ${duplicateUsers.length}`);
    console.log(`   • Subscriptions fixed: ${totalFixed}`);
    console.log(`   • Errors encountered: ${totalErrors}`);
    console.log(`   • Remaining duplicates: ${remainingDuplicates.length}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ Fix failed with error:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Fix interrupted by user');
  process.exit(1);
});

// Run the fix
fixDuplicateSubscriptions();
