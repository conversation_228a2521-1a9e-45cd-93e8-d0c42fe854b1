<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Deletion Feedback Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        .ti {
            width: 1em;
            height: 1em;
            display: inline-block;
            vertical-align: middle;
        }
        .ti-check::before { content: "✓"; }
        .ti-trash::before { content: "🗑"; }
        .ti-alert-triangle::before { content: "⚠"; }
        .ti-x::before { content: "✕"; }
        .ti-info-circle::before { content: "ℹ"; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Video Deletion Feedback Test</h1>
        
        <!-- Test Video Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-4">
                <div class="aspect-video bg-gray-700 rounded mb-3 flex items-center justify-center">
                    <span class="text-gray-400">Video Thumbnail</span>
                </div>
                <h3 class="font-semibold mb-2">Test Video 1</h3>
                <div class="flex justify-end space-x-2">
                    <button class="text-gray-400 hover:text-red-400 p-1"
                            onclick="showDeleteDialog('test-video-1', 'Test Video 1')">
                        <i class="ti ti-trash text-sm"></i>
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-4">
                <div class="aspect-video bg-gray-700 rounded mb-3 flex items-center justify-center">
                    <span class="text-gray-400">Video Thumbnail</span>
                </div>
                <h3 class="font-semibold mb-2">Test Video 2 (Slow Response)</h3>
                <div class="flex justify-end space-x-2">
                    <button class="text-gray-400 hover:text-red-400 p-1"
                            onclick="showDeleteDialog('test-video-2-slow', 'Test Video 2 (Slow Response)')">
                        <i class="ti ti-trash text-sm"></i>
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-4">
                <div class="aspect-video bg-gray-700 rounded mb-3 flex items-center justify-center">
                    <span class="text-gray-400">Video Thumbnail</span>
                </div>
                <h3 class="font-semibold mb-2">Test Video 3 (Error)</h3>
                <div class="flex justify-end space-x-2">
                    <button class="text-gray-400 hover:text-red-400 p-1"
                            onclick="showDeleteDialog('test-video-3-error', 'Test Video 3 (Error)')">
                        <i class="ti ti-trash text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="test-results" class="space-y-2 text-sm">
                <p class="text-gray-400">Click on the delete buttons above to test the feedback mechanisms.</p>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="fixed top-16 right-4 bg-gray-800 text-white px-4 py-3 rounded-lg shadow-lg z-50 hidden flex items-center">
        <i id="toast-icon" class="mr-2"></i>
        <span id="toast-message"></span>
    </div>

    <script>
        // Mock API responses for testing
        const mockResponses = {
            'test-video-1': { success: true, delay: 1000 },
            'test-video-2-slow': { success: true, delay: 3000 },
            'test-video-3-error': { success: false, error: 'Video not found', delay: 1500 }
        };

        function logTest(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<p class="text-green-400">[${timestamp}] ${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }

        function showToast(type, message) {
            const toast = document.getElementById('toast');
            const toastIcon = document.getElementById('toast-icon');
            const toastMessage = document.getElementById('toast-message');
            
            if (type === 'success') {
                toastIcon.className = 'ti ti-check text-green-400 mr-2';
                toast.classList.add('border-l-4', 'border-green-400');
                toast.classList.remove('border-l-4', 'border-red-400', 'border-yellow-400');
            } else if (type === 'error') {
                toastIcon.className = 'ti ti-x text-red-400 mr-2';
                toast.classList.add('border-l-4', 'border-red-400');
                toast.classList.remove('border-l-4', 'border-green-400', 'border-yellow-400');
            } else if (type === 'warning') {
                toastIcon.className = 'ti ti-alert-triangle text-yellow-400 mr-2';
                toast.classList.add('border-l-4', 'border-yellow-400');
                toast.classList.remove('border-l-4', 'border-green-400', 'border-red-400', 'border-blue-400');
            } else if (type === 'info') {
                toastIcon.className = 'ti ti-info-circle text-blue-400 mr-2';
                toast.classList.add('border-l-4', 'border-blue-400');
                toast.classList.remove('border-l-4', 'border-green-400', 'border-red-400', 'border-yellow-400');
            }
            
            toastMessage.textContent = message;
            toast.classList.remove('hidden');
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        function createModalDialog(options) {
            const dialog = document.createElement('div');
            dialog.id = 'custom-modal';
            dialog.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm transition-all duration-300';
            
            const themes = {
                'danger': {
                    icon: options.icon || 'ti-alert-triangle',
                    color: 'text-red-400',
                    bg: 'bg-red-500/10',
                    border: 'border-gray-600/50',
                    button: 'bg-red-500 hover:bg-red-600',
                    buttonIcon: 'ti-trash'
                }
            };
            
            const theme = themes[options.type || 'info'];
            const confirmClass = options.confirmClass || theme.button;
            const buttonIcon = theme.buttonIcon || 'ti-check';
            const cancelText = options.cancelText || 'Cancel';
            const confirmText = options.confirmText || 'Confirm';

            dialog.innerHTML = `
                <div class="transform transition-all duration-300 opacity-0 scale-95 modal-content max-w-md w-full mx-4">
                    <div class="bg-gray-800 rounded-lg shadow-xl border ${theme.border} overflow-hidden">
                        <div class="px-6 py-5 flex items-center">
                            <div class="w-12 h-12 rounded-full ${theme.bg} flex items-center justify-center mr-4 shrink-0">
                                <i class="ti ${theme.icon} ${theme.color} text-2xl"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-medium text-white">${options.title}</h3>
                                <p class="text-gray-400 text-sm mt-1">${options.message}</p>
                            </div>
                        </div>
                        <div class="px-6 py-4 flex justify-end space-x-3 border-t border-gray-600/50">
                            <button id="modal-cancel-btn" class="px-4 py-2.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors text-sm font-medium flex items-center">
                                <i class="ti ti-x mr-1.5"></i>
                                ${cancelText}
                            </button>
                            <button id="modal-confirm-btn" class="${confirmClass} px-4 py-2.5 text-white rounded-lg transition-colors text-sm font-medium flex items-center">
                                <i class="ti ${buttonIcon} mr-1.5"></i>
                                ${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            document.body.classList.add('overflow-hidden');
            
            setTimeout(() => {
                const modalContent = dialog.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.classList.replace('opacity-0', 'opacity-100');
                    modalContent.classList.replace('scale-95', 'scale-100');
                }
            }, 10);
            
            return new Promise((resolve) => {
                let isProcessing = false;
                
                document.getElementById('modal-confirm-btn').addEventListener('click', (e) => {
                    if (isProcessing) {
                        e.preventDefault();
                        return;
                    }
                    isProcessing = true;
                    resolve({ confirmed: true, closeNow: false });
                });
                
                document.getElementById('modal-cancel-btn').addEventListener('click', (e) => {
                    if (isProcessing) {
                        e.preventDefault();
                        return;
                    }
                    closeModalWithAnimation();
                });
                
                function closeModalWithAnimation(confirmed = false) {
                    if (isProcessing && !confirmed) return;
                    
                    const modalContent = dialog.querySelector('.modal-content');
                    if (modalContent) {
                        modalContent.classList.replace('opacity-100', 'opacity-0');
                        modalContent.classList.replace('scale-100', 'scale-95');
                    }
                    setTimeout(() => {
                        document.body.classList.remove('overflow-hidden');
                        dialog.remove();
                        resolve({ confirmed, closeNow: true });
                    }, 200);
                }
            });
        }

        function setModalButtonState(buttonId, state, text) {
            const button = document.getElementById(buttonId);
            if (!button) return;
            
            if (!button.dataset.originalClasses) {
                button.dataset.originalClasses = button.className;
            }
            
            if (state === 'loading') {
                button.disabled = true;
                button.classList.add('opacity-75', 'cursor-not-allowed');
                button.innerHTML = `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg> ${text || 'Processing...'}`;
            } else if (state === 'success') {
                button.disabled = true;
                button.classList.remove('opacity-75', 'cursor-not-allowed');
                button.classList.add('bg-green-500', 'hover:bg-green-600');
                button.innerHTML = `<i class="ti ti-check mr-1.5"></i> ${text || 'Done'}`;
            } else if (state === 'error') {
                button.disabled = false;
                button.classList.remove('opacity-75', 'cursor-not-allowed');
                button.className = button.dataset.originalClasses || button.className;
                button.innerHTML = `<i class="ti ti-alert-triangle mr-1.5"></i> ${text || 'Try Again'}`;
            } else {
                button.disabled = false;
                button.classList.remove('opacity-75', 'cursor-not-allowed', 'bg-green-500', 'hover:bg-green-600');
                button.className = button.dataset.originalClasses || button.className;
                button.innerHTML = `<i class="ti ti-trash mr-1.5"></i> ${text || 'Delete'}`;
            }
        }

        async function showDeleteDialog(videoId, videoTitle) {
            logTest(`Starting deletion test for: ${videoTitle}`);
            
            const result = await createModalDialog({
                type: 'danger',
                icon: 'ti-alert-triangle',
                title: 'Delete Video',
                message: `Are you sure you want to delete "${videoTitle}"? This action cannot be undone.`,
                confirmText: 'Delete',
                cancelText: 'Cancel',
                confirmClass: 'bg-red-500 hover:bg-red-600'
            });
            
            if (result.confirmed) {
                const confirmButton = document.getElementById('modal-confirm-btn');
                const cancelButton = document.getElementById('modal-cancel-btn');
                
                try {
                    logTest(`User confirmed deletion, showing loading state...`);
                    
                    // Show loading state
                    setModalButtonState('modal-confirm-btn', 'loading', 'Deleting...');
                    if (cancelButton) {
                        cancelButton.disabled = true;
                        cancelButton.classList.add('opacity-50', 'cursor-not-allowed');
                    }
                    
                    // Simulate API call with mock response
                    const mockResponse = mockResponses[videoId];
                    await new Promise(resolve => setTimeout(resolve, mockResponse.delay));
                    
                    if (mockResponse.success) {
                        logTest(`API returned success, showing success state...`);
                        
                        // Show success state briefly
                        setModalButtonState('modal-confirm-btn', 'success', 'Deleted!');
                        showToast('success', 'Video deleted successfully');
                        
                        // Close modal and "reload" after a short delay
                        setTimeout(() => {
                            const modal = document.getElementById('custom-modal');
                            if (modal) {
                                const modalContent = modal.querySelector('.modal-content');
                                if (modalContent) {
                                    modalContent.classList.replace('opacity-100', 'opacity-0');
                                    modalContent.classList.replace('scale-100', 'scale-95');
                                }
                                setTimeout(() => {
                                    document.body.classList.remove('overflow-hidden');
                                    modal.remove();
                                    logTest(`Modal closed, deletion process completed successfully`);
                                }, 200);
                            }
                        }, 1000);
                    } else {
                        logTest(`API returned error: ${mockResponse.error}`);
                        
                        // Reset button state on error
                        setModalButtonState('modal-confirm-btn', 'normal', 'Delete');
                        if (cancelButton) {
                            cancelButton.disabled = false;
                            cancelButton.classList.remove('opacity-50', 'cursor-not-allowed');
                        }
                        showToast('error', mockResponse.error || 'Failed to delete video');
                    }
                } catch (error) {
                    logTest(`Exception occurred: ${error.message}`);
                    
                    // Reset button state on error
                    setModalButtonState('modal-confirm-btn', 'normal', 'Delete');
                    if (cancelButton) {
                        cancelButton.disabled = false;
                        cancelButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                    showToast('error', 'An error occurred while deleting the video');
                }
            } else {
                logTest(`User cancelled deletion`);
            }
        }
    </script>
</body>
</html>
