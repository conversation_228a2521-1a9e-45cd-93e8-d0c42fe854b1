#!/usr/bin/env node

/**
 * Check Current Streams Status
 * Quick database query to check active streams and recent terminations
 */

const { db } = require('./db/database');

console.log('🔍 Checking Current Stream Status');
console.log('=================================\n');

async function checkCurrentStreams() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT id, title, status, start_time, duration, created_at, updated_at
      FROM streams 
      WHERE status = 'live' OR updated_at > datetime('now', '-2 hours')
      ORDER BY updated_at DESC
      LIMIT 20
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

async function checkRecentTerminations() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT id, title, status, start_time, duration, created_at, updated_at
      FROM streams 
      WHERE status IN ('offline', 'error') 
        AND updated_at > datetime('now', '-4 hours')
        AND start_time IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT 10
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

async function main() {
  try {
    console.log('📊 CURRENT ACTIVE STREAMS:');
    console.log('==========================');
    
    const currentStreams = await checkCurrentStreams();
    
    if (currentStreams.length === 0) {
      console.log('❌ No active streams found');
    } else {
      currentStreams.forEach((stream, index) => {
        const startTime = stream.start_time ? new Date(stream.start_time) : null;
        const updatedTime = new Date(stream.updated_at);
        const runningTime = startTime ? Math.floor((new Date() - startTime) / 1000 / 60) : 0;
        
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Duration Limit: ${stream.duration || 'None'} minutes`);
        if (startTime) {
          console.log(`   Started: ${startTime.toLocaleString()}`);
          console.log(`   Running Time: ${runningTime} minutes (${Math.floor(runningTime/60)}h ${runningTime%60}m)`);
        }
        console.log(`   Last Updated: ${updatedTime.toLocaleString()}`);
        console.log('');
      });
    }
    
    console.log('\n🚨 RECENT TERMINATIONS (Last 4 hours):');
    console.log('======================================');
    
    const recentTerminations = await checkRecentTerminations();
    
    if (recentTerminations.length === 0) {
      console.log('✅ No recent terminations found');
    } else {
      recentTerminations.forEach((stream, index) => {
        const startTime = stream.start_time ? new Date(stream.start_time) : null;
        const updatedTime = new Date(stream.updated_at);
        const runningTime = startTime && stream.updated_at ? 
          Math.floor((updatedTime - startTime) / 1000 / 60) : 0;
        
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Final Status: ${stream.status}`);
        console.log(`   Duration Limit: ${stream.duration || 'None'} minutes`);
        if (startTime) {
          console.log(`   Started: ${startTime.toLocaleString()}`);
          console.log(`   Terminated: ${updatedTime.toLocaleString()}`);
          console.log(`   Total Runtime: ${runningTime} minutes (${Math.floor(runningTime/60)}h ${runningTime%60}m)`);
          
          // Check if this was a ~1 hour termination
          if (runningTime >= 55 && runningTime <= 65) {
            console.log(`   🚨 CRITICAL: ~1 hour termination detected!`);
          }
          
          // Check if this was a long-running stream
          if (runningTime >= 480) { // 8+ hours
            console.log(`   🎉 SUCCESS: Long-running stream (${Math.floor(runningTime/60)} hours)!`);
          }
        }
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking streams:', error.message);
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
