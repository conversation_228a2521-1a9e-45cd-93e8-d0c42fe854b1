/**
 * Debug script to test the quota middleware and identify the 403 error cause
 */

const OptimizedQuotaMiddleware = require('./middleware/optimized-quota-middleware');

async function debugQuotaMiddleware(userId) {
  try {
    console.log(`🔍 Debugging quota middleware for user: ${userId}`);
    
    // Test admin check
    console.log('\n1. Testing admin check...');
    const isAdmin = await OptimizedQuotaMiddleware.checkAdminStatus(userId);
    console.log(`   Admin status: ${isAdmin}`);
    
    if (isAdmin) {
      console.log('✅ User is admin - should bypass all restrictions');
      return;
    }
    
    // Test combined user quota data
    console.log('\n2. Testing combined user quota data...');
    const userQuotaData = await OptimizedQuotaMiddleware.getCombinedUserQuotaData(userId);
    console.log('   User quota data:', JSON.stringify(userQuotaData, null, 2));
    
    // Test subscription validation
    console.log('\n3. Testing subscription validation...');
    try {
      OptimizedQuotaMiddleware.validateSubscription(userQuotaData);
      console.log('✅ Subscription validation passed');
    } catch (error) {
      console.log('❌ Subscription validation failed:', error.message);
      throw error;
    }
    
    // Test quota limits validation
    console.log('\n4. Testing quota limits validation...');
    try {
      OptimizedQuotaMiddleware.validateQuotaLimits(userQuotaData);
      console.log('✅ Quota limits validation passed');
    } catch (error) {
      console.log('❌ Quota limits validation failed:', error.message);
      console.log('   Error details:', error.details);
      throw error;
    }
    
    console.log('\n✅ All validations passed! User should be able to create streams.');
    
  } catch (error) {
    console.log('\n❌ Validation failed with error:', error.message);
    console.log('   Error type:', error.constructor.name);
    console.log('   Error details:', error.details);
    
    // Analyze the error
    if (error.message.includes('subscription')) {
      console.log('\n🔍 Analysis: Subscription-related error');
      console.log('   - Check if user has active subscription');
      console.log('   - Check if subscription has expired');
      console.log('   - Verify Preview plan handling');
    } else if (error.message.includes('limit') || error.message.includes('quota')) {
      console.log('\n🔍 Analysis: Quota-related error');
      console.log('   - Check current vs max streaming slots');
      console.log('   - Verify Preview plan streaming restrictions');
      console.log('   - Check stream count calculation');
    }
  }
}

// Test with a sample user ID
async function runDebugTests() {
  console.log('🚀 Starting Quota Middleware Debug Tests');
  console.log('==========================================');
  
  // You can replace this with an actual user ID from your database
  const testUserId = 'test-user-id';
  
  try {
    // First, let's check if we can get user info directly from database
    const { db } = require('./db/database');
    
    console.log('📋 Getting user information from database...');
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT id, username, plan_type, max_streaming_slots FROM users LIMIT 5', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    console.log('Available users for testing:');
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ID: ${user.id}, Username: ${user.username}, Plan: ${user.plan_type}, Slots: ${user.max_streaming_slots}`);
    });
    
    if (users.length > 0) {
      const firstUser = users[0];
      console.log(`\n🧪 Testing with user: ${firstUser.username} (${firstUser.id})`);
      await debugQuotaMiddleware(firstUser.id);
    } else {
      console.log('❌ No users found in database');
    }
    
  } catch (error) {
    console.error('❌ Debug test failed:', error);
  }
}

// Run the debug tests
if (require.main === module) {
  runDebugTests().catch(error => {
    console.error('❌ Debug script error:', error);
    process.exit(1);
  });
}

module.exports = { debugQuotaMiddleware };
