#!/usr/bin/env node

/**
 * Final PodLite Test
 * Comprehensive test of the reported issue scenario
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');

async function runFinalTest() {
  try {
    console.log('🎯 StreamOnPod: Final PodLite Issue Test');
    console.log('='.repeat(50));
    console.log(`Time: ${new Date().toISOString()}\n`);

    // Test the exact scenario reported: PodLite user with 0 streams trying to create a stream
    console.log('📋 Testing Reported Issue Scenario');
    console.log('-'.repeat(40));
    console.log('Scenario: PodLite user with 0 streams gets "Streaming Limit Reached" error');
    console.log('Expected: User should be able to create 1 stream');
    console.log('');

    // Get our test user
    const testUser = await new Promise((resolve, reject) => {
      db.get('SELECT id, username FROM users WHERE username = ?', ['podlite_test_user'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!testUser) {
      console.log('❌ Test user not found. Run create-test-user.js first.');
      process.exit(1);
    }

    console.log(`🧪 Testing with user: ${testUser.username}`);

    // Verify user has PodLite subscription
    const subscription = await Subscription.getUserSubscription(testUser.id);
    if (!subscription || subscription.plan_name !== 'PodLite') {
      console.log('❌ Test user does not have active PodLite subscription');
      process.exit(1);
    }

    console.log(`✅ User has active ${subscription.plan_name} subscription`);

    // Check current stream count
    const streamCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [testUser.id], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    console.log(`📊 Current streams: ${streamCount}/${subscription.max_streaming_slots}`);

    // Test quota check - this is what the middleware uses
    console.log('\n🔍 Testing Quota Check (Middleware Logic)');
    console.log('-'.repeat(40));

    const quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    
    console.log('Quota Check Results:');
    console.log(`  - Has Limit: ${quotaCheck.hasLimit}`);
    console.log(`  - Max Slots: ${quotaCheck.maxSlots}`);
    console.log(`  - Current Slots: ${quotaCheck.currentSlots}`);
    console.log(`  - Plan Source: ${quotaCheck.planSource}`);
    console.log(`  - Subscription ID: ${quotaCheck.subscriptionId}`);

    // Determine if this would trigger the error
    if (quotaCheck.hasLimit) {
      console.log('\n❌ ISSUE REPRODUCED!');
      console.log('User would receive "Streaming Limit Reached" error');
      
      const errorMessage = quotaCheck.maxSlots === 0
        ? 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.'
        : `You have reached your streaming limit of ${quotaCheck.maxSlots} concurrent streams. Please upgrade your plan or stop an existing stream.`;
      
      console.log(`Error message: "${errorMessage}"`);
      
      // This is the bug - investigate why
      console.log('\n🔍 Investigating Root Cause:');
      console.log(`Expected: hasLimit should be false (${streamCount} < ${subscription.max_streaming_slots})`);
      console.log(`Actual: hasLimit is true`);
      
      // Check the logic
      const expectedHasLimit = subscription.max_streaming_slots !== -1 && streamCount >= subscription.max_streaming_slots;
      console.log(`Logic check: ${subscription.max_streaming_slots} !== -1 && ${streamCount} >= ${subscription.max_streaming_slots} = ${expectedHasLimit}`);
      
      if (quotaCheck.hasLimit !== expectedHasLimit) {
        console.log('🐛 BUG CONFIRMED: Quota logic is incorrect');
      }
      
    } else {
      console.log('\n✅ NO ISSUE FOUND');
      console.log('User can create streams as expected');
      console.log('The reported issue is not currently occurring');
    }

    // Test with different stream counts
    console.log('\n🧪 Testing Edge Cases');
    console.log('-'.repeat(40));

    // Test what happens if we simulate having 1 stream
    console.log('Simulating user with 1 stream (at limit):');
    
    // Create a temporary stream for testing
    const { v4: uuidv4 } = require('uuid');
    const tempStreamId = uuidv4();
    
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [tempStreamId, testUser.id, 'Test Stream', 'offline', 'rtmp://test.example.com/live', 'test_key_123'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    const quotaCheckWithStream = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - With 1 stream: hasLimit=${quotaCheckWithStream.hasLimit} (${quotaCheckWithStream.currentSlots}/${quotaCheckWithStream.maxSlots})`);

    // Clean up test stream
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE id = ?', [tempStreamId], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    const quotaCheckAfterCleanup = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`  - After cleanup: hasLimit=${quotaCheckAfterCleanup.hasLimit} (${quotaCheckAfterCleanup.currentSlots}/${quotaCheckAfterCleanup.maxSlots})`);

    // Final assessment
    console.log('\n📊 FINAL ASSESSMENT');
    console.log('='.repeat(50));

    if (quotaCheckAfterCleanup.hasLimit && quotaCheckAfterCleanup.currentSlots < quotaCheckAfterCleanup.maxSlots) {
      console.log('❌ ISSUE CONFIRMED: PodLite users with available slots are being blocked');
      console.log('\nRECOMMENDED ACTIONS:');
      console.log('1. Check for data integrity issues in subscription tables');
      console.log('2. Verify quota calculation logic in checkStreamingSlotLimit method');
      console.log('3. Check for duplicate subscriptions or corrupted data');
      console.log('4. Review recent changes to subscription or quota logic');
    } else {
      console.log('✅ NO ISSUE FOUND: PodLite streaming limits are working correctly');
      console.log('\nPOSSIBLE EXPLANATIONS FOR REPORTED ISSUE:');
      console.log('1. Issue was already fixed (as documented in PODLITE_STREAMING_LIMIT_FIX.md)');
      console.log('2. Issue was specific to certain users (duplicate subscriptions)');
      console.log('3. Issue was temporary and has been resolved');
      console.log('4. User may have been at their actual limit (1/1 streams)');
    }

    console.log('\n' + '='.repeat(50));

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Test interrupted by user');
  process.exit(1);
});

// Run the test
runFinalTest().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
