/**
 * Stream Creation 400 Error Diagnostic Tool
 * This script helps identify the exact cause of HTTP 400 Bad Request errors
 * when creating streams in StreamOnPod application
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const DIAGNOSTIC_CONFIG = {
  // Test user credentials
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  }
};

class StreamCreationDiagnostic {
  constructor() {
    this.sessionCookie = null;
    this.userInfo = null;
  }

  async login() {
    try {
      console.log('🔐 Attempting to login...');
      const response = await axios.post(`${BASE_URL}/login`, {
        email: DIAGNOSTIC_CONFIG.testUser.email,
        password: DIAGNOSTIC_CONFIG.testUser.password
      }, {
        withCredentials: true,
        validateStatus: () => true
      });

      if (response.status === 200 && response.headers['set-cookie']) {
        this.sessionCookie = response.headers['set-cookie'];
        console.log('✅ Login successful');
        return true;
      } else {
        console.log('❌ Login failed:', response.status, response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ Login error:', error.message);
      return false;
    }
  }

  async getUserInfo() {
    try {
      console.log('👤 Fetching user information...');
      const response = await axios.get(`${BASE_URL}/api/user/profile`, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || ''
        },
        withCredentials: true,
        validateStatus: () => true
      });

      if (response.status === 200) {
        this.userInfo = response.data;
        console.log('✅ User info retrieved');
        console.log('   Plan Type:', this.userInfo.plan_type || 'Unknown');
        console.log('   Max Streaming Slots:', this.userInfo.max_streaming_slots || 'Unknown');
        console.log('   Max Storage GB:', this.userInfo.max_storage_gb || 'Unknown');
        return true;
      } else {
        console.log('❌ Failed to get user info:', response.status, response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ User info error:', error.message);
      return false;
    }
  }

  async checkSubscriptionStatus() {
    try {
      console.log('📋 Checking subscription status...');
      const response = await axios.get(`${BASE_URL}/api/subscription/status`, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || ''
        },
        withCredentials: true,
        validateStatus: () => true
      });

      console.log('📊 Subscription Status Response:');
      console.log('   Status Code:', response.status);
      console.log('   Response:', JSON.stringify(response.data, null, 2));
      
      return response.status === 200;
    } catch (error) {
      console.error('❌ Subscription check error:', error.message);
      return false;
    }
  }

  async testStreamCreationScenarios() {
    console.log('\n🧪 Testing Stream Creation Scenarios');
    console.log('=====================================');

    const testCases = [
      {
        name: 'Valid Basic Stream',
        data: {
          streamTitle: 'Test Stream',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890'
        }
      },
      {
        name: 'Missing Title',
        data: {
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890'
        }
      },
      {
        name: 'Empty Title',
        data: {
          streamTitle: '',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890'
        }
      },
      {
        name: 'Missing RTMP URL',
        data: {
          streamTitle: 'Test Stream',
          streamKey: 'test-stream-key-12345678901234567890'
        }
      },
      {
        name: 'Invalid RTMP URL',
        data: {
          streamTitle: 'Test Stream',
          rtmpUrl: 'http://invalid-url.com',
          streamKey: 'test-stream-key-12345678901234567890'
        }
      },
      {
        name: 'Missing Stream Key',
        data: {
          streamTitle: 'Test Stream',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2'
        }
      },
      {
        name: 'Invalid Stream Key',
        data: {
          streamTitle: 'Test Stream',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test'
        }
      },
      {
        name: 'With Advanced Settings',
        data: {
          streamTitle: 'Test Stream Advanced',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890',
          useAdvancedSettings: true,
          bitrate: 5000,
          resolution: '1920x1080',
          fps: 60
        }
      },
      {
        name: 'With Schedule Time (Past)',
        data: {
          streamTitle: 'Test Stream Scheduled',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890',
          scheduleTime: '2023-01-01T12:00:00'
        }
      },
      {
        name: 'With Schedule Time (Future)',
        data: {
          streamTitle: 'Test Stream Scheduled Future',
          rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
          streamKey: 'test-stream-key-12345678901234567890',
          scheduleTime: new Date(Date.now() + 2 * 60 * 1000).toISOString().slice(0, 19) // 2 minutes from now
        }
      }
    ];

    for (const testCase of testCases) {
      await this.testSingleStreamCreation(testCase.name, testCase.data);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
    }
  }

  async testSingleStreamCreation(testName, streamData) {
    try {
      console.log(`\n🔬 Testing: ${testName}`);
      console.log('   Data:', JSON.stringify(streamData, null, 2));

      const response = await axios.post(`${BASE_URL}/api/streams`, streamData, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || '',
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        validateStatus: () => true
      });

      console.log('📊 Result:');
      console.log('   Status Code:', response.status);
      console.log('   Response:', JSON.stringify(response.data, null, 2));

      // Analyze the error
      if (response.status === 400) {
        this.analyzeError(response.data, testName);
      } else if (response.status === 403) {
        console.log('   🚫 Authorization/Quota Error - Check subscription/plan limits');
      } else if (response.status === 200) {
        console.log('   ✅ Success - Stream created');
        // Clean up created stream
        if (response.data.stream && response.data.stream.id) {
          await this.cleanupStream(response.data.stream.id);
        }
      }

    } catch (error) {
      console.error(`   ❌ Request Error: ${error.message}`);
    }
  }

  analyzeError(errorData, testName) {
    console.log('   🔍 Error Analysis:');
    
    if (errorData.error) {
      const errorMessage = errorData.error;
      
      // Categorize the error
      if (errorMessage.includes('required')) {
        console.log('   📝 Category: Missing Required Field');
      } else if (errorMessage.includes('RTMP')) {
        console.log('   🌐 Category: RTMP Configuration Error');
      } else if (errorMessage.includes('stream key')) {
        console.log('   🔑 Category: Stream Key Validation Error');
      } else if (errorMessage.includes('Video')) {
        console.log('   🎥 Category: Video Validation Error');
      } else if (errorMessage.includes('Schedule')) {
        console.log('   ⏰ Category: Schedule Time Validation Error');
      } else if (errorMessage.includes('Advanced settings')) {
        console.log('   ⚙️ Category: Advanced Settings Permission Error');
      } else {
        console.log('   ❓ Category: Unknown Error Type');
      }
      
      console.log('   💬 Error Message:', errorMessage);
    }

    if (errorData.details) {
      console.log('   📋 Error Details:', JSON.stringify(errorData.details, null, 2));
    }
  }

  async cleanupStream(streamId) {
    try {
      await axios.delete(`${BASE_URL}/api/streams/${streamId}`, {
        headers: {
          'Cookie': this.sessionCookie?.join('; ') || ''
        },
        withCredentials: true,
        validateStatus: () => true
      });
      console.log('   🧹 Test stream cleaned up');
    } catch (error) {
      console.log('   ⚠️ Cleanup warning:', error.message);
    }
  }

  async runDiagnostics() {
    console.log('🚀 Stream Creation 400 Error Diagnostics');
    console.log('=========================================');

    // Step 1: Login
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without login');
      return;
    }

    // Step 2: Get user info
    await this.getUserInfo();

    // Step 3: Check subscription status
    await this.checkSubscriptionStatus();

    // Step 4: Test various stream creation scenarios
    await this.testStreamCreationScenarios();

    console.log('\n🏁 Diagnostics Complete');
    console.log('=========================================');
    console.log('💡 Common 400 Error Causes:');
    console.log('   1. Missing required fields (streamTitle, rtmpUrl, streamKey)');
    console.log('   2. Invalid RTMP URL format');
    console.log('   3. Invalid or duplicate stream key');
    console.log('   4. Schedule time in the past');
    console.log('   5. Advanced settings without proper plan');
    console.log('   6. Video processing not completed');
    console.log('   7. Subscription/quota validation failures');
  }
}

// Run diagnostics
if (require.main === module) {
  const diagnostic = new StreamCreationDiagnostic();
  diagnostic.runDiagnostics().catch(error => {
    console.error('❌ Diagnostic error:', error);
    process.exit(1);
  });
}

module.exports = StreamCreationDiagnostic;
