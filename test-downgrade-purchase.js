#!/usr/bin/env node

/**
 * Test script to simulate downgrade purchase and identify issues
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const User = require('./models/User');

async function testDowngradePurchase() {
  console.log('🧪 Testing Downgrade Purchase Flow...\n');

  try {
    // Step 1: Find a user with an expired subscription
    console.log('🔍 Looking for users with expired subscriptions...');
    const expiredSub = await new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, u.username, u.plan_type, sp.name as plan_name, sp.price
        FROM user_subscriptions us
        JOIN users u ON us.user_id = u.id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE (us.status = 'expired' OR us.status = 'cancelled' OR us.end_date < datetime('now'))
        AND sp.name != 'Preview'
        AND sp.price > 0
        ORDER BY us.updated_at DESC
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!expiredSub) {
      console.log('❌ No expired paid subscriptions found to test with');
      return;
    }

    console.log('📋 Found expired subscription:');
    console.log(`   User: ${expiredSub.username} (${expiredSub.user_id})`);
    console.log(`   Expired Plan: ${expiredSub.plan_name} (${expiredSub.price} IDR)`);
    console.log(`   Current User Plan: ${expiredSub.plan_type}`);
    console.log(`   Status: ${expiredSub.status}\n`);

    // Step 2: Test the hasExpiredSubscription method
    console.log('🔍 Testing hasExpiredSubscription method...');
    const expiredInfo = await Subscription.hasExpiredSubscription(expiredSub.user_id);
    console.log('   Result:', expiredInfo);

    // Step 3: Test getUserSubscription (should return null for expired)
    console.log('\n🔍 Testing getUserSubscription...');
    const activeSubscription = await Subscription.getUserSubscription(expiredSub.user_id);
    console.log(`   Active subscription: ${activeSubscription ? activeSubscription.plan_name : 'null'}`);

    // Step 4: Get available plans for downgrade
    console.log('\n📋 Getting available plans...');
    const plans = await Subscription.getAllPlans();
    const lowerPricedPlans = plans.filter(plan => 
      plan.price > 0 && 
      plan.price < expiredSub.price && 
      plan.name !== 'Preview'
    );

    if (lowerPricedPlans.length === 0) {
      console.log('❌ No lower-priced plans available for testing');
      return;
    }

    const targetPlan = lowerPricedPlans[0];
    console.log(`   Target downgrade plan: ${targetPlan.name} (${targetPlan.price} IDR)`);

    // Step 5: Simulate the subscription validation logic
    console.log('\n🔍 Simulating subscription validation...');
    
    const user = await User.findById(expiredSub.user_id);
    const currentPlan = await Subscription.getPlanByName(user.plan_type);
    
    console.log(`   User plan type: ${user.plan_type}`);
    console.log(`   Current plan: ${currentPlan ? currentPlan.name : 'null'}`);
    console.log(`   Current plan price: ${currentPlan ? currentPlan.price : 'null'}`);
    console.log(`   Target plan price: ${targetPlan.price}`);
    console.log(`   Has expired subscription: ${expiredInfo.hasExpired}`);

    // Check validation conditions
    const isDowngrade = targetPlan.price > 0 && currentPlan && currentPlan.price > 0 && targetPlan.price < currentPlan.price;
    const shouldAllowDowngrade = expiredInfo.hasExpired;
    
    console.log(`   Is downgrade: ${isDowngrade}`);
    console.log(`   Should allow downgrade: ${shouldAllowDowngrade}`);
    
    if (isDowngrade && !shouldAllowDowngrade) {
      console.log('   ❌ Validation would BLOCK downgrade');
    } else {
      console.log('   ✅ Validation would ALLOW downgrade');
    }

    // Check existing subscription validation
    const existingSubscription = await Subscription.getUserSubscription(expiredSub.user_id);
    console.log(`   Existing active subscription: ${existingSubscription ? 'YES' : 'NO'}`);
    
    if (existingSubscription && !expiredInfo.hasExpired) {
      console.log('   ❌ Would be blocked by existing subscription check');
    } else {
      console.log('   ✅ Would pass existing subscription check');
    }

    // Check Preview plan validation
    const isPreviewToPreview = user.plan_type === 'Preview' && targetPlan.name === 'Preview' && !expiredInfo.hasExpired;
    console.log(`   Preview to Preview check: ${isPreviewToPreview ? 'BLOCKED' : 'ALLOWED'}`);

    // Step 6: Test subscription creation (dry run)
    console.log('\n📝 Testing subscription creation (dry run)...');
    
    try {
      // Calculate end date
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30);
      
      console.log('   Subscription parameters:');
      console.log(`     user_id: ${expiredSub.user_id}`);
      console.log(`     plan_id: ${targetPlan.id}`);
      console.log(`     status: active`);
      console.log(`     end_date: ${endDate.toISOString()}`);
      console.log(`     payment_method: midtrans`);
      
      // Don't actually create the subscription, just validate the parameters
      console.log('   ✅ Subscription parameters look valid');
      
    } catch (error) {
      console.log('   ❌ Subscription creation would fail:', error.message);
    }

    // Step 7: Summary
    console.log('\n✅ DOWNGRADE PURCHASE TEST SUMMARY:');
    console.log(`   - Found expired user: ✅`);
    console.log(`   - hasExpiredSubscription working: ${expiredInfo.hasExpired ? '✅' : '❌'}`);
    console.log(`   - getUserSubscription returns null: ${!activeSubscription ? '✅' : '❌'}`);
    console.log(`   - Downgrade validation: ${isDowngrade && shouldAllowDowngrade ? '✅' : '❌'}`);
    console.log(`   - Existing subscription check: ${!existingSubscription || expiredInfo.hasExpired ? '✅' : '❌'}`);
    console.log(`   - Preview plan check: ${!isPreviewToPreview ? '✅' : '❌'}`);
    
    const allChecksPass = expiredInfo.hasExpired && 
                         !activeSubscription && 
                         (isDowngrade && shouldAllowDowngrade) && 
                         (!existingSubscription || expiredInfo.hasExpired) && 
                         !isPreviewToPreview;
    
    if (allChecksPass) {
      console.log('\n🎉 All validation checks pass! Downgrade should work.');
    } else {
      console.log('\n⚠️ Some validation checks failed. This might cause the downgrade error.');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testDowngradePurchase().then(() => {
    console.log('\n🏁 Test completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testDowngradePurchase };
