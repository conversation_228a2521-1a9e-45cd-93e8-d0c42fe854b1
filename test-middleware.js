/**
 * Test script to verify the optimized quota middleware works correctly
 */

try {
  console.log('Testing optimized quota middleware...');
  
  // Test importing the middleware
  const OptimizedQuotaMiddleware = require('./middleware/optimized-quota-middleware');
  console.log('✅ Middleware imported successfully');
  
  // Test that all required methods exist
  const requiredMethods = [
    'checkValidSubscription',
    'checkStreamingQuota', 
    'checkStorageQuota',
    'checkActiveAccount',
    'checkSubscriptionAndQuota',
    'getUserQuotaInfo',
    'updateStorageUsage',
    'getGoogleDriveEligiblePlans',
    'getAdvancedSettingsEligiblePlans',
    'checkSubscription',
    'syncUserStorageCache',
    'syncAllUsersStorageCache',
    'clearUserCache',
    'clearAllCache'
  ];
  
  console.log('\nChecking required methods:');
  for (const method of requiredMethods) {
    if (typeof OptimizedQuotaMiddleware[method] === 'function') {
      console.log(`✅ ${method} - exists`);
    } else {
      console.log(`❌ ${method} - missing`);
    }
  }
  
  console.log('\n✅ All tests passed! Middleware is ready to use.');
  
} catch (error) {
  console.error('❌ Error testing middleware:', error.message);
  console.error(error.stack);
  process.exit(1);
}
