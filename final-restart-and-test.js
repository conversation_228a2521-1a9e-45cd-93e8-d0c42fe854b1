#!/usr/bin/env node

/**
 * Final Application Restart and 1-Hour Termination Test
 * Restarts the application with ALL fixes and monitors for termination
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 FINAL: StreamOnPod Restart with Complete 1-Hour Termination Fix');
console.log('================================================================\n');

class FinalRestartAndTest {
  constructor() {
    this.logFile = path.join(__dirname, 'logs', 'final-restart-test.log');
    this.testResults = {
      startTime: new Date(),
      applicationRestarted: false,
      emergencyMessagesDetected: [],
      testStreamStarted: false,
      monitoringActive: false
    };
    
    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${type}] ${message}`;
    console.log(logEntry);
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async killExistingProcesses() {
    this.log('Terminating existing Node.js processes...', 'INFO');
    
    return new Promise((resolve) => {
      exec('taskkill /f /im node.exe', (error, stdout, stderr) => {
        if (error && !error.message.includes('not found')) {
          this.log(`Process termination warning: ${error.message}`, 'WARNING');
        } else {
          this.log('Existing processes terminated', 'SUCCESS');
        }
        
        // Wait a moment for processes to fully terminate
        setTimeout(resolve, 2000);
      });
    });
  }

  async startApplicationWithMonitoring() {
    this.log('Starting StreamOnPod application with monitoring...', 'INFO');
    
    return new Promise((resolve) => {
      // Start the application
      const appProcess = spawn('npm', ['run', 'dev'], {
        detached: false,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: true
      });
      
      let emergencyFixesDetected = 0;
      const requiredEmergencyMessages = [
        'EMERGENCY: Periodic health checks DISABLED',
        'EMERGENCY: Health check disabled to prevent automatic stream terminations',
        'EMERGENCY: Long-duration health monitoring DISABLED',
        'EMERGENCY: File handle health monitoring DISABLED'
      ];
      
      appProcess.stdout.on('data', (data) => {
        const output = data.toString();
        
        // Check for emergency fix messages
        requiredEmergencyMessages.forEach(message => {
          if (output.includes(message) && !this.testResults.emergencyMessagesDetected.includes(message)) {
            this.testResults.emergencyMessagesDetected.push(message);
            emergencyFixesDetected++;
            this.log(`🔧 EMERGENCY FIX ACTIVE: ${message}`, 'SUCCESS');
          }
        });
        
        // Check if server started
        if (output.includes('Server running on') || output.includes('listening on')) {
          this.log('✅ Application started successfully', 'SUCCESS');
          this.testResults.applicationRestarted = true;
          
          // Check if all emergency fixes are active
          if (emergencyFixesDetected >= 3) {
            this.log(`🎉 ALL ${emergencyFixesDetected} EMERGENCY FIXES ACTIVE!`, 'SUCCESS');
            resolve(true);
          } else {
            this.log(`⚠️  Only ${emergencyFixesDetected}/4 emergency fixes detected`, 'WARNING');
            resolve(true);
          }
        }
        
        // Log other important messages
        if (output.includes('StreamingService') || output.includes('EMERGENCY')) {
          this.log(`APP: ${output.trim()}`, 'INFO');
        }
      });
      
      appProcess.stderr.on('data', (data) => {
        const errorOutput = data.toString();
        if (!errorOutput.includes('DeprecationWarning')) {
          this.log(`STDERR: ${errorOutput.trim()}`, 'WARNING');
        }
      });
      
      appProcess.on('error', (error) => {
        this.log(`Failed to start application: ${error.message}`, 'ERROR');
        resolve(false);
      });
      
      // Timeout after 45 seconds
      setTimeout(() => {
        if (!this.testResults.applicationRestarted) {
          this.log('Application startup timeout - may still be starting', 'WARNING');
          resolve(false);
        }
      }, 45000);
    });
  }

  async waitForApplicationReady() {
    this.log('Waiting for application to be fully ready...', 'INFO');
    
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 10;
      
      const checkReady = () => {
        attempts++;
        exec('netstat -an | findstr :3000', (error, stdout, stderr) => {
          if (stdout && stdout.includes('3000')) {
            this.log('✅ Application is ready and listening on port 3000', 'SUCCESS');
            resolve(true);
          } else if (attempts < maxAttempts) {
            setTimeout(checkReady, 2000);
          } else {
            this.log('❌ Application readiness check timeout', 'ERROR');
            resolve(false);
          }
        });
      };
      
      setTimeout(checkReady, 3000); // Initial delay
    });
  }

  async generateFinalReport() {
    const duration = new Date() - this.testResults.startTime;
    const durationSeconds = Math.round(duration / 1000);
    
    this.log('\n📋 FINAL RESTART AND TEST REPORT', 'REPORT');
    this.log('=================================', 'REPORT');
    this.log(`Process Duration: ${durationSeconds} seconds`, 'REPORT');
    this.log(`Application Restarted: ${this.testResults.applicationRestarted}`, 'REPORT');
    this.log(`Emergency Fixes Detected: ${this.testResults.emergencyMessagesDetected.length}`, 'REPORT');
    
    if (this.testResults.emergencyMessagesDetected.length > 0) {
      this.log('\n🔧 EMERGENCY FIXES ACTIVE:', 'SUCCESS');
      this.testResults.emergencyMessagesDetected.forEach((message, index) => {
        this.log(`${index + 1}. ${message}`, 'SUCCESS');
      });
    }
    
    // Final assessment
    if (this.testResults.applicationRestarted && this.testResults.emergencyMessagesDetected.length >= 3) {
      this.log('\n🎉 SUCCESS: Application restarted with ALL termination fixes active!', 'SUCCESS');
      this.log('The 1-hour stream termination bug should now be completely resolved.', 'SUCCESS');
      
      this.log('\n🧪 CRITICAL TEST PROTOCOL:', 'INFO');
      this.log('NOW you must test the fix by:', 'INFO');
      this.log('1. Start a test stream IMMEDIATELY', 'INFO');
      this.log('2. Monitor at these CRITICAL time points:', 'INFO');
      this.log('   ⏰ 30 minutes - Long-duration health check would have started', 'INFO');
      this.log('   ⏰ 60 minutes - Previous termination point (CRITICAL)', 'INFO');
      this.log('   ⏰ 90 minutes - Confirmation that fix is working', 'INFO');
      this.log('3. Watch for termination events in logs/real-time-stream-monitor.log', 'INFO');
      this.log('4. If stream survives past 60 minutes, the fix is successful!', 'INFO');
      
      this.log('\n📊 SUCCESS CRITERIA:', 'INFO');
      this.log('✅ Stream continues past 60 minutes without termination', 'INFO');
      this.log('✅ No "CRITICAL_TERMINATION" messages in logs', 'INFO');
      this.log('✅ No automatic stream stops at the 1-hour mark', 'INFO');
      this.log('✅ Emergency fix messages appear in application console', 'INFO');
      
    } else {
      this.log('\n⚠️  WARNING: Issues detected during restart', 'WARNING');
      
      if (!this.testResults.applicationRestarted) {
        this.log('\n❌ APPLICATION NOT STARTED:', 'ERROR');
        this.log('Manual restart required:', 'ERROR');
        this.log('1. Open terminal in StreamOnPod directory', 'ERROR');
        this.log('2. Run: npm run dev', 'ERROR');
        this.log('3. Look for EMERGENCY messages in console', 'ERROR');
      }
      
      if (this.testResults.emergencyMessagesDetected.length < 3) {
        this.log('\n⚠️  INSUFFICIENT EMERGENCY FIXES DETECTED:', 'WARNING');
        this.log('Expected at least 3 emergency fix messages', 'WARNING');
        this.log('The 1-hour termination may still occur', 'WARNING');
      }
    }
    
    this.log(`\n📄 Detailed log: ${this.logFile}`, 'INFO');
    this.log('\n🔍 Monitor the application console for EMERGENCY messages!', 'INFO');
  }

  async run() {
    try {
      this.log('Starting final restart and test process...', 'START');
      
      // Step 1: Kill existing processes
      await this.killExistingProcesses();
      
      // Step 2: Start application with monitoring
      const started = await this.startApplicationWithMonitoring();
      
      // Step 3: Wait for application to be ready
      if (started) {
        await this.waitForApplicationReady();
      }
      
      // Step 4: Generate final report
      await this.generateFinalReport();
      
    } catch (error) {
      this.log(`Final restart process failed: ${error.message}`, 'ERROR');
      process.exit(1);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Final restart process interrupted by user');
  console.log('You may need to manually restart the application with: npm run dev');
  process.exit(1);
});

// Run the final restart and test
const finalTest = new FinalRestartAndTest();
finalTest.run().then(() => {
  console.log('\n🎯 NEXT: Start a test stream and monitor for 90+ minutes!');
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
