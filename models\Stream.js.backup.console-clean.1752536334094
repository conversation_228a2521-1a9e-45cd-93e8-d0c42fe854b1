const { v4: uuidv4 } = require('uuid');
const { db } = require('../db/database');
const { datetimeLocalToUTCFixed, getCurrentUTC } = require('../utils/timezone');
class Stream {
  static async create(streamData) {
    const id = uuidv4();
    let {
      title,
      video_id,
      rtmp_url,
      stream_key,
      platform,
      platform_icon,
      bitrate = 2500,
      resolution,
      fps = 30,
      orientation = 'horizontal',
      loop_video = true,
      schedule_time = null,
      duration = null,
      use_advanced_settings = false,
      user_id,
      schedule_timezone = 'Asia/Jakarta'
    } = streamData;
    // HAPUS BARIS INI - konversi sudah dilakukan di app.js
    // if (schedule_time) {
    //   const originalTime = schedule_time;
    //   schedule_time = datetimeLocalToUTCFixed(schedule_time);
    //   console.log(`[Stream.create] Schedule time conversion: ${originalTime} -> ${schedule_time} (Fixed Asia/Jakarta)`);
    // }
    const loop_video_int = loop_video ? 1 : 0;
    const use_advanced_settings_int = use_advanced_settings ? 1 : 0;
    const status = schedule_time ? 'scheduled' : 'offline';
    const status_updated_at = await getCurrentUTC();
    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO streams (
          id, title, video_id, rtmp_url, stream_key, platform, platform_icon,
          bitrate, resolution, fps, orientation, loop_video,
          schedule_time, duration, status, status_updated_at, use_advanced_settings, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id, title, video_id, rtmp_url, stream_key, platform, platform_icon,
          bitrate, resolution, fps, orientation, loop_video_int,
          schedule_time, duration, status, status_updated_at, use_advanced_settings_int, user_id
        ],
        function (err) {
          if (err) {
            console.error('Error creating stream:', err.message);
            return reject(err);
          }
          resolve({ id, ...streamData, status, status_updated_at });
        }
      );
    });
  }
  static findById(id) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM streams WHERE id = ?', [id], (err, row) => {
        if (err) {
          console.error('Error finding stream:', err.message);
          return reject(err);
        }
        if (row) {
          row.loop_video = row.loop_video === 1;
          row.use_advanced_settings = row.use_advanced_settings === 1;
        }
        resolve(row);
      });
    });
  }

  // Batch load streams by IDs to prevent N+1 query problem
  static findByIds(ids) {
    if (!ids || ids.length === 0) {
      return Promise.resolve([]);
    }

    return new Promise((resolve, reject) => {
      const placeholders = ids.map(() => '?').join(',');
      const query = `SELECT * FROM streams WHERE id IN (${placeholders})`;

      db.all(query, ids, (err, rows) => {
        if (err) {
          console.error('Error in Stream.findByIds:', err.message);
          return reject(err);
        }

        if (rows) {
          rows.forEach(row => {
            row.loop_video = row.loop_video === 1;
            row.use_advanced_settings = row.use_advanced_settings === 1;
          });
        }

        resolve(rows || []);
      });
    });
  }
static findActiveByUserId(userId) {
    return new Promise((resolve, reject) => {
      db.all(
        "SELECT * FROM streams WHERE user_id = ? AND status = 'live'",
        [userId],
        (err, rows) => {
          if (err) {
            console.error('Error finding active streams by user ID:', err.message);
            return reject(err);
          }
          if (rows) {
            rows.forEach(row => {
              row.loop_video = row.loop_video === 1;
              row.use_advanced_settings = row.use_advanced_settings === 1;
            });
          }
          resolve(rows || []);
        }
      );
    });
  }
  static findAll(userId = null, filter = null, limit = null, offset = null, search = '') {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT s.*,
               v.title AS video_title,
               v.filepath AS video_filepath,
               v.thumbnail_path AS video_thumbnail,
               v.duration AS video_duration,
               v.resolution AS video_resolution,
               v.bitrate AS video_bitrate,
               v.fps AS video_fps
        FROM streams s
        LEFT JOIN videos v ON s.video_id = v.id
        WHERE 1=1
      `;
      const params = [];

      if (userId) {
        query += ' AND s.user_id = ?';
        params.push(userId);
      }

      if (filter) {
        if (filter === 'live') {
          query += " AND s.status = 'live'";
        } else if (filter === 'scheduled') {
          query += " AND s.status = 'scheduled'";
        } else if (filter === 'offline') {
          query += " AND s.status = 'offline'";
        } else if (filter === 'error') {
          query += " AND s.status = 'error'";
        }
      }

      if (search && search.trim() !== '') {
        query += ` AND (
          s.title LIKE ? OR
          s.platform LIKE ? OR
          s.status LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY s.created_at DESC';

      if (limit !== null && offset !== null) {
        query += ' LIMIT ? OFFSET ?';
        params.push(limit, offset);
      }

      db.all(query, params, (err, rows) => {
        if (err) {
          console.error('Error finding streams:', err.message);
          return reject(err);
        }
        if (rows) {
          rows.forEach(row => {
            row.loop_video = row.loop_video === 1;
            row.use_advanced_settings = row.use_advanced_settings === 1;
          });
        }
        resolve(rows || []);
      });
    });
  }

  // Count all streams for pagination
  static countAll(userId = null, filter = null, search = '') {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT COUNT(*) as count
        FROM streams s
        WHERE 1=1
      `;
      const params = [];

      if (userId) {
        query += ' AND s.user_id = ?';
        params.push(userId);
      }

      if (filter) {
        if (filter === 'live') {
          query += " AND s.status = 'live'";
        } else if (filter === 'scheduled') {
          query += " AND s.status = 'scheduled'";
        } else if (filter === 'offline') {
          query += " AND s.status = 'offline'";
        } else if (filter === 'error') {
          query += " AND s.status = 'error'";
        }
      }

      if (search && search.trim() !== '') {
        query += ` AND (
          s.title LIKE ? OR
          s.platform LIKE ? OR
          s.status LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      db.get(query, params, (err, row) => {
        if (err) {
          console.error('Error counting streams:', err.message);
          return reject(err);
        }
        resolve(row.count || 0);
      });
    });
  }
  static async update(id, streamData) {
    // HAPUS BLOK INI - konversi sudah dilakukan di app.js
    // if (streamData.schedule_time) {
    //   const originalTime = streamData.schedule_time;
    //   streamData.schedule_time = datetimeLocalToUTCFixed(streamData.schedule_time);
    //   console.log(`[Stream.update] Schedule time conversion: ${originalTime} -> ${streamData.schedule_time} (Fixed Asia/Jakarta)`);
    //   console.log(`[Stream.update] Received schedule_time: ${streamData.schedule_time}, Type: ${typeof streamData.schedule_time}`);
    // }
    const fields = [];
    const values = [];
    Object.entries(streamData).forEach(([key, value]) => {
      if (key === 'loop_video' && typeof value === 'boolean') {
        fields.push(`${key} = ?`);
        values.push(value ? 1 : 0);
      } else if (key !== 'schedule_timezone') {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    });
    fields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);
    const query = `UPDATE streams SET ${fields.join(', ')} WHERE id = ?`;
    return new Promise((resolve, reject) => {
      db.run(query, values, function (err) {
        if (err) {
          console.error('Error updating stream:', err.message);
          return reject(err);
        }
        resolve({ id, ...streamData });
      });
    });
  }
  static delete(id, userId) {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM streams WHERE id = ? AND user_id = ?',
        [id, userId],
        function (err) {
          if (err) {
            console.error('Error deleting stream:', err.message);
            return reject(err);
          }

          // Invalidate cache after successful deletion
          if (this.changes > 0) {
            try {
              const cacheService = require('../services/cacheService');
              cacheService.invalidateStream(id, userId);
              cacheService.invalidateUser(userId);
            } catch (cacheError) {
              console.error('Error invalidating cache after stream deletion:', cacheError);
              // Don't fail the deletion due to cache errors
            }
          }

          resolve({ success: true, deleted: this.changes > 0 });
        }
      );
    });
  }

  // Delete all streams for a user (used when downgrading to Preview plan)
  static deleteAllUserStreams(userId) {
    return new Promise((resolve, reject) => {
      // First get all streams to log them
      db.all('SELECT id, title FROM streams WHERE user_id = ?', [userId], (err, streams) => {
        if (err) {
          console.error('Error getting user streams:', err.message);
          return reject(err);
        }

        if (streams.length === 0) {
          // console.log(`📭 No streams found for user ${userId}`); // Removed for production
          return resolve({ success: true, deleted: 0, streams: [] });
        }

        // console.log(`🗑️ Deleting ${streams.length} streams for user ${userId}:`); // Removed for production
        streams.forEach(stream => {
          console.log(`   - ${stream.title} (${stream.id})`);
        });

        // Delete all streams for the user
        db.run(
          'DELETE FROM streams WHERE user_id = ?',
          [userId],
          function (err) {
            if (err) {
              console.error('Error deleting all user streams:', err.message);
              return reject(err);
            }
            // console.log(`✅ Successfully deleted ${this.changes} streams for user ${userId}`); // Removed for production
            resolve({
              success: true,
              deleted: this.changes,
              streams: streams.map(s => ({ id: s.id, title: s.title }))
            });
          }
        );
      });
    });
  }
  static async updateStatus(id, status, userId, errorMessage = null) {
    console.log(`[DB] Updating status for stream ${id} to: ${status}`);
    const status_updated_at = await getCurrentUTC();
    let start_time = null;
    let end_time = null;
    if (status === 'live') {
      start_time = await getCurrentUTC();
    } else if (status === 'offline' || status === 'error') {
      end_time = await getCurrentUTC();
    }

    // Update stream status in database
    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE streams SET
          status = ?,
          status_updated_at = ?,
          start_time = COALESCE(?, start_time),
          end_time = COALESCE(?, end_time),
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ? AND user_id = ?`,
        [status, status_updated_at, start_time, end_time, id, userId],
        function (err) {
          if (err) {
            console.error('Error updating stream status:', err.message);
            return reject(err);
          }

          if (this.changes === 0) {
            console.warn(`[DB] No rows updated for stream ${id} - stream may not exist or user ${userId} may not own it`);
          } else {
            console.log(`[DB] Successfully updated status for stream ${id} to: ${status}`);
          }

          resolve({
            id,
            status,
            status_updated_at,
            start_time,
            end_time,
            errorMessage,
            updated: this.changes > 0
          });
        }
      );
    });
  }
  static async getStreamWithVideo(id) {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT s.*, v.title AS video_title, v.filepath AS video_filepath,
                v.thumbnail_path AS video_thumbnail, v.duration AS video_duration
         FROM streams s
         LEFT JOIN videos v ON s.video_id = v.id
         WHERE s.id = ?`,
        [id],
        (err, row) => {
          if (err) {
            console.error('Error fetching stream with video:', err.message);
            return reject(err);
          }
          if (row) {
            row.loop_video = row.loop_video === 1;
            row.use_advanced_settings = row.use_advanced_settings === 1;
          }
          resolve(row);
        }
      );
    });
  }
  static async isStreamKeyInUse(streamKey, userId, excludeId = null, options = {}) {
    return new Promise((resolve, reject) => {
      // Options for different validation modes
      const {
        globalCheck = false,        // Check across all users
        includeInactive = false,    // Include offline/error streams
        debugMode = false          // Enable detailed logging
      } = options;

      let query = 'SELECT COUNT(*) as count FROM streams WHERE stream_key = ?';
      const params = [streamKey];

      // Add user filter unless global check is requested
      if (!globalCheck) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      // Filter by active statuses only (exclude old/inactive streams)
      if (!includeInactive) {
        query += ' AND status IN (?, ?, ?, ?)';
        params.push('offline', 'live', 'scheduled', 'starting');
      }

      // Exclude specific stream ID if provided (for updates)
      if (excludeId) {
        query += ' AND id != ?';
        params.push(excludeId);
      }

      if (debugMode) {
        console.log(`[Stream.isStreamKeyInUse] Query: ${query}`);
        console.log(`[Stream.isStreamKeyInUse] Params: [${params.map(p => p === streamKey ? '[HIDDEN]' : p).join(', ')}]`);
      }

      db.get(query, params, (err, row) => {
        if (err) {
          console.error('Error checking stream key:', err.message);
          return reject(err);
        }

        const isInUse = row.count > 0;

        if (debugMode) {
          console.log(`[Stream.isStreamKeyInUse] Result: ${isInUse} (count: ${row.count})`);
        }

        resolve(isInUse);
      });
    });
  }

  // Debug method to get detailed information about streams with the same key
  static async getStreamsWithKey(streamKey, userId = null, options = {}) {
    return new Promise((resolve, reject) => {
      const {
        globalCheck = false,
        includeInactive = false
      } = options;

      let query = 'SELECT id, title, status, user_id, created_at, updated_at FROM streams WHERE stream_key = ?';
      const params = [streamKey];

      if (!globalCheck && userId) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      if (!includeInactive) {
        query += ' AND status IN (?, ?, ?, ?)';
        params.push('offline', 'live', 'scheduled', 'starting');
      }

      query += ' ORDER BY created_at DESC';

      db.all(query, params, (err, rows) => {
        if (err) {
          console.error('Error getting streams with key:', err.message);
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  // Cleanup old inactive streams to prevent false positives in stream key validation
  static async cleanupOldStreams(options = {}) {
    return new Promise((resolve, reject) => {
      const {
        olderThanDays = 30,     // Remove streams older than X days
        statusesToClean = ['error', 'offline'], // Only clean these statuses
        dryRun = false          // If true, only count what would be deleted
      } = options;

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      let query = 'SELECT COUNT(*) as count FROM streams WHERE updated_at < ? AND status IN (';
      query += statusesToClean.map(() => '?').join(', ') + ')';

      const params = [cutoffISO, ...statusesToClean];

      if (dryRun) {
        // Just count what would be deleted
        db.get(query, params, (err, row) => {
          if (err) {
            console.error('Error counting old streams:', err.message);
            return reject(err);
          }
          resolve({ wouldDelete: row.count, deleted: 0, dryRun: true });
        });
      } else {
        // First count, then delete
        db.get(query, params, (err, countRow) => {
          if (err) {
            console.error('Error counting old streams:', err.message);
            return reject(err);
          }

          if (countRow.count === 0) {
            return resolve({ wouldDelete: 0, deleted: 0, dryRun: false });
          }

          // Now delete them
          const deleteQuery = 'DELETE FROM streams WHERE updated_at < ? AND status IN (' +
            statusesToClean.map(() => '?').join(', ') + ')';

          db.run(deleteQuery, params, function(err) {
            if (err) {
              console.error('Error deleting old streams:', err.message);
              return reject(err);
            }
            resolve({ wouldDelete: countRow.count, deleted: this.changes, dryRun: false });
          });
        });
      }
    });
  }

  static findScheduledInRange(endTime) {
    return new Promise((resolve, reject) => {
      const endTimeISO = endTime.toISOString();
      console.log(`[DB] Searching for streams scheduled before ${endTimeISO} with status 'scheduled'`);
  
      const query = `
        SELECT s.*,
               v.title AS video_title,
               v.filepath AS video_filepath,
               v.thumbnail_path AS video_thumbnail,
               v.duration AS video_duration,
               v.resolution AS video_resolution,
               v.bitrate AS video_bitrate,
               v.fps AS video_fps
        FROM streams s
        LEFT JOIN videos v ON s.video_id = v.id
        WHERE s.status = 'scheduled'
        AND s.status != 'starting'
        AND s.schedule_time IS NOT NULL
        AND s.schedule_time <= ?
        ORDER BY s.schedule_time ASC
      `;
  
      db.all(query, [endTimeISO], (err, rows) => {
        if (err) {
          console.error('Error finding scheduled streams:', err.message);
          return reject(err);
        }
  
        console.log(`[DB] Found ${rows ? rows.length : 0} scheduled streams in database`);
        if (rows) {
          rows.forEach(row => {
            row.loop_video = row.loop_video === 1;
            row.use_advanced_settings = row.use_advanced_settings === 1;
            console.log(`[DB] Stream ${row.id}: ${row.title} scheduled for ${row.schedule_time}`);
          });
        }
        resolve(rows || []);
      });
    });
  }
  static findAllWithIncorrectTimezone() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT *
        FROM streams
        WHERE schedule_time IS NOT NULL
        AND status = 'scheduled'
        AND schedule_time NOT LIKE '%Z'
      `;
      db.all(query, [], (err, rows) => {
        if (err) {
          console.error('Error finding streams with incorrect timezone:', err.message);
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }
}
module.exports = Stream;
