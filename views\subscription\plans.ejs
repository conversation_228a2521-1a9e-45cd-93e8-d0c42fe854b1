<% layout('layout') -%>

<!-- Custom CSS for Prorated Upgrade -->
<style>
  .prorated-preview {
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .prorated-preview .bg-gray-700 {
    border: 1px solid #4a5568;
  }

  .prorated-preview .text-green-400 {
    color: #68d391 !important;
  }

  .prorated-preview .text-yellow-400 {
    color: #f6e05e !important;
  }

  .prorated-preview hr {
    border-color: #4a5568;
    margin: 8px 0;
  }

  /* Button hover effects */
  .bg-blue-600:hover {
    background-color: #2563eb !important;
  }

  .bg-green-600 {
    background-color: #059669 !important;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .prorated-preview .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .prorated-preview .flex span:last-child {
      align-self: flex-end;
      font-weight: 600;
    }
  }
</style>

<!-- Midtrans Snap Script -->
<% if (typeof midtransClientKey !== 'undefined' && midtransClientKey) { %>
<% const isProduction = process.env.MIDTRANS_IS_PRODUCTION === 'true'; %>
<% const snapUrl = isProduction ? 'https://app.midtrans.com/snap/snap.js' : 'https://app.sandbox.midtrans.com/snap/snap.js'; %>
<script type="text/javascript"
  src="<%= snapUrl %>"
  data-client-key="<%= midtransClientKey %>"
  onload="// console.log('✅ Midtrans Snap script loaded successfully') // Cleaned for production"
  onerror="// console.error('❌ Failed to load Midtrans Snap script') // Cleaned for production"></script>
<% } else { %>
<script>// console.warn('⚠️ Midtrans client key not available'); // Cleaned for production</script>
<% } %>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-white mb-2"><%= t('subscription.choose_plan_title') %></h1>
    <p class="text-gray-400"><%= t('subscription.choose_plan_subtitle') %></p>
  </div>
</div>

<!-- Current Plan Info -->
<% if (typeof currentSubscription !== 'undefined' && currentSubscription) { %>
  <div class="bg-primary/10 border border-primary/20 rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center gap-3 mb-2">
            <h3 class="text-lg font-semibold text-primary">
              <%= t('subscription.current_plan') %>: <%= currentSubscription.plan_name %>
              <% if (currentSubscription.isExpired) { %>
                <span class="text-red-400 text-sm ml-2">
                  (<%= currentSubscription.isCancelled ? (locale === 'id' ? 'Dibatalkan' : 'Cancelled') : (locale === 'id' ? 'Berakhir' : 'Expired') %>)
                </span>
              <% } %>
            </h3>
            <% if (typeof trialInfo !== 'undefined' && trialInfo && trialInfo.is_active) { %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                <i class="ti ti-gift mr-1"></i>
                <%= t('subscription.trial_active') %>
              </span>
            <% } else if (currentSubscription.isExpired) { %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <i class="ti ti-x-circle mr-1"></i>
                <%= currentSubscription.isCancelled ? (locale === 'id' ? 'Dibatalkan' : 'Cancelled') : (locale === 'id' ? 'Berakhir' : 'Expired') %>
              </span>
              <!-- Renewal Button for Expired/Cancelled Subscription -->
              <button onclick="showRenewalModal('<%= currentSubscription.plan_id %>', '<%= currentSubscription.plan_name %>')"
                      class="ml-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary hover:bg-secondary text-white transition-colors">
                <i class="ti ti-refresh mr-1"></i>
                <%= locale === 'id' ? 'Perpanjang' : 'Renew' %>
              </button>
            <% } %>
          </div>
          <% if (typeof quotaInfo !== 'undefined' && quotaInfo) { %>
            <p class="text-gray-300">
              <%= t('subscription.streaming_usage') %>: <%= quotaInfo.streaming.current %>/<%= quotaInfo.streaming.max %> <%= t('subscription.slots_used') %> |
              <%= t('subscription.storage_usage') %>: <%= quotaInfo.storage.current %><%= quotaInfo.storage.unit %>/<%= quotaInfo.storage.max %><%= quotaInfo.storage.unit %> <%= t('subscription.used') %> (<%= quotaInfo.storage.percentage %>%)
            </p>
          <% } %>
          <% if (typeof trialInfo !== 'undefined' && trialInfo && trialInfo.is_active) { %>
            <p class="text-yellow-400 text-sm mt-1">
              <i class="ti ti-clock mr-1"></i>
              <%= t('subscription.trial_expires') %>: <%= new Date(trialInfo.trial_end_date).toLocaleDateString() %>
            </p>
          <% } %>
        </div>
        <div class="text-right">
          <% if (typeof trialInfo !== 'undefined' && trialInfo && trialInfo.is_active) { %>
            <p class="text-yellow-400 text-sm font-medium"><%= t('subscription.trial_access') %></p>
            <p class="text-gray-400 text-xs">
              <%= trialInfo.trial_slots %> slot<%= trialInfo.trial_slots !== 1 ? 's' : '' %> •
              <% if (trialInfo.trial_storage_gb <= 1) { %>
                <%= Math.round(trialInfo.trial_storage_gb * 1024) %>MB
              <% } else { %>
                <%= trialInfo.trial_storage_gb %>GB
              <% } %>
            </p>
          <% } else { %>
            <!-- Show subscription expiration info -->
            <% if (currentSubscription.plan_name === 'Preview') { %>
              <!-- For Preview plan (unlimited) -->
              <p class="text-gray-400 text-sm">
                <i class="ti ti-eye mr-1"></i>
                <%= locale === 'id' ? 'Plan Gratis' : 'Free Plan' %>
              </p>
              <p class="text-xs mt-1 text-green-400">
                <i class="ti ti-infinity mr-1"></i>
                <%= locale === 'id' ? 'Unlimited' : 'Unlimited' %>
              </p>
            <% } else if (currentSubscription.end_date) { %>
              <!-- For paid plans with expiration -->
              <% if (currentSubscription.isCancelled) { %>
                <!-- For cancelled subscriptions -->
                <p class="text-gray-400 text-sm">
                  <i class="ti ti-ban mr-1"></i>
                  <%= locale === 'id' ? 'Dibatalkan pada' : 'Cancelled on' %>: <%= new Date(currentSubscription.end_date).toLocaleDateString() %>
                </p>
                <p class="text-xs mt-1 text-red-500">
                  <i class="ti ti-x-circle mr-1"></i>
                  <%= locale === 'id' ? 'Langganan dibatalkan oleh admin' : 'Subscription cancelled by admin' %>
                </p>
              <% } else { %>
                <!-- For regular expiration -->
                <p class="text-gray-400 text-sm">
                  <i class="ti ti-calendar mr-1"></i>
                  <%= t('subscription.expires') %>: <%= new Date(currentSubscription.end_date).toLocaleDateString() %>
                </p>
                <%
                  // Calculate days remaining
                  const endDate = new Date(currentSubscription.end_date);
                  const today = new Date();
                  const timeDiff = endDate.getTime() - today.getTime();
                  const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
                %>
                <% if (daysRemaining > 0) { %>
                  <p class="text-xs mt-1 <%= daysRemaining <= 7 ? 'text-red-400' : daysRemaining <= 14 ? 'text-yellow-400' : 'text-green-400' %>">
                    <i class="ti ti-clock mr-1"></i>
                    <%= daysRemaining %> <%= locale === 'id' ? 'hari tersisa' : 'days remaining' %>
                  </p>
                <% } else if (daysRemaining === 0) { %>
                  <p class="text-xs mt-1 text-red-400">
                    <i class="ti ti-alert-circle mr-1"></i>
                    <%= locale === 'id' ? 'Berakhir hari ini' : 'Expires today' %>
                  </p>
                <% } else { %>
                  <p class="text-xs mt-1 text-red-500">
                    <i class="ti ti-x-circle mr-1"></i>
                    <%= locale === 'id' ? 'Sudah berakhir' : 'Expired' %>
                    <% if (Math.abs(daysRemaining) === 1) { %>
                      (<%= locale === 'id' ? '1 hari lalu' : '1 day ago' %>)
                    <% } else if (Math.abs(daysRemaining) <= 30) { %>
                      (<%= Math.abs(daysRemaining) %> <%= locale === 'id' ? 'hari lalu' : 'days ago' %>)
                    <% } else { %>
                      (<%= locale === 'id' ? 'lebih dari 30 hari lalu' : 'more than 30 days ago' %>)
                    <% } %>
                  </p>
                <% } %>
              <% } %>
            <% } else { %>
              <!-- For plans without end_date (should not happen for non-Preview plans) -->
              <p class="text-red-400 text-sm">
                <i class="ti ti-alert-triangle mr-1"></i>
                <%= locale === 'id' ? 'Tanggal berakhir tidak ditemukan' : 'Expiration date not found' %>
              </p>
              <p class="text-xs mt-1 text-gray-400">
                <%= locale === 'id' ? 'Hubungi admin untuk perbaikan' : 'Contact admin for assistance' %>
              </p>
            <% } %>
          <% } %>
        </div>
    </div>
  </div>
<% } %>

<!-- Expired Subscription Notice -->
<% if (typeof expiredSubscriptionInfo !== 'undefined' && expiredSubscriptionInfo && expiredSubscriptionInfo.hasExpired) { %>
  <div class="max-w-7xl mx-auto px-6 mb-8">
    <div class="bg-orange-900/30 border border-orange-500/50 rounded-lg p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <i class="ti ti-info-circle text-orange-400 text-xl"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-orange-200">
            <%= locale === 'id' ? 'Langganan Anda Telah Berakhir' : 'Your Subscription Has Expired' %>
          </h3>
          <div class="mt-2 text-sm text-orange-300">
            <p>
              <% if (locale === 'id') { %>
                Langganan <strong><%= expiredSubscriptionInfo.previousPlan %></strong> Anda telah berakhir.
                Anda sekarang dapat memilih plan apa saja, termasuk plan yang lebih rendah dari sebelumnya.
              <% } else { %>
                Your <strong><%= expiredSubscriptionInfo.previousPlan %></strong> subscription has expired.
                You can now choose any plan, including plans lower than your previous subscription.
              <% } %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
<% } %>

<!-- Pricing Cards -->
<div class="max-w-7xl mx-auto px-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% plans.forEach(function(plan) { %>
        <div class="bg-dark-800 rounded-lg border <%= plan.name === 'Pro' ? 'border-primary' : 'border-gray-700' %> relative overflow-hidden">
          <% if (plan.name === 'Pro') { %>
            <div class="absolute top-0 left-0 right-0 bg-primary text-white text-center py-2 text-sm font-medium">
              <%= t('subscription.most_popular') %>
            </div>
          <% } %>

          <div class="p-6 <%= plan.name === 'Pro' ? 'pt-12' : '' %>">
            <!-- Plan Header -->
            <div class="text-center mb-6">
              <h3 class="text-xl font-bold text-white mb-2"><%= plan.name %></h3>
              <div class="mb-4">
                <% if (plan.price === 0) { %>
                  <span class="text-3xl font-bold text-white">Gratis</span>
                <% } else { %>
                  <div class="text-center">
                    <span class="text-3xl font-bold text-white">Rp<%= plan.price.toLocaleString('id-ID') %></span>
                    <span class="text-gray-400">/<%= plan.billing_period %></span>
                    <% if (plan.price > 0) { %>
                      <div class="text-xs text-gray-400 mt-1">
                        <% if (locale === 'id') { %>
                          + biaya admin 1%
                        <% } else { %>
                          + 1% admin fee
                        <% } %>
                      </div>
                    <% } %>
                  </div>
                <% } %>
              </div>

              <!-- Prorated Price Preview -->
              <%
                // Get current user plan price for comparison (for preview container)
                var currentPlanPriceForPreview = 0;
                if (typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name) {
                  var currentPlanForPreview = plans.find(p => p.name === currentSubscription.plan_name);
                  if (currentPlanForPreview) {
                    currentPlanPriceForPreview = currentPlanForPreview.price;
                  }
                }

                // Check if this is the current plan (for preview container)
                var isCurrentPlanForPreview = typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name === plan.name;

                // Check if this is a downgrade to paid plan (for preview container)
                var isDowngradeToPaidForPreview = plan.price > 0 && currentPlanPriceForPreview > 0 && plan.price < currentPlanPriceForPreview;

                // Show preview container for all paid plans except current and downgrades
                var showPreviewContainer = !isCurrentPlanForPreview && !isDowngradeToPaidForPreview && plan.price > 0;
              %>
              <% if (showPreviewContainer) { %>
                <div id="prorated-preview-<%= plan.id %>" class="prorated-preview mt-3" style="display: none;">
                  <div class="bg-gray-700 rounded-lg p-3 text-sm">
                    <div class="text-yellow-400 font-medium mb-2">
                      <% if (locale === 'id') { %>
                        💰 Harga untuk Anda:
                      <% } else { %>
                        💰 Your Price:
                      <% } %>
                    </div>
                    <div class="prorated-content"></div>
                  </div>
                </div>
              <% } %>
            </div>

            <!-- Features -->
            <div class="space-y-3 mb-6">
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300">
                  <%= plan.max_streaming_slots === -1 ? t('subscription.unlimited') : plan.max_streaming_slots %> <%= plan.max_streaming_slots !== 1 ? t('subscription.streaming_slots') : t('subscription.streaming_slot') %>
                </span>
              </div>
              <div class="flex items-center text-sm">
                <i class="ti ti-check text-green-400 mr-2"></i>
                <span class="text-gray-300">
                  <% if (plan.max_storage_gb <= 1) { %>
                    <%= Math.round(plan.max_storage_gb * 1024) %>MB <%= t('subscription.storage') %>
                  <% } else { %>
                    <%= plan.max_storage_gb %>GB <%= t('subscription.storage') %>
                  <% } %>
                </span>
              </div>
              <% if (plan.features) { %>
                <% try { %>
                  <% var features = typeof plan.features === 'string' ? JSON.parse(plan.features) : plan.features; %>
                  <% features.forEach(function(feature) { %>
                    <div class="flex items-center text-sm">
                      <i class="ti ti-check text-green-400 mr-2"></i>
                      <span class="text-gray-300"><%= feature %></span>
                    </div>
                  <% }); %>
                <% } catch(e) { %>
                  <div class="flex items-center text-sm">
                    <i class="ti ti-check text-green-400 mr-2"></i>
                    <span class="text-gray-300"><%= t('subscription.basic_features') %></span>
                  </div>
                <% } %>
              <% } %>
            </div>

            <!-- Action Button -->
            <div class="text-center">
              <%
                // Get current user plan price for comparison
                var currentPlanPrice = 0;
                if (typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name) {
                  var currentPlan = plans.find(p => p.name === currentSubscription.plan_name);
                  if (currentPlan) {
                    currentPlanPrice = currentPlan.price;
                  }
                }

                // Check if user has expired subscription
                var hasExpiredSubscription = typeof expiredSubscriptionInfo !== 'undefined' && expiredSubscriptionInfo && expiredSubscriptionInfo.hasExpired;

                // Check if this is the current plan
                var isCurrentPlan = typeof currentSubscription !== 'undefined' && currentSubscription && currentSubscription.plan_name === plan.name && !hasExpiredSubscription;

                // Check if this is a downgrade to paid plan (only restrict for active subscriptions)
                var isDowngradeToPaid = plan.price > 0 && currentPlanPrice > 0 && plan.price < currentPlanPrice && !hasExpiredSubscription;

                // Check if this is an upgrade
                var isUpgrade = plan.price > 0 && currentPlanPrice > 0 && plan.price > currentPlanPrice;

                // Determine button text and action
                var buttonText = '';
                var buttonAction = '';
                if (isCurrentPlan) {
                  buttonText = t('subscription.current_plan');
                } else if (isDowngradeToPaid) {
                  buttonText = t('subscription.downgrade_not_available');
                } else if (plan.price === 0) {
                  buttonText = t('subscription.get_started');
                  buttonAction = 'subscribeToPlan';
                } else if (hasExpiredSubscription && plan.price > 0 && currentPlanPrice > 0 && plan.price < currentPlanPrice) {
                  // Special case: Allow downgrade for expired subscriptions
                  buttonText = locale === 'id' ? 'Berlangganan (Downgrade)' : 'Subscribe (Downgrade)';
                  buttonAction = 'handlePlanAction';
                } else if (hasExpiredSubscription && currentSubscription && currentSubscription.plan_name === plan.name) {
                  // Special case: Renew same plan after expiration
                  buttonText = locale === 'id' ? 'Perpanjang Langganan' : 'Renew Subscription';
                  buttonAction = 'handlePlanAction';
                } else if (isUpgrade) {
                  buttonText = t('subscription.upgrade_plan_btn');
                  buttonAction = 'handlePlanAction';
                } else if (typeof currentSubscription !== 'undefined' && currentSubscription) {
                  buttonText = t('subscription.change_plan');
                  buttonAction = 'handlePlanAction';
                } else {
                  buttonText = t('subscription.subscribe');
                  buttonAction = 'handlePlanAction';
                }
              %>

              <% if (isCurrentPlan) { %>
                <button class="w-full bg-gray-600 text-gray-300 py-2 px-4 rounded-lg cursor-not-allowed" disabled>
                  <%= buttonText %>
                </button>
              <% } else if (isDowngradeToPaid) { %>
                <button class="w-full bg-red-600 text-red-200 py-2 px-4 rounded-lg cursor-not-allowed" disabled title="<%= locale === 'id' ? 'Tidak dapat downgrade ke plan berbayar yang lebih rendah' : 'Cannot downgrade to lower paid plan' %>">
                  <%= buttonText %>
                </button>
              <% } else if (plan.price === 0) { %>
                <button onclick="subscribeToPlan('<%= plan.id %>', this)" class="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                  <%= buttonText %>
                </button>
              <% } else if (hasExpiredSubscription && plan.price > 0 && currentPlanPrice > 0 && plan.price < currentPlanPrice) { %>
                <!-- Special case: Allow downgrade for expired subscriptions -->
                <button onclick="<%= buttonAction %>('<%= plan.id %>', this)" class="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors" title="<%= locale === 'id' ? 'Anda dapat berlangganan plan yang lebih rendah karena langganan sebelumnya telah berakhir' : 'You can subscribe to a lower plan because your previous subscription has expired' %>">
                  <%= buttonText %>
                </button>
              <% } else if (hasExpiredSubscription && currentSubscription && currentSubscription.plan_name === plan.name) { %>
                <!-- Special case: Renew same plan after expiration -->
                <button onclick="<%= buttonAction %>('<%= plan.id %>', this)" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors" title="<%= locale === 'id' ? 'Perpanjang langganan plan yang sama' : 'Renew your subscription to the same plan' %>">
                  <%= buttonText %>
                </button>
              <% } else { %>
                <!-- For all paid plans (except current and downgrades), show preview button -->
                <%
                  // Show preview button for upgrades and lateral moves (same price or higher)
                  var showPreviewButton = !isCurrentPlan && !isDowngradeToPaid && plan.price > 0;
                %>
                <% if (showPreviewButton) { %>
                  <button onclick="showProratedPreview('<%= plan.id %>', '<%= plan.name %>', '<%= plan.price %>')"
                          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors mb-2"
                          id="preview-btn-<%= plan.id %>">
                    <% if (locale === 'id') { %>
                      💰 Lihat Harga untuk Anda
                    <% } else { %>
                      💰 See Your Price
                    <% } %>
                  </button>
                <% } %>
 
                 <% if (plan.trial_days && plan.trial_days > 0 && !isCurrentPlan) { %>
                  <button onclick="previewTrial('<%= plan.id %>')"
                          class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors mb-2"
                          id="preview-trial-btn-<%= plan.id %>">
                    Preview Trial
                  </button>
                <% } %>

                 <!-- Main action button -->
                 <button onclick="handlePlanAction('<%= plan.id %>', this)"
                         class="w-full <%= plan.name === 'Pro' ? 'bg-primary hover:bg-secondary' : 'bg-primary hover:bg-secondary' %> text-white py-2 px-4 rounded-lg transition-colors"
                         id="action-btn-<%= plan.id %>">
                   <%= buttonText %>
                 </button>
               <% } %>
            </div>
          </div>
        </div>
      <% }); %>
  </div>

  <!-- Renewal Modal -->
  <div id="renewalModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-dark-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">
          <i class="ti ti-refresh mr-2"></i>
          <%= locale === 'id' ? 'Perpanjang Langganan' : 'Renew Subscription' %>
        </h3>
        <button onclick="hideRenewalModal()" class="text-gray-400 hover:text-white">
          <i class="ti ti-x text-xl"></i>
        </button>
      </div>

      <div class="mb-6">
        <p class="text-gray-300 mb-2">
          <%= locale === 'id' ? 'Anda akan memperpanjang langganan untuk:' : 'You are about to renew your subscription for:' %>
        </p>
        <p class="text-white font-semibold text-lg" id="renewalPlanName">Plan Name</p>
        <div class="flex items-center justify-between mt-2">
          <p class="text-gray-400 text-sm">
            <%= locale === 'id' ? 'Durasi: 30 hari' : 'Duration: 30 days' %>
          </p>
          <p class="text-primary font-semibold text-lg" id="renewalPlanPrice">
            Rp 0
          </p>
        </div>
      </div>

      <div class="bg-dark-700 rounded-lg p-4 mb-6">
        <div class="flex items-center text-yellow-400 mb-2">
          <i class="ti ti-credit-card mr-2"></i>
          <span class="font-medium"><%= locale === 'id' ? 'Informasi Pembayaran' : 'Payment Information' %></span>
        </div>
        <ul class="text-gray-300 text-sm space-y-1">
          <li>• <%= locale === 'id' ? 'Pembayaran diperlukan untuk memperpanjang langganan' : 'Payment is required to renew your subscription' %></li>
          <li>• <%= locale === 'id' ? 'Langganan akan aktif segera setelah pembayaran berhasil' : 'Subscription will be active immediately after successful payment' %></li>
          <li>• <%= locale === 'id' ? 'Anda akan mendapatkan akses penuh ke fitur plan selama 30 hari' : 'You will get full access to plan features for 30 days' %></li>
          <li>• <%= locale === 'id' ? 'Slot streaming dan storage akan dikembalikan' : 'Streaming slots and storage will be restored' %></li>
        </ul>
      </div>

      <div class="flex space-x-3">
        <button onclick="hideRenewalModal()"
                class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
          <%= locale === 'id' ? 'Batal' : 'Cancel' %>
        </button>
        <button id="confirmRenewalBtn"
                class="flex-1 px-4 py-2 bg-primary hover:bg-secondary text-white rounded-lg transition-colors">
          <i class="ti ti-credit-card mr-2"></i>
          <%= locale === 'id' ? 'Bayar & Perpanjang' : 'Pay & Renew' %>
        </button>
      </div>
    </div>
  </div>

  <!-- FAQ Section -->
  <div class="mt-16">
      <h2 class="text-2xl font-bold text-white text-center mb-8"><%= t('subscription.faq_title') %></h2>
      <div class="max-w-3xl mx-auto space-y-4">
        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_change_plan_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_change_plan_a') %></p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_exceed_limits_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_exceed_limits_a') %></p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white"><%= t('subscription.faq_subscription_management_q') %></h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <p class="text-gray-300"><%= t('subscription.faq_subscription_management_a') %></p>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white">
                <% if (locale === 'id') { %>
                  💰 Bagaimana sistem harga upgrade bekerja?
                <% } else { %>
                  💰 How does the upgrade pricing system work?
                <% } %>
              </h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <div class="text-gray-300 space-y-3">
              <% if (locale === 'id') { %>
                <p><strong>Sistem Prorated Upgrade:</strong></p>
                <ul class="list-disc list-inside space-y-2 ml-4">
                  <li>Anda mendapat kredit dari sisa hari plan lama</li>
                  <li>Kredit dipotong dari harga plan baru</li>
                  <li>Biaya admin 1% ditambahkan ke harga upgrade</li>
                  <li>Masa aktif tetap sampai tanggal yang sama</li>
                </ul>

                <p class="mt-4"><strong>Contoh:</strong></p>
                <div class="bg-gray-700 rounded-lg p-3 text-sm">
                  <div class="space-y-1">
                    <div class="flex justify-between">
                      <span>Plan PodLite (sisa 20 hari):</span>
                      <span>Rp 16.600</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Plan PodPrime:</span>
                      <span>Rp 99.900</span>
                    </div>
                    <div class="flex justify-between text-green-400">
                      <span>Kredit plan lama:</span>
                      <span>-Rp 16.600</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Harga upgrade:</span>
                      <span>Rp 83.300</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Biaya admin (1%):</span>
                      <span>Rp 833</span>
                    </div>
                    <hr class="border-gray-600 my-2">
                    <div class="flex justify-between font-bold text-yellow-400">
                      <span>Total Pembayaran:</span>
                      <span>Rp 84.133</span>
                    </div>
                  </div>
                </div>

                <p class="text-green-400 text-sm">
                  ✅ Hemat Rp 16.600 dari plan lama! Masa aktif tetap sampai tanggal yang sama.
                </p>
              <% } else { %>
                <p><strong>Prorated Upgrade System:</strong></p>
                <ul class="list-disc list-inside space-y-2 ml-4">
                  <li>You get credit from remaining days of your old plan</li>
                  <li>Credit is deducted from the new plan price</li>
                  <li>1% admin fee is added to the upgrade price</li>
                  <li>Active period remains until the same expiry date</li>
                </ul>

                <p class="mt-4"><strong>Example:</strong></p>
                <div class="bg-gray-700 rounded-lg p-3 text-sm">
                  <div class="space-y-1">
                    <div class="flex justify-between">
                      <span>PodLite Plan (20 days remaining):</span>
                      <span>IDR 16,600</span>
                    </div>
                    <div class="flex justify-between">
                      <span>PodPrime Plan:</span>
                      <span>IDR 99,900</span>
                    </div>
                    <div class="flex justify-between text-green-400">
                      <span>Old plan credit:</span>
                      <span>-IDR 16,600</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Upgrade price:</span>
                      <span>IDR 83,300</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Admin fee (1%):</span>
                      <span>IDR 833</span>
                    </div>
                    <hr class="border-gray-600 my-2">
                    <div class="flex justify-between font-bold text-yellow-400">
                      <span>Total Payment:</span>
                      <span>IDR 84,133</span>
                    </div>
                  </div>
                </div>

                <p class="text-green-400 text-sm">
                  ✅ Save IDR 16,600 from your old plan! Active period remains until the same date.
                </p>
              <% } %>
            </div>
          </div>
        </div>

        <div class="bg-dark-800 rounded-lg border border-gray-700">
          <button class="w-full text-left p-6 focus:outline-none" onclick="toggleFAQ(this)">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-white">
                <% if (locale === 'id') { %>
                  ⚙️ Apa itu biaya admin 1%?
                <% } else { %>
                  ⚙️ What is the 1% admin fee?
                <% } %>
              </h3>
              <i class="ti ti-chevron-down text-gray-400 transform transition-transform"></i>
            </div>
          </button>
          <div class="hidden px-6 pb-6">
            <div class="text-gray-300 space-y-3">
              <% if (locale === 'id') { %>
                <p>Biaya admin 1% adalah biaya pemrosesan pembayaran yang ditambahkan ke total transaksi.</p>

                <div class="bg-blue-900/30 border border-blue-500/30 rounded-lg p-3">
                  <p class="text-blue-300 text-sm">
                    <strong>📝 Catatan Penting:</strong><br>
                    • Harga plan yang ditampilkan adalah harga asli tanpa biaya admin<br>
                    • Biaya admin 1% ditambahkan saat checkout<br>
                    • Biaya ini untuk pemrosesan payment gateway<br>
                    • Transparansi penuh dalam perhitungan harga
                  </p>
                </div>

                <p><strong>Contoh perhitungan:</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4 text-sm">
                  <li>Plan PodFlow: Rp 49.900 + admin 1% (Rp 499) = <strong>Rp 50.399</strong></li>
                  <li>Plan PodPrime: Rp 99.900 + admin 1% (Rp 999) = <strong>Rp 100.899</strong></li>
                  <li>Upgrade PodLite → PodPrime: Rp 83.300 + admin 1% (Rp 833) = <strong>Rp 84.133</strong></li>
                </ul>
              <% } else { %>
                <p>The 1% admin fee is a payment processing fee added to the total transaction amount.</p>

                <div class="bg-blue-900/30 border border-blue-500/30 rounded-lg p-3">
                  <p class="text-blue-300 text-sm">
                    <strong>📝 Important Notes:</strong><br>
                    • Plan prices displayed are original prices without admin fee<br>
                    • 1% admin fee is added during checkout<br>
                    • This fee covers payment gateway processing<br>
                    • Full transparency in price calculation
                  </p>
                </div>

                <p><strong>Calculation examples:</strong></p>
                <ul class="list-disc list-inside space-y-1 ml-4 text-sm">
                  <li>PodFlow Plan: IDR 49,900 + 1% admin (IDR 499) = <strong>IDR 50,399</strong></li>
                  <li>PodPrime Plan: IDR 99,900 + 1% admin (IDR 999) = <strong>IDR 100,899</strong></li>
                  <li>Upgrade PodLite → PodPrime: IDR 83,300 + 1% admin (IDR 833) = <strong>IDR 84,133</strong></li>
                </ul>
              <% } %>
            </div>
          </div>
        </div>
    </div>
  </div>
</div>

<script>
  // Translation variables for JavaScript
  const translations = {
    subscribeSuccess: '<%= t("subscription.subscribe_success") %>',
    subscribeError: '<%= t("subscription.subscribe_error") %>',
    subscribeFailed: '<%= t("subscription.subscribe_failed") %>'
  };

  // Check Midtrans availability on page load
  document.addEventListener('DOMContentLoaded', function() {
    // console.log('🔍 Checking Midtrans Snap availability on page load...'); // Cleaned for production
    // console.log('typeof snap:', typeof snap); // Cleaned for production
    // console.log('window.snap:', window.snap); // Cleaned for production

    if (typeof snap !== 'undefined') {
      // console.log('✅ Midtrans Snap is available'); // Cleaned for production
    } else {
      // console.warn('⚠️ Midtrans Snap is not available'); // Cleaned for production
    }
  });

  function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('i');

    content.classList.toggle('hidden');
    icon.classList.toggle('rotate-180');
  }

  // Renewal functionality
  function showRenewalModal(planId, planName) {
    const modal = document.getElementById('renewalModal');
    const planNameElement = document.getElementById('renewalPlanName');
    const planPriceElement = document.getElementById('renewalPlanPrice');
    const renewButton = document.getElementById('confirmRenewalBtn');

    if (modal && planNameElement && renewButton) {
      planNameElement.textContent = planName;

      // Find plan price from the plans data
      const planCard = document.querySelector(`[data-plan-id="${planId}"]`);
      let planPrice = 'Contact Admin';

      if (planCard) {
        const priceElement = planCard.querySelector('.plan-price');
        if (priceElement) {
          planPrice = priceElement.textContent.trim();
        }
      }

      // Set price in modal
      if (planPriceElement) {
        planPriceElement.textContent = planPrice;
      }

      renewButton.onclick = () => renewSubscription(planId, planName);
      modal.classList.remove('hidden');
    }
  }

  function hideRenewalModal() {
    const modal = document.getElementById('renewalModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  async function renewSubscription(planId, planName) {
    const renewButton = document.getElementById('confirmRenewalBtn');
    const originalText = renewButton.innerHTML;

    try {
      renewButton.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Processing Payment...';
      renewButton.disabled = true;

      // Create renewal payment
      const response = await fetch('/subscription/renew-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: planId
        })
      });

      const data = await response.json();
      console.log('Renewal payment response:', data);
      if (data.success && data.payment && data.payment.snap_token) {
        console.log('Opening Midtrans Snap with token:', data.payment.snap_token.substring(0, 20) + '...');
        hideRenewalModal();
        showToast('info', 'Opening payment gateway...');

        // Open Midtrans Snap for payment
        if (typeof snap !== 'undefined') {
          snap.pay(data.payment.snap_token, {
            onSuccess: (result) => {
              console.log('Payment successful:', result);
              showToast('success', `Payment successful! ${planName} subscription renewed.`);
              setTimeout(() => {
                window.location.reload();
              }, 2000);
            },
            onPending: (result) => {
              console.log('Payment pending:', result);
              showToast('info', 'Payment is pending. Please complete your payment.');
              setTimeout(() => {
                window.location.reload();
              }, 3000);
            },
            onError: (result) => {
              console.error('Payment error:', result);
              showToast('error', 'Payment failed. Please try again.');
            },
            onClose: () => {
              console.log('Payment modal closed');
              showToast('info', 'Payment cancelled. You can try again anytime.');
            }
          });
        } else {
          console.error('Snap is not available');
          throw new Error('Payment system not available');
        }
      } else if (data.success && !data.payment.snap_token) {
        console.error('Payment created but no snap token received:', data);
        throw new Error('Payment gateway token not received');
      } else if (data.requiresPayment) {
        hideRenewalModal();
        showToast('info', 'This plan requires payment to renew.');
      } else {
        console.error('Renewal payment failed:', data);
        throw new Error(data.message || data.error || 'Failed to create renewal payment');
      }

    } catch (error) {
      console.error('Renewal error:', error);
      showToast('error', 'Failed to process renewal: ' + error.message);
    } finally {
      renewButton.innerHTML = originalText;
      renewButton.disabled = false;
    }
  }

  // Toast notification function
  function showToast(type, message) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm ${
      type === 'success' ? 'bg-green-600' :
      type === 'error' ? 'bg-red-600' :
      type === 'info' ? 'bg-blue-600' : 'bg-gray-600'
    }`;

    toast.innerHTML = `
      <div class="flex items-center">
        <i class="ti ti-${type === 'success' ? 'check' : type === 'error' ? 'x' : 'info-circle'} mr-2"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);
  }

  // Track button states to prevent spam clicking
  const buttonStates = new Map();

  // Function to show processing dialog without OK button
  function showProcessingDialog(message) {
    // Remove existing dialog if any
    const existingDialog = document.getElementById('processing-dialog');
    if (existingDialog) {
      existingDialog.remove();
    }

    // Create overlay
    const overlay = document.createElement('div');
    overlay.id = 'processing-dialog';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // Create dialog box
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      text-align: center;
      max-width: 400px;
      margin: 20px;
    `;

    // Create loading spinner
    const spinner = document.createElement('div');
    spinner.style.cssText = `
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #ad6610;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px auto;
    `;

    // Add CSS animation for spinner
    if (!document.getElementById('spinner-style')) {
      const style = document.createElement('style');
      style.id = 'spinner-style';
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }

    // Create message
    const messageEl = document.createElement('p');
    messageEl.textContent = message;
    messageEl.style.cssText = `
      margin: 0;
      font-size: 16px;
      color: #333;
      line-height: 1.5;
    `;

    // Create subtitle
    const subtitle = document.createElement('p');
    subtitle.textContent = 'Halaman akan dimuat ulang otomatis...';
    subtitle.style.cssText = `
      margin: 10px 0 0 0;
      font-size: 14px;
      color: #666;
    `;

    // Assemble dialog
    dialog.appendChild(spinner);
    dialog.appendChild(messageEl);
    dialog.appendChild(subtitle);
    overlay.appendChild(dialog);

    // Add to page
    document.body.appendChild(overlay);

    // Prevent closing by clicking overlay
    overlay.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
    });
  }



  async function subscribeToPlan(planId, buttonElement) {
    try {
      console.log('🚀 Starting subscription process for plan:', planId);
      console.log('🔘 Button element:', buttonElement);
      // Check if button is already processing
      if (buttonStates.get(planId)) {
        // console.log('⚠️ Button already processing, ignoring click'); // Cleaned for production
        return;
      }

      // Set button state to processing
      buttonStates.set(planId, true);

      // Disable button to prevent double-clicks
      const originalText = buttonElement.textContent;
      buttonElement.textContent = translations.processing || 'Processing...';
      buttonElement.disabled = true;
      buttonElement.style.opacity = '0.6';
      buttonElement.style.cursor = 'not-allowed';

      // First check if plan requires payment
      console.log('📡 Checking subscription requirements...');
      const response = await fetch('/subscription/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      console.log('📡 Subscription response status:', response.status);
      const result = await response.json();
      console.log('📡 Subscription response data:', result);
      if (result.success) {
        if (result.requires_payment) {
          // console.log('💰 Payment required, proceeding to payment creation...'); // Cleaned for production
          // Plan requires payment, create payment transaction
          await createPayment(planId, buttonElement);
        } else {
          // console.log('🆓 Free plan, subscription created directly'); // Cleaned for production
          // Free plan, subscription created directly
          alert(translations.subscribeSuccess);
          window.location.reload();
        }
      } else {
        console.log('❌ Subscription failed:', result.error);
        alert(translations.subscribeError + ': ' + result.error);

        // Re-enable button on error
        buttonElement.textContent = originalText;
        buttonElement.disabled = false;
        buttonElement.style.opacity = '1';
        buttonElement.style.cursor = 'pointer';
        buttonStates.delete(planId);
      }
    } catch (error) {
      console.error('❌ Subscription error:', error);
      alert(translations.subscribeFailed + ': ' + error.message);

      // Re-enable button on error
      const originalText = buttonElement.getAttribute('data-original-text') || 'Upgrade';
      buttonElement.textContent = originalText;
      buttonElement.disabled = false;
      buttonElement.style.opacity = '1';
      buttonElement.style.cursor = 'pointer';
      buttonStates.delete(planId);
    }
  }

  async function createPayment(planId, button) {
    try {
      // console.log('🔄 Creating payment for plan:', planId); // Cleaned for production

      // Check if payment is already being processed for this plan
      if (buttonStates.get(planId + '_payment')) {
        // console.log('⚠️ Payment already processing, ignoring click'); // Cleaned for production
        return;
      }

      // Set payment processing state
      buttonStates.set(planId + '_payment', true);

      // Show loading state
      const originalText = button.textContent;
      button.textContent = 'Processing...';
      button.disabled = true;
      button.style.opacity = '0.6';
      button.style.cursor = 'not-allowed';

      // console.log('📡 Sending payment request...'); // Cleaned for production
      const response = await fetch('/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      // console.log('📡 Response status:', response.status); // Cleaned for production
      const result = await response.json();
      // console.log('📡 Response data:', result); // Cleaned for production

      if (result.success) {
        // console.log('✅ Payment creation successful'); // Cleaned for production
        // console.log('🎫 Snap token:', result.snap_token); // Cleaned for production
        // console.log('🔗 Redirect URL:', result.redirect_url); // Cleaned for production

        // Show appropriate message for expired users with downgrades
        if (result.expired_user && result.previous_plan) {
          const isDowngrade = result.plan_name && result.previous_plan &&
                             plans.find(p => p.name === result.plan_name)?.price <
                             plans.find(p => p.name === result.previous_plan)?.price;

          if (isDowngrade) {
            showToast('info', `Downgrading from ${result.previous_plan} to ${result.plan_name} - Full price applies`);
          } else {
            showToast('info', `Purchasing ${result.plan_name} plan - Full price applies`);
          }
        }

        // Check if Snap is available
        if (typeof snap !== 'undefined' && result.snap_token) {
          // console.log('🔄 Using Snap popup for payment...'); // Cleaned for production

          // Store order_id for status checking
          const orderId = result.order_id;

          // Restore button state before opening Snap
          button.textContent = originalText;
          button.disabled = false;
          button.style.opacity = '1';
          button.style.cursor = 'pointer';

          // Open Snap popup
          snap.pay(result.snap_token, {
            onSuccess: function(snapResult) {
              // console.log('✅ Payment successful:', snapResult); // Cleaned for production
              showProcessingDialog('Pembayaran berhasil! Menunggu konfirmasi sistem...');
              // Use the order_id from payment creation response
              checkPaymentStatus(orderId, 3000);
              // Clear payment state
              buttonStates.delete(planId + '_payment');
            },
            onPending: function(snapResult) {
              // console.log('⏳ Payment pending:', snapResult); // Cleaned for production
              showProcessingDialog('Pembayaran sedang diproses. Silakan tunggu konfirmasi sistem...');
              // Use the order_id from payment creation response
              checkPaymentStatus(orderId, 5000);
              // Clear payment state
              buttonStates.delete(planId + '_payment');
            },
            onError: function(snapResult) {
              // console.log('❌ Payment error:', snapResult); // Cleaned for production
              alert('Pembayaran gagal. Silakan coba lagi.');
              // Clear payment state
              buttonStates.delete(planId + '_payment');
            },
            onClose: function() {
              // console.log('🔒 Payment popup closed'); // Cleaned for production
              // User closed the popup, clear payment state
              buttonStates.delete(planId + '_payment');
            }
          });
        } else {
          // Fallback to redirect method if Snap is not available
          // console.log('⚠️ Snap not available, using redirect method...'); // Cleaned for production
          alert('Membuka halaman pembayaran Midtrans...');

          // Open payment page in new tab
          const paymentWindow = window.open(result.redirect_url, '_blank');

          // Restore button state
          button.textContent = originalText;
          button.disabled = false;
          button.style.opacity = '1';
          button.style.cursor = 'pointer';

          // Check if window was blocked
          if (!paymentWindow) {
            alert('Pop-up diblokir! Silakan klik link berikut untuk melanjutkan pembayaran:\n\n' + result.redirect_url);
          } else {
            // Show message to user
            alert('Halaman pembayaran telah dibuka di tab baru. Setelah pembayaran selesai, refresh halaman ini.');
          }

          // Clear payment state
          buttonStates.delete(planId + '_payment');
        }
      } else {
        // console.log('❌ Payment creation failed:', result.error); // Cleaned for production
        alert('Failed to create payment: ' + result.error);
        // Restore button state on error
        button.textContent = originalText;
        button.disabled = false;
        button.style.opacity = '1';
        button.style.cursor = 'pointer';
        // Clear payment state
        buttonStates.delete(planId + '_payment');
      }

    } catch (error) {
      // console.error('Payment creation error:', error); // Cleaned for production
      alert('Failed to create payment. Please try again.');

      // Restore button state
      if (button) {
        button.textContent = button.getAttribute('data-original-text') || 'Subscribe';
        button.disabled = false;
        button.style.opacity = '1';
        button.style.cursor = 'pointer';
      }
      // Clear payment state
      buttonStates.delete(planId + '_payment');
    }
  }



  // Function to check payment status after Snap callback
  async function checkPaymentStatus(orderId, delay = 3000) {
    // console.log('🔍 Checking payment status for order:', orderId); // Cleaned for production

    // Wait for the specified delay to allow webhook processing
    setTimeout(async () => {
      try {
        const response = await fetch(`/payment/status/${orderId}`);
        const result = await response.json();

        // console.log('📊 Payment status result:', result); // Cleaned for production

        // Remove processing dialog before showing final result
        const processingDialog = document.getElementById('processing-dialog');
        if (processingDialog) {
          processingDialog.remove();
        }

        if (result.success) {
          if (result.status === 'success') {
            alert('Pembayaran berhasil dikonfirmasi! Subscription Anda telah diaktivasi.');
            window.location.reload();
          } else if (result.status === 'pending') {
            // For pending status, just reload without alert since user already knows it's processing
            window.location.reload();
          } else {
            alert('Status pembayaran: ' + result.status + '. Halaman akan dimuat ulang.');
            window.location.reload();
          }
        } else {
          // console.warn('⚠️ Could not verify payment status, reloading page...'); // Cleaned for production
          window.location.reload();
        }
      } catch (error) {
        // console.error('❌ Error checking payment status:', error); // Cleaned for production
        alert('Tidak dapat memverifikasi status pembayaran. Halaman akan dimuat ulang.');
        window.location.reload();
      }
    }, delay);
  }

  // Function to show prorated preview
  async function showProratedPreview(planId, planName, planPrice) {
    try {
      const previewContainer = document.getElementById(`prorated-preview-${planId}`);
      const previewBtn = document.getElementById(`preview-btn-${planId}`);

      if (!previewContainer) {
        console.error('Preview container not found for plan:', planId);
        return;
      }

      if (!previewBtn) {
        console.error('Preview button not found for plan:', planId);
        return;
      }

      // Show loading state
      const locale = '<%= locale %>';
      previewBtn.textContent = locale === 'id' ? '⏳ Menghitung...' : '⏳ Calculating...';
      previewBtn.disabled = true;

      // Calculate prorated pricing
      const response = await fetch('/payment/calculate-upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId })
      });

      const result = await response.json();

      if (result.success) {
        const calc = result.calculation;

        let content = '';
        if (calc.isUpgrade) {
          if (locale === 'id') {
            content = `
              <div class="text-xs space-y-1">
                <div class="flex justify-between">
                  <span>Plan ${calc.newPlan}:</span>
                  <span>${calc.newPlanPriceFormatted}</span>
                </div>
                <div class="flex justify-between text-green-400">
                  <span>Kredit plan lama (${calc.remainingDays} hari):</span>
                  <span>-${calc.savingsFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Harga upgrade:</span>
                  <span>${calc.upgradePriceFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Biaya admin (1%):</span>
                  <span>${calc.adminFeeFormatted}</span>
                </div>
                <hr class="border-gray-600 my-2">
                <div class="flex justify-between font-bold text-yellow-400">
                  <span>Total Pembayaran:</span>
                  <span>${calc.totalPaymentFormatted}</span>
                </div>
                <div class="text-green-400 text-xs mt-1">
                  💰 Hemat ${calc.savingsFormatted} dari plan lama!
                </div>
              </div>
            `;
          } else {
            content = `
              <div class="text-xs space-y-1">
                <div class="flex justify-between">
                  <span>${calc.newPlan} Plan:</span>
                  <span>${calc.newPlanPriceFormatted}</span>
                </div>
                <div class="flex justify-between text-green-400">
                  <span>Old plan credit (${calc.remainingDays} days):</span>
                  <span>-${calc.savingsFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Upgrade price:</span>
                  <span>${calc.upgradePriceFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Admin fee (1%):</span>
                  <span>${calc.adminFeeFormatted}</span>
                </div>
                <hr class="border-gray-600 my-2">
                <div class="flex justify-between font-bold text-yellow-400">
                  <span>Total Payment:</span>
                  <span>${calc.totalPaymentFormatted}</span>
                </div>
                <div class="text-green-400 text-xs mt-1">
                  💰 Save ${calc.savingsFormatted} from your old plan!
                </div>
              </div>
            `;
          }
        } else {
          if (locale === 'id') {
            content = `
              <div class="text-xs space-y-1">
                <div class="flex justify-between">
                  <span>Plan ${calc.newPlan}:</span>
                  <span>${calc.newPlanPriceFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Biaya admin (1%):</span>
                  <span>${calc.adminFeeFormatted}</span>
                </div>
                <hr class="border-gray-600 my-2">
                <div class="flex justify-between font-bold text-yellow-400">
                  <span>Total Pembayaran:</span>
                  <span>${calc.totalPaymentFormatted}</span>
                </div>
              </div>
            `;
          } else {
            content = `
              <div class="text-xs space-y-1">
                <div class="flex justify-between">
                  <span>${calc.newPlan} Plan:</span>
                  <span>${calc.newPlanPriceFormatted}</span>
                </div>
                <div class="flex justify-between">
                  <span>Admin fee (1%):</span>
                  <span>${calc.adminFeeFormatted}</span>
                </div>
                <hr class="border-gray-600 my-2">
                <div class="flex justify-between font-bold text-yellow-400">
                  <span>Total Payment:</span>
                  <span>${calc.totalPaymentFormatted}</span>
                </div>
              </div>
            `;
          }
        }

        previewContainer.querySelector('.prorated-content').innerHTML = content;
        previewContainer.style.display = 'block';

        // Update button
        previewBtn.textContent = locale === 'id' ? '✅ Harga Dihitung' : '✅ Price Calculated';
        previewBtn.disabled = true;
        previewBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        previewBtn.classList.add('bg-green-600');

      } else {
        throw new Error(result.error || 'Failed to calculate pricing');
      }

    } catch (error) {
      console.error('Error calculating prorated preview:', error);
      const errorMsg = locale === 'id' ? 'Gagal menghitung harga: ' : 'Failed to calculate price: ';
      alert(errorMsg + error.message);

      // Reset button completely
      const resetText = locale === 'id' ? '💰 Lihat Harga untuk Anda' : '💰 See Your Price';
      previewBtn.textContent = resetText;
      previewBtn.disabled = false;
      previewBtn.classList.remove('bg-green-600');
      previewBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
    }
  }

  // Function to handle plan action (upgrade/subscribe)
  async function handlePlanAction(planId, buttonElement) {
    try {
      console.log('🔄 handlePlanAction called with:', { planId, buttonElement: !!buttonElement });
      // Use the existing subscribeToPlan function
      await subscribeToPlan(planId, buttonElement);
    } catch (error) {
      console.error('❌ Error in handlePlanAction:', error);
      alert('Failed to process plan action: ' + error.message);
    }
  }

  // Function to preview trial details
  async function previewTrial(planId) {
    const previewContainer = document.getElementById(`prorated-preview-${planId}`);
    const previewBtn = document.getElementById(`preview-trial-btn-${planId}`);

    if (!previewContainer || !previewBtn) {
      console.error('Preview elements not found for plan:', planId);
      return;
    }

    const originalBtnText = previewBtn.innerHTML;
    previewBtn.innerHTML = 'Loading...';
    previewBtn.disabled = true;

    try {
      const response = await fetch('/subscription/preview-trial', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ planId })
      });

      const result = await response.json();

      if (result.success) {
        const details = result.trial_details;
        const content = `
          <div class="text-xs space-y-1">
            <div class="flex justify-between">
              <span>${details.trial_days} days free trial</span>
            </div>
            <div class="flex justify-between">
              <span>Streaming Slots:</span>
              <span>${details.max_streaming_slots}</span>
            </div>
            <div class="flex justify-between">
              <span>Storage:</span>
              <span>${details.max_storage_gb} GB</span>
            </div>
          </div>
        `;
        previewContainer.querySelector('.prorated-content').innerHTML = content;
        previewContainer.style.display = 'block';
        previewBtn.innerHTML = 'Trial Details';
        previewBtn.classList.add('bg-green-600');

      } else {
        alert('Failed to get trial details: ' + result.error);
        previewBtn.innerHTML = originalBtnText;
        previewBtn.disabled = false;
      }
    } catch (error) {
      console.error('Error previewing trial:', error);
      alert('An error occurred while fetching trial details.');
      previewBtn.innerHTML = originalBtnText;
      previewBtn.disabled = false;
    }
  }

</script>
