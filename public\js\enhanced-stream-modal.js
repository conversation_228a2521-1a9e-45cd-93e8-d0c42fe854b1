/**
 * Enhanced Stream Modal with Improved UI Responsiveness
 * Addresses unresponsive button clicks and slow response times
 */

// Configuration
const UI_CONFIG = {
  REQUEST_TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  DEBOUNCE_DELAY: 300, // 300ms
  NOTIFICATION_DURATION: 5000 // 5 seconds
};

// Use existing notification system from notifications.js
// The notifications variable is already available globally from notifications.js
// No need to redeclare it - just use the global one directly

// Enhanced button state management
class ButtonStateManager {
  constructor(button) {
    this.button = button;
    this.originalState = {
      text: button.textContent || button.innerHTML,
      disabled: button.disabled,
      className: button.className
    };
  }

  setLoading(text = 'Processing...') {
    this.button.disabled = true;
    this.button.innerHTML = `<i class="ti ti-loader animate-spin mr-2"></i>${text}`;
    this.button.classList.add('opacity-75', 'cursor-not-allowed');
  }

  setSuccess(text = 'Success!') {
    this.button.innerHTML = `<i class="ti ti-check mr-2"></i>${text}`;
    this.button.classList.remove('opacity-75', 'cursor-not-allowed');
    this.button.classList.add('bg-green-600', 'hover:bg-green-700');
  }

  setError(text = 'Error') {
    this.button.innerHTML = `<i class="ti ti-alert-circle mr-2"></i>${text}`;
    this.button.classList.remove('opacity-75', 'cursor-not-allowed');
    this.button.classList.add('bg-red-600', 'hover:bg-red-700');
  }

  restore() {
    this.button.textContent = this.originalState.text;
    this.button.disabled = this.originalState.disabled;
    this.button.className = this.originalState.className;
  }
}

// Utility function to extract error messages from various response formats
function extractErrorMessage(errorData, defaultMessage = 'An error occurred') {
  if (typeof errorData === 'string') {
    return errorData;
  }

  if (errorData && typeof errorData === 'object') {
    // Try different common error message properties
    if (errorData.message) return errorData.message;
    if (errorData.error) {
      if (typeof errorData.error === 'string') return errorData.error;
      if (errorData.error.message) return errorData.error.message;
      if (errorData.error.userMessage) return errorData.error.userMessage;
    }
    if (errorData.userMessage) return errorData.userMessage;

    // If it's an object with no recognizable error properties, try to get first meaningful value
    const keys = Object.keys(errorData);
    if (keys.length > 0) {
      const firstValue = errorData[keys[0]];
      if (typeof firstValue === 'string' && firstValue.length > 0) {
        return firstValue;
      }
    }
  }

  return defaultMessage;
}

// Enhanced API request handler with timeout and retry
class APIRequestHandler {
  static async makeRequest(url, options = {}, retryCount = 0) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), UI_CONFIG.REQUEST_TIMEOUT);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Try to get more detailed error information from response
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = extractErrorMessage(errorData, errorMessage);
        } catch (parseError) {
          // If we can't parse the error response, use the default message
        }
        throw new Error(errorMessage);
      }

      // Check if we got an HTML response instead of JSON (indicates redirect to login)
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        throw new Error('Your session has expired. Please refresh the page and try again.');
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      // Handle timeout
      if (error.name === 'AbortError') {
        throw new Error('Request timed out. Please try again.');
      }

      // Retry logic for network errors
      if (retryCount < UI_CONFIG.RETRY_ATTEMPTS && this.isRetryableError(error)) {
        await this.delay(UI_CONFIG.RETRY_DELAY * Math.pow(2, retryCount));
        return this.makeRequest(url, options, retryCount + 1);
      }

      throw error;
    }
  }

  static isRetryableError(error) {
    return error.message.includes('fetch') || 
           error.message.includes('network') ||
           error.message.includes('timeout');
  }

  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Enhanced stream creation handler
class StreamCreationHandler {
  constructor() {
    this.isProcessing = false;
    this.debounceTimer = null;
  }

  async handleSubmit(event) {
    console.log('🔥 Enhanced stream modal: handleSubmit called!');
    console.log('🔥 Event in handleSubmit:', {
      type: event.type,
      target: event.target.id,
      defaultPrevented: event.defaultPrevented
    });
    event.preventDefault();

    // Prevent double submission
    if (this.isProcessing) {
      console.log('Enhanced stream modal: Already processing, showing warning');
      notifications.warning('Please Wait', 'Stream creation is already in progress.');
      return;
    }

    // Debounce rapid clicks
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.processSubmission();
    }, UI_CONFIG.DEBOUNCE_DELAY);
  }

  async processSubmission() {
    this.isProcessing = true;

    const submitBtn = document.querySelector('button[type="submit"][form="newStreamForm"]');
    const buttonManager = new ButtonStateManager(submitBtn);

    try {
      // Show loading state
      buttonManager.setLoading('Creating Stream...');

      // Validate form data
      console.log('Enhanced stream modal: Collecting form data');
      const formData = this.collectFormData();
      console.log('Enhanced stream modal: Form data collected:', formData);

      const validationResult = this.validateFormData(formData);
      console.log('Enhanced stream modal: Validation result:', validationResult);

      if (!validationResult.isValid) {
        console.log('Enhanced stream modal: Validation failed:', validationResult.error);
        throw new Error(validationResult.error);
      }

      // Show progress notification
      const progressNotification = notifications.info('Creating Stream', 'Please wait while we create your stream...');

      // Make API request with improved error handling
      const result = await APIRequestHandler.makeRequest('/api/streams', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.getCSRFToken() ? { 'X-CSRF-Token': this.getCSRFToken() } : {})
        },
        body: JSON.stringify(formData)
      });

      // Remove progress notification
      notifications.remove(progressNotification);

      // Check if we got redirected to login (session expired)
      if (typeof result === 'string' && result.includes('<!DOCTYPE html>')) {
        throw new Error('Your session has expired. Please refresh the page and try again.');
      }

      if (result && result.success) {
        buttonManager.setSuccess('Stream Created!');

        // Show modal notification that waits for user acknowledgment
        notifications.alert(
          'Stream created successfully! Your new stream is ready to use.',
          'Success!',
          'success'
        ).then(() => {
          // Only refresh after user clicks OK
          this.closeModal();
          window.location.reload();
        });
      } else {
        const errorMessage = result?.error || 'Failed to create stream';
        throw new Error(errorMessage);
      }

    } catch (error) {
      console.error('Stream creation error:', error);
      buttonManager.setError('Creation Failed');

      // Show detailed error message
      this.handleError(error);

      // Restore button after delay
      setTimeout(() => {
        buttonManager.restore();
      }, 3000);
    } finally {
      this.isProcessing = false;
    }
  }

  collectFormData() {
    return {
      streamTitle: document.getElementById('streamTitle')?.value || '',
      videoId: document.getElementById('selectedVideoId')?.value || '',
      rtmpUrl: document.getElementById('rtmpUrl')?.value || '',
      streamKey: document.getElementById('streamKey')?.value || '',
      bitrate: document.querySelector('select[name="bitrate"]')?.value || '2500',
      fps: document.querySelector('select[name="fps"]')?.value || '30',
      loopVideo: document.querySelector('input[name="loopVideo"]')?.checked || false,
      orientation: window.currentOrientation || 'horizontal',
      resolution: document.getElementById('currentResolution')?.textContent || '1280x720',
      useAdvancedSettings: !document.getElementById('advancedSettingsContent')?.classList.contains('hidden'),
      scheduleTime: document.getElementById('scheduleTime')?.value || '',
      scheduleTimezone: document.getElementById('displayTimezone')?.value || 'Asia/Jakarta',
      duration: document.getElementById('duration')?.value || ''
    };
  }

  validateFormData(formData) {
    if (!formData.streamTitle.trim()) {
      return { isValid: false, error: 'Stream title is required' };
    }
    if (!formData.rtmpUrl.trim()) {
      return { isValid: false, error: 'RTMP URL is required' };
    }
    if (!formData.streamKey.trim()) {
      return { isValid: false, error: 'Stream key is required' };
    }
    if (!formData.videoId) {
      return { isValid: false, error: 'Please select a video for streaming' };
    }
    if (!window.isStreamKeyValid) {
      return { isValid: false, error: 'Please use a different stream key. This one is already in use.' };
    }

    return { isValid: true };
  }

  handleError(error) {
    let title = 'Creation Failed';
    let message = error.message || 'An unexpected error occurred';

    // Handle specific error types and codes
    if (message.includes('subscription') || message.includes('Preview plan')) {
      title = 'Subscription Required';
      if (message.includes('Preview plan')) {
        message = 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.';
      } else {
        message = 'You need an active subscription to create streams. Please upgrade your plan.';
      }
    } else if (message.includes('quota') || message.includes('limit') || message.includes('streaming limit')) {
      title = 'Streaming Limit Reached';
      message = 'You have reached your streaming limit. Please upgrade your plan or stop an existing stream.';
    } else if (message.includes('RTMP')) {
      title = 'Invalid RTMP Configuration';
      message = 'Please check your RTMP URL and stream key configuration.';
    } else if (message.includes('timeout')) {
      title = 'Request Timeout';
      message = 'The request took too long to complete. Please try again.';
    } else if (message.includes('HTTP 403') || message.includes('Forbidden')) {
      title = 'Access Denied';
      message = 'You do not have permission to perform this action. Please check your subscription status.';
    } else if (message.includes('HTTP 401') || message.includes('session has expired')) {
      title = 'Session Expired';
      message = 'Your session has expired. Please refresh the page and log in again.';
      // Auto-refresh after showing error
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } else if (message.includes('Title is required')) {
      title = 'Missing Title';
      message = 'Please enter a stream title before creating the stream.';
    } else if (message.includes('RTMP URL is required')) {
      title = 'Missing RTMP URL';
      message = 'Please enter a valid RTMP URL for your streaming platform.';
    } else if (message.includes('Stream key is required')) {
      title = 'Missing Stream Key';
      message = 'Please enter your stream key from your streaming platform.';
    } else if (message.includes('Please select a video')) {
      title = 'No Video Selected';
      message = 'Please select a video file to stream before creating the stream.';
    } else if (message.includes('stream key already in use')) {
      title = 'Duplicate Stream Key';
      message = 'This stream key is already being used. Please use a different stream key.';
    }

    notifications.error(title, message);
  }

  getCSRFToken() {
    return document.querySelector('input[name="_csrf"]')?.value;
  }

  closeModal() {
    if (typeof window.closeNewStreamModal === 'function') {
      window.closeNewStreamModal();
    }
  }
}

// Enhanced stream control handler
class StreamControlHandler {
  static async startStream(streamId) {
    if (!streamId) return;

    const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
    const buttonManagers = Array.from(actionBtns).map(btn => new ButtonStateManager(btn));

    try {
      // Set loading state
      buttonManagers.forEach(manager => manager.setLoading('Starting...'));

      const result = await APIRequestHandler.makeRequest(`/api/streams/${streamId}/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'live' })
      });

      if (result.success) {
        buttonManagers.forEach(manager => manager.setSuccess('Started!'));
        const streamMode = result.isAdvancedMode ? "Advanced mode" : "Simple mode";
        notifications.success('Stream Started!', `Stream started successfully in ${streamMode}`);
        
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        // Use extractErrorMessage for better error handling
        const errorMessage = extractErrorMessage(result, 'Failed to start stream');
        throw new Error(errorMessage);
      }

    } catch (error) {
      console.error('Stream start error:', error);
      buttonManagers.forEach(manager => manager.setError('Failed'));

      let title = 'Start Failed';
      let message = error.message || 'Failed to start stream';

      // Handle specific error types and codes
      if (message.includes('subscription') || message.includes('Preview plan')) {
        title = 'Subscription Required';
        if (message.includes('Preview plan')) {
          message = 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.';
        } else {
          message = 'Your subscription has expired. Please renew to continue streaming.';
        }
      } else if (message.includes('quota') || message.includes('limit') || message.includes('streaming limit')) {
        title = 'Streaming Limit Reached';
        message = 'You have reached your streaming limit. Please upgrade your plan or stop an existing stream.';
      } else if (message.includes('HTTP 403') || message.includes('Forbidden')) {
        title = 'Access Denied';
        message = 'You do not have permission to start this stream. Please check your subscription status.';
      } else if (message.includes('HTTP 401')) {
        title = 'Authentication Required';
        message = 'Your session has expired. Please refresh the page and try again.';
      }

      notifications.error(title, message);

      // Restore buttons after delay
      setTimeout(() => {
        buttonManagers.forEach(manager => manager.restore());
      }, 3000);
    }
  }

  static async stopStream(streamId) {
    if (!streamId) return;

    const actionBtns = document.querySelectorAll(`[data-stream-id="${streamId}"] .action-btn`);
    const buttonManagers = Array.from(actionBtns).map(btn => new ButtonStateManager(btn));

    try {
      buttonManagers.forEach(manager => manager.setLoading('Stopping...'));

      const result = await APIRequestHandler.makeRequest(`/api/streams/${streamId}/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'offline' })
      });

      if (result.success) {
        buttonManagers.forEach(manager => manager.setSuccess('Stopped!'));
        notifications.success('Stream Stopped!', 'Stream stopped successfully');
        
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        // Use extractErrorMessage for better error handling
        const errorMessage = extractErrorMessage(result, 'Failed to stop stream');
        throw new Error(errorMessage);
      }

    } catch (error) {
      console.error('Stream stop error:', error);
      buttonManagers.forEach(manager => manager.setError('Failed'));

      let title = 'Stop Failed';
      let message = error.message || 'Failed to stop stream';

      // Handle specific error types
      if (message.includes('HTTP 403') || message.includes('Forbidden')) {
        title = 'Access Denied';
        message = 'You do not have permission to stop this stream.';
      } else if (message.includes('HTTP 401')) {
        title = 'Authentication Required';
        message = 'Your session has expired. Please refresh the page and try again.';
      }

      notifications.error(title, message);

      setTimeout(() => {
        buttonManagers.forEach(manager => manager.restore());
      }, 3000);
    }
  }
}

// Add global flag to indicate script is loaded
console.log('🚀 Enhanced stream modal script is loading...');
window.enhancedStreamModalLoaded = true;

// Initialize enhanced handlers
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 Enhanced stream modal: DOMContentLoaded fired');
  console.log('🔍 Enhanced stream modal: Checking for required dependencies...');
  console.log('- notifications available:', typeof notifications !== 'undefined');
  console.log('- UI_CONFIG available:', typeof UI_CONFIG !== 'undefined');
  console.log('- ButtonStateManager available:', typeof ButtonStateManager !== 'undefined');

  // Add debugging to see if this script is actually running
  window.enhancedModalLoaded = true;
  console.log('✅ Enhanced modal script is running!');

  // Initialize stream creation handler
  const streamForm = document.getElementById('newStreamForm');
  console.log('📝 Enhanced stream modal: Form element found:', !!streamForm);

  if (streamForm) {
    console.log('📝 Enhanced stream modal: Form details:');
    console.log('- Form ID:', streamForm.id);
    console.log('- Form action:', streamForm.action);
    console.log('- Form method:', streamForm.method);
  }

  if (streamForm) {
    console.log('Enhanced stream modal: Attaching event handler');
    const creationHandler = new StreamCreationHandler();

    // Override the form's onsubmit property to ensure we capture all submissions
    streamForm.onsubmit = function(e) {
      console.log('🎯 Enhanced stream modal: Form onsubmit captured!');
      console.log('🎯 Event details:', {
        type: e.type,
        target: e.target.id,
        defaultPrevented: e.defaultPrevented,
        bubbles: e.bubbles,
        cancelable: e.cancelable
      });

      e.preventDefault(); // Always prevent default
      e.stopPropagation(); // Stop event bubbling

      console.log('🎯 After preventDefault - defaultPrevented:', e.defaultPrevented);

      // Call our handler
      console.log('🎯 Calling creationHandler.handleSubmit...');
      creationHandler.handleSubmit(e);

      return false; // Ensure form doesn't submit
    };

    // Also add event listener as backup
    streamForm.addEventListener('submit', (e) => {
      console.log('🎯 Enhanced stream modal: Form addEventListener captured!');
      e.preventDefault();
      e.stopPropagation();
      creationHandler.handleSubmit(e);
      return false;
    }, true);

    console.log('Enhanced stream modal: Event handler attached successfully');

    // Also intercept the submit button click directly
    const submitButton = document.querySelector('button[type="submit"][form="newStreamForm"]');
    if (submitButton) {
      console.log('📝 Enhanced stream modal: Found submit button, attaching click handler');
      submitButton.addEventListener('click', (e) => {
        console.log('🎯 Enhanced stream modal: Submit button clicked!');
        e.preventDefault();
        e.stopPropagation();

        // Trigger our handler directly
        console.log('🎯 Calling creationHandler.handleSubmit directly from button click...');
        creationHandler.handleSubmit(e);

        return false;
      }, true);
    }

    // Add debugging for form elements
    console.log('Enhanced stream modal: Form elements check:');
    console.log('- streamTitle:', !!document.getElementById('streamTitle'));
    console.log('- selectedVideoId:', !!document.getElementById('selectedVideoId'));
    console.log('- rtmpUrl:', !!document.getElementById('rtmpUrl'));
    console.log('- streamKey:', !!document.getElementById('streamKey'));
    console.log('- submit button:', !!submitButton);
  } else {
    console.error('Enhanced stream modal: newStreamForm not found!');
  }

  // Replace global functions with enhanced versions
  window.startStream = StreamControlHandler.startStream;
  window.stopStream = StreamControlHandler.stopStream;
  window.notifications = notifications;
  window.extractErrorMessage = extractErrorMessage;

  // Add test function for debugging
  window.testStreamCreation = function() {
    console.log('🧪 Testing stream creation functionality...');

    const form = document.getElementById('newStreamForm');
    if (!form) {
      console.error('❌ Form not found');
      return;
    }

    // Fill test data
    const streamTitle = document.getElementById('streamTitle');
    const rtmpUrl = document.getElementById('rtmpUrl');
    const streamKey = document.getElementById('streamKey');
    const videoId = document.getElementById('selectedVideoId');

    if (streamTitle) streamTitle.value = 'Test Stream ' + Date.now();
    if (rtmpUrl) rtmpUrl.value = 'rtmp://a.rtmp.youtube.com/live2';
    if (streamKey) streamKey.value = 'test-key-' + Date.now();
    if (videoId) videoId.value = 'dd49eb22-f50b-4792-8167-fc8d2ba2ca68';

    console.log('✅ Test data filled');

    // Try multiple ways to trigger submission
    console.log('🧪 Method 1: Triggering form.onsubmit directly');
    if (form.onsubmit) {
      const fakeEvent = { preventDefault: () => {}, stopPropagation: () => {}, type: 'submit', target: form };
      form.onsubmit(fakeEvent);
    }

    console.log('🧪 Method 2: Dispatching submit event');
    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
    form.dispatchEvent(submitEvent);

    console.log('🧪 Method 3: Clicking submit button');
    const submitButton = document.querySelector('button[type="submit"][form="newStreamForm"]');
    if (submitButton) {
      submitButton.click();
    }

    console.log('✅ All test methods triggered');
  };

  // Add function to check if enhanced modal is loaded
  window.checkEnhancedModal = function() {
    console.log('🔍 Enhanced Modal Status Check:');
    console.log('- Enhanced modal script loaded:', true);
    console.log('- Form found:', !!document.getElementById('newStreamForm'));
    console.log('- Submit button found:', !!document.querySelector('button[type="submit"][form="newStreamForm"]'));
    console.log('- Form onsubmit handler:', !!document.getElementById('newStreamForm')?.onsubmit);
    console.log('- StreamCreationHandler available:', typeof StreamCreationHandler !== 'undefined');
    console.log('- Notifications available:', typeof notifications !== 'undefined');
    console.log('- UI_CONFIG available:', typeof UI_CONFIG !== 'undefined');
    console.log('- ButtonStateManager available:', typeof ButtonStateManager !== 'undefined');
  };

  // Store reference to the form for other functions
  window.enhancedStreamForm = streamForm;

  console.log('Enhanced stream modal: Initialization complete');
  console.log('💡 Use window.testStreamCreation() to test stream creation');
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    NotificationSystem,
    ButtonStateManager,
    APIRequestHandler,
    StreamCreationHandler,
    StreamControlHandler,
    extractErrorMessage
  };
}
