/**
 * Pub/Sub Service using Dragonfly for real-time notifications
 * Handles real-time communication across the application
 */

class PubSubService {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.subscribers = new Map(); // Local event handlers
    this.channels = {
      STREAM_STATUS: 'stream:status',
      STREAM_START: 'stream:start',
      STREAM_STOP: 'stream:stop',
      STREAM_ERROR: 'stream:error',
      USER_NOTIFICATION: 'user:notification',
      SYSTEM_ALERT: 'system:alert',
      LOAD_BALANCER: 'loadbalancer:update',
      VIDEO_PROCESSING: 'video:processing',
      CACHE_INVALIDATION: 'cache:invalidate'
    };
    
    this.stats = {
      published: 0,
      received: 0,
      errors: 0,
      subscribers: 0
    };

    this.init();
  }

  async init() {
    try {
      if (!this.isProduction) {
        // console.log('💾 [PubSub] Using local event system only'); // Removed for production
      }
    } catch (error) {
      console.error('❌ [PubSub] Initialization error:', error.message);
    }
  }

  setupSubscriptions() {
    if (!this.subClient) return;

    // Subscribe to all channels
    Object.values(this.channels).forEach(channel => {
      this.subClient.subscribe(channel);
    });

    // Handle incoming messages
    this.subClient.on('message', (channel, message) => {
      this.handleMessage(channel, message);
    });

    this.subClient.on('error', (error) => {
      this.stats.errors++;
      console.error('❌ [PubSub] Subscriber error:', error.message);
    });
  }

  handleMessage(channel, message) {
    try {
      this.stats.received++;
      const data = JSON.parse(message);
      
      // Emit to local subscribers
      const handlers = this.subscribers.get(channel);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(data);
          } catch (error) {
            console.error(`❌ [PubSub] Handler error for channel ${channel}:`, error.message);
          }
        });
      }

      if (!this.isProduction) {
        // console.log(`📨 [PubSub] Received message on ${channel}:`, data); // Removed for production
      }
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [PubSub] Message parsing error:', error.message);
    }
  }

  // Publish message to channel
  async publish(channel, data) {
    try {
      this.stats.published++;
      const message = JSON.stringify(data);

      // Emit locally only
      this.handleMessage(channel, message);

      if (!this.isProduction) {
        // console.log(`📤 [PubSub] Published to ${channel}:`, data); // Removed for production
      }

      return true;
    } catch (error) {
      this.stats.errors++;
      console.error('❌ [PubSub] Publish error:', error.message);
      return false;
    }
  }

  // Subscribe to channel with handler
  subscribe(channel, handler) {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set());
    }
    
    this.subscribers.get(channel).add(handler);
    this.stats.subscribers++;
    
    if (!this.isProduction) {
      // console.log(`🔔 [PubSub] Subscribed to ${channel}`); // Removed for production
    }
  }

  // Unsubscribe from channel
  unsubscribe(channel, handler) {
    const handlers = this.subscribers.get(channel);
    if (handlers) {
      handlers.delete(handler);
      this.stats.subscribers--;
      
      if (handlers.size === 0) {
        this.subscribers.delete(channel);
      }
    }
  }

  // Stream-specific methods
  async publishStreamStatus(streamId, status, userId = null) {
    return this.publish(this.channels.STREAM_STATUS, {
      streamId,
      status,
      userId,
      timestamp: new Date().toISOString()
    });
  }

  async publishStreamStart(streamId, userId, streamData) {
    return this.publish(this.channels.STREAM_START, {
      streamId,
      userId,
      streamData,
      timestamp: new Date().toISOString()
    });
  }

  async publishStreamStop(streamId, userId, reason = 'manual') {
    return this.publish(this.channels.STREAM_STOP, {
      streamId,
      userId,
      reason,
      timestamp: new Date().toISOString()
    });
  }

  async publishStreamError(streamId, userId, error) {
    return this.publish(this.channels.STREAM_ERROR, {
      streamId,
      userId,
      error: error.message || error,
      timestamp: new Date().toISOString()
    });
  }

  // User notification methods
  async publishUserNotification(userId, notification) {
    return this.publish(this.channels.USER_NOTIFICATION, {
      userId,
      notification,
      timestamp: new Date().toISOString()
    });
  }

  // System alert methods
  async publishSystemAlert(type, message, severity = 'info') {
    return this.publish(this.channels.SYSTEM_ALERT, {
      type,
      message,
      severity,
      timestamp: new Date().toISOString()
    });
  }

  // Load balancer updates
  async publishLoadBalancerUpdate(data) {
    return this.publish(this.channels.LOAD_BALANCER, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  // Video processing updates
  async publishVideoProcessing(videoId, userId, status, progress = null) {
    return this.publish(this.channels.VIDEO_PROCESSING, {
      videoId,
      userId,
      status,
      progress,
      timestamp: new Date().toISOString()
    });
  }

  // Cache invalidation
  async publishCacheInvalidation(pattern, type = 'pattern') {
    return this.publish(this.channels.CACHE_INVALIDATION, {
      pattern,
      type,
      timestamp: new Date().toISOString()
    });
  }

  // Get statistics
  getStats() {
    return {
      ...this.stats,
      channels: Object.keys(this.channels).length,
      activeSubscribers: this.subscribers.size,
      connected: false
    };
  }

  // Health check
  async healthCheck() {
    try {
      const stats = this.getStats();

      return {
        status: 'healthy',
        pubsub: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Cleanup
  async cleanup() {
    try {
      this.subscribers.clear();

      if (!this.isProduction) {
        // console.log('🧹 [PubSub] Cleanup completed'); // Removed for production
      }
    } catch (error) {
      console.error('❌ [PubSub] Cleanup error:', error.message);
    }
  }
}

// Create singleton instance
const pubSubService = new PubSubService();

module.exports = pubSubService;
