# 🌐 Network Redundancy Guide untuk 24/7 Streaming

## Overview
Panduan ini menjelaskan cara setup network redundancy untuk meningkatkan uptime streaming mendekati 24/7.

## 🔧 Setup Dual Internet Connection

### 1. Primary + Backup ISP
```bash
# Setup failover routing (Linux)
# Primary: ISP A (eth0)
# Backup: ISP B (eth1)

# Install network bonding
sudo apt-get install ifenslave

# Configure bonding for failover
echo "alias bond0 bonding" >> /etc/modprobe.d/aliases
echo "options bond0 mode=1 miimon=100" >> /etc/modprobe.d/aliases

# Setup network interfaces
cat >> /etc/network/interfaces << EOF
auto bond0
iface bond0 inet static
    address *************
    netmask *************
    gateway ***********
    bond-slaves eth0 eth1
    bond-mode active-backup
    bond-miimon 100
EOF
```

### 2. Load Balancer Configuration
```javascript
// network-load-balancer.js
const net = require('net');
const { spawn } = require('child_process');

class NetworkLoadBalancer {
  constructor() {
    this.connections = [
      { name: 'Primary ISP', interface: 'eth0', priority: 1, active: true },
      { name: 'Backup ISP', interface: 'eth1', priority: 2, active: false }
    ];
  }

  async checkConnectivity(interface) {
    return new Promise((resolve) => {
      const ping = spawn('ping', ['-c', '1', '-I', interface, '*******']);
      ping.on('close', (code) => {
        resolve(code === 0);
      });
    });
  }

  async switchToBackup() {
    console.log('🔄 Switching to backup connection...');
    // Implementation for switching network interface
    // This would integrate with your streaming service
  }
}
```

## 📡 Multiple RTMP Endpoints

### 1. YouTube Backup Servers
```javascript
// youtube-backup-servers.js
const YOUTUBE_RTMP_SERVERS = [
  'rtmp://a.rtmp.youtube.com/live2',      // Primary
  'rtmp://b.rtmp.youtube.com/live2',      // Backup 1
  'rtmp://c.rtmp.youtube.com/live2'       // Backup 2
];

class YouTubeFailover {
  constructor(streamKey) {
    this.streamKey = streamKey;
    this.currentServerIndex = 0;
    this.servers = YOUTUBE_RTMP_SERVERS;
  }

  getCurrentRTMPUrl() {
    return this.servers[this.currentServerIndex];
  }

  switchToNextServer() {
    this.currentServerIndex = (this.currentServerIndex + 1) % this.servers.length;
    console.log(`🔄 Switching to YouTube server: ${this.getCurrentRTMPUrl()}`);
    return this.getCurrentRTMPUrl();
  }
}
```

## 🖥️ Server Redundancy

### 1. Dual Server Setup
```yaml
# docker-compose.yml for redundant setup
version: '3.8'
services:
  streamonpod-primary:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SERVER_ROLE=primary
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  streamonpod-backup:
    build: .
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - SERVER_ROLE=backup
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    depends_on:
      - streamonpod-primary

  nginx-loadbalancer:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - streamonpod-primary
      - streamonpod-backup
```

### 2. Health Check & Failover
```javascript
// server-health-monitor.js
const http = require('http');

class ServerHealthMonitor {
  constructor() {
    this.servers = [
      { url: 'http://localhost:3000', role: 'primary', healthy: true },
      { url: 'http://localhost:3001', role: 'backup', healthy: true }
    ];
  }

  async checkHealth(server) {
    return new Promise((resolve) => {
      const req = http.get(`${server.url}/health`, (res) => {
        resolve(res.statusCode === 200);
      });
      
      req.on('error', () => resolve(false));
      req.setTimeout(5000, () => {
        req.abort();
        resolve(false);
      });
    });
  }

  async monitorServers() {
    for (const server of this.servers) {
      const healthy = await this.checkHealth(server);
      
      if (!healthy && server.healthy) {
        console.log(`❌ Server ${server.role} is down!`);
        await this.handleServerFailure(server);
      } else if (healthy && !server.healthy) {
        console.log(`✅ Server ${server.role} is back online!`);
      }
      
      server.healthy = healthy;
    }
  }

  async handleServerFailure(failedServer) {
    if (failedServer.role === 'primary') {
      console.log('🔄 Switching to backup server...');
      // Implement failover logic
    }
  }
}
```

## 📊 Monitoring & Alerting

### 1. Uptime Monitoring
```javascript
// uptime-monitor.js
const nodemailer = require('nodemailer');

class UptimeMonitor {
  constructor() {
    this.alertThreshold = 30; // seconds
    this.lastStreamCheck = new Date();
  }

  async sendAlert(message) {
    // Email alert configuration
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: '<EMAIL>',
        pass: 'your-app-password'
      }
    });

    await transporter.sendMail({
      from: 'StreamOnPod Monitor <<EMAIL>>',
      to: '<EMAIL>',
      subject: '🚨 StreamOnPod Alert',
      text: message
    });
  }

  async checkStreamStatus() {
    // Check if streams are running
    const { db } = require('./db/database');
    
    const liveStreams = await new Promise((resolve, reject) => {
      db.all("SELECT COUNT(*) as count FROM streams WHERE status = 'live'", [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0].count);
      });
    });

    if (liveStreams === 0) {
      const downtime = new Date() - this.lastStreamCheck;
      if (downtime > this.alertThreshold * 1000) {
        await this.sendAlert(`🚨 No live streams detected for ${Math.round(downtime/1000)} seconds`);
      }
    } else {
      this.lastStreamCheck = new Date();
    }
  }
}
```

## 🎯 Implementasi Bertahap

### Phase 1: Basic Redundancy (Week 1)
1. ✅ Setup auto-restart system
2. ✅ Implement YouTube server failover
3. ✅ Basic uptime monitoring

### Phase 2: Network Redundancy (Week 2)
1. Setup dual ISP connection
2. Configure network failover
3. Test failover scenarios

### Phase 3: Server Redundancy (Week 3)
1. Deploy backup server
2. Setup load balancer
3. Implement health monitoring

### Phase 4: Advanced Monitoring (Week 4)
1. Real-time alerting system
2. Performance metrics dashboard
3. Automated recovery procedures

## 📈 Expected Uptime Improvements

| Setup Level | Expected Uptime | Downtime/Month |
|-------------|----------------|----------------|
| Basic (Current) | 80-90% | 72-144 hours |
| + Auto-restart | 90-95% | 36-72 hours |
| + Network Redundancy | 95-98% | 14-36 hours |
| + Server Redundancy | 98-99.5% | 3.6-14 hours |
| + Full Monitoring | 99.5-99.9% | 0.7-3.6 hours |

## 💰 Cost Analysis

### Monthly Costs:
- **Backup ISP**: $30-50/month
- **Backup Server**: $20-40/month (VPS)
- **Monitoring Tools**: $10-20/month
- **Total**: $60-110/month

### ROI Calculation:
- **Revenue Loss per Hour**: Estimate your hourly streaming revenue
- **Uptime Improvement**: ~10-15% additional uptime
- **Break-even**: Usually achieved if hourly revenue > $2-4

## 🚀 Quick Start Commands

```bash
# 1. Start auto-restart system
node auto-restart-system.js

# 2. Monitor YouTube streams
node youtube-stream-monitor.js

# 3. Generate uptime report
node auto-restart-system.js report

# 4. Check network connectivity
ping -c 10 *******
ping -c 10 youtube.com

# 5. Monitor system resources
htop
df -h
free -h
```

## ⚠️ Important Notes

1. **True 24/7 is nearly impossible** - expect 99-99.5% uptime at best
2. **Cost vs Benefit** - evaluate if redundancy costs are justified
3. **Complexity** - more systems = more potential failure points
4. **Testing** - regularly test failover procedures
5. **Documentation** - maintain runbooks for emergency procedures
