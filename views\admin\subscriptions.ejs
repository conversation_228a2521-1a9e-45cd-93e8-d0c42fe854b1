<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white"><%= t('admin.subscription_management') %></h1>
      <p class="text-gray-400 mt-1"><%= t('admin.subscription_management_desc') %></p>
    </div>
    <div class="flex items-center space-x-4">
      <button onclick="refreshSubscriptions()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-refresh mr-2"></i>
        <%= t('admin.refresh') %>
      </button>
      <button onclick="showCreateSubscriptionModal()" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors">
        <i class="ti ti-plus mr-2"></i><%= t('admin.create_subscription') %>
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards - MOBILE RESPONSIVE -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6 mb-6 sm:mb-8">
  <div class="bg-dark-800 rounded-lg p-4 sm:p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-xs sm:text-sm">Total <%= t('admin.subscriptions') %></p>
        <p class="text-xl sm:text-2xl font-bold text-white"><%= totalSubscriptions %></p>
      </div>
      <div class="w-10 h-10 sm:w-12 sm:h-12 bg-primary/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-credit-card text-primary text-lg sm:text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.sub_active') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'active').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-check text-green-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.expired') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'expired').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-alert-triangle text-yellow-400 text-xl"></i>
      </div>
    </div>
  </div>

  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm"><%= t('admin.cancelled') %> <%= t('admin.subscriptions') %></p>
        <p class="text-2xl font-bold text-white">
          <%= subscriptions.filter(s => s.status === 'cancelled').length %>
        </p>
      </div>
      <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-x text-red-400 text-xl"></i>
      </div>
    </div>
  </div>

  <!-- NEW: Data Inconsistencies Card -->
  <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-gray-400 text-sm">Data Issues</p>
        <p class="text-2xl font-bold text-white" id="inconsistencyCount">
          <% if (typeof analytics !== 'undefined' && analytics.inconsistencyCount) { %>
            <%= analytics.inconsistencyCount %>
          <% } else { %>
            <span class="text-gray-500">-</span>
          <% } %>
        </p>
      </div>
      <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
        <i class="ti ti-alert-circle text-orange-400 text-xl"></i>
      </div>
    </div>
    <% if (typeof analytics !== 'undefined' && analytics.inconsistencyCount > 0) { %>
      <button onclick="showInconsistenciesModal()" class="mt-2 text-xs text-orange-400 hover:text-orange-300 transition-colors touch-target">
        View & Fix Issues
      </button>
    <% } %>
  </div>
</div>

<!-- NEW: Data Inconsistencies Alert -->
<% if (typeof analytics !== 'undefined' && analytics.inconsistencyCount > 0) { %>
<div class="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4 mb-6">
  <div class="flex items-start space-x-3">
    <div class="flex-shrink-0">
      <i class="ti ti-alert-triangle text-orange-400 text-xl"></i>
    </div>
    <div class="flex-1">
      <h4 class="text-orange-400 font-semibold">Subscription Data Inconsistencies Detected</h4>
      <p class="text-gray-300 text-sm mt-1">
        <%= analytics.inconsistencyCount %> users have mismatched subscription data between their user records and subscription records.
        This can cause streaming limit issues.
      </p>
      <div class="flex items-center space-x-4 mt-3">
        <button onclick="showInconsistenciesModal()" class="bg-orange-500 hover:bg-orange-600 text-white px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm transition-colors touch-target">
          <i class="ti ti-eye mr-1 sm:mr-2"></i><span class="hidden sm:inline">View </span>Issues
        </button>
        <button onclick="fixAllInconsistencies()" class="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm transition-colors touch-target">
          <i class="ti ti-tools mr-1 sm:mr-2"></i><span class="hidden sm:inline">Fix All </span>Issues
        </button>
      </div>
    </div>
  </div>
</div>
<% } %>

<!-- Subscriptions Table -->
<div class="bg-dark-800 rounded-lg border border-gray-700">
  <div class="p-4 sm:p-6 border-b border-gray-700">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.subscriptions') %></h3>
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
        <form method="GET" action="/admin/subscriptions" class="relative">
          <input type="text" name="search" id="searchSubscriptions" placeholder="<%= t('admin.search_subscriptions') %>"
                 value="<%= search || '' %>"
                 class="w-full sm:w-auto bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary text-sm">
          <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
          <% if (search) { %>
            <a href="/admin/subscriptions" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white touch-target">
              <i class="ti ti-x"></i>
            </a>
          <% } %>
        </form>
        <select id="statusFilter" onchange="filterSubscriptions()" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg text-sm">
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="expired">Expired</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>
    </div>
  </div>
  <!-- Mobile Card View (hidden on desktop) -->
  <div class="block sm:hidden">
    <% if (subscriptions && subscriptions.length > 0) { %>
      <% subscriptions.forEach(subscription => { %>
        <div class="p-4 border-b border-gray-700 last:border-b-0">
          <div class="flex items-start justify-between mb-2">
            <div>
              <h4 class="font-medium text-white"><%= subscription.username %></h4>
              <p class="text-sm text-gray-400"><%= subscription.email %></p>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              <% if (subscription.status === 'active') { %>bg-green-100 text-green-800<% } %>
              <% if (subscription.status === 'expired') { %>bg-yellow-100 text-yellow-800<% } %>
              <% if (subscription.status === 'cancelled') { %>bg-red-100 text-red-800<% } %>">
              <%= subscription.status %>
            </span>
          </div>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span class="text-gray-400">Plan:</span>
              <span class="text-white ml-1"><%= subscription.plan_name %></span>
            </div>
            <div>
              <span class="text-gray-400">Price:</span>
              <span class="text-white ml-1">Rp <%= subscription.plan_price?.toLocaleString() || 0 %></span>
            </div>
            <div>
              <span class="text-gray-400">Start:</span>
              <span class="text-white ml-1"><%= new Date(subscription.start_date).toLocaleDateString() %></span>
            </div>
            <div>
              <span class="text-gray-400">End:</span>
              <span class="text-white ml-1">
                <% if (subscription.end_date) { %>
                  <%= new Date(subscription.end_date).toLocaleDateString() %>
                <% } else { %>
                  Unlimited
                <% } %>
              </span>
            </div>
          </div>
          <div class="mt-3 flex space-x-2">
            <button onclick="viewUserHistory('<%= subscription.user_id %>')"
                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-xs transition-colors touch-target">
              <i class="ti ti-history mr-1"></i>History
            </button>
            <button onclick="editSubscription('<%= subscription.id %>')"
                    class="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-xs transition-colors touch-target">
              <i class="ti ti-edit mr-1"></i>Edit
            </button>
          </div>
        </div>
      <% }); %>
    <% } else { %>
      <div class="p-8 text-center text-gray-400">
        <i class="ti ti-inbox text-4xl mb-2"></i>
        <p>No subscriptions found</p>
      </div>
    <% } %>
  </div>

  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden sm:block overflow-x-auto">
    <table class="min-w-full">
      <thead class="bg-gray-700">
        <tr>
          <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.user') %></th>
          <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.plan') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.subscription_status') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.start_date') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.end_date') %></th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.payment_method') %></th>
          <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider"><%= t('admin.actions') %></th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-700">
        <% subscriptions.forEach(function(subscription) { %>
          <tr class="hover:bg-dark-700/50 transition-colors subscription-row"
              data-status="<%= subscription.status %>"
              data-search="<%= subscription.username.toLowerCase() %> <%= (subscription.email || '').toLowerCase() %> <%= subscription.plan_name.toLowerCase() %>">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <i class="ti ti-user text-white"></i>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-white"><%= subscription.username %></div>
                  <div class="text-sm text-gray-400"><%= subscription.email %></div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"><%= subscription.plan_name %></span>
              <% if (subscription.plan_price > 0) { %>
                <div class="text-sm text-gray-400 mt-1">
                  <%= subscription.plan_currency === 'IDR' ? 'Rp' : '$' %><%= subscription.plan_price.toLocaleString() %>
                </div>
              <% } %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <%
                let statusClass = 'bg-gray-100 text-gray-800';
                if (subscription.status === 'active') statusClass = 'bg-green-100 text-green-800';
                else if (subscription.status === 'expired') statusClass = 'bg-yellow-100 text-yellow-800';
                else if (subscription.status === 'cancelled') statusClass = 'bg-red-100 text-red-800';
              %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= statusClass %>">
                <%= t('admin.' + subscription.status) %>
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
              <%= new Date(subscription.start_date).toLocaleDateString() %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
              <%= new Date(subscription.end_date).toLocaleDateString() %>
              <% if (new Date(subscription.end_date) < new Date() && subscription.status === 'active') { %>
                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">Expired</span>
              <% } %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><%= subscription.payment_method %></span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <button onclick="viewUserHistory('<%= subscription.user_id %>', '<%= subscription.username %>')" class="text-blue-400 hover:text-blue-300" title="<%= t('admin.view_history') %>">
                  <i class="ti ti-history"></i>
                </button>
                <% if (subscription.status === 'active') { %>
                  <button onclick="showExtendModal('<%= subscription.id %>', '<%= subscription.username %>', '<%= subscription.end_date %>')" class="text-green-400 hover:text-green-300" title="<%= t('admin.extend') %>">
                    <i class="ti ti-calendar-plus"></i>
                  </button>
                  <button onclick="cancelSubscription('<%= subscription.id %>', '<%= subscription.username %>')" class="text-red-400 hover:text-red-300" title="<%= t('admin.cancel') %>">
                    <i class="ti ti-x"></i>
                  </button>
                <% } %>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>

    <!-- No results message -->
    <% if (subscriptions.length === 0) { %>
      <div class="p-8 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
          <i class="ti ti-credit-card text-2xl text-gray-400"></i>
        </div>
        <% if (search) { %>
          <h3 class="text-lg font-medium text-white mb-2">No subscriptions found</h3>
          <p class="text-gray-400 mb-4">No subscriptions match your search criteria "<%= search %>"</p>
          <a href="/admin/subscriptions" class="inline-flex items-center gap-2 text-primary hover:text-primary-light">
            <i class="ti ti-arrow-left"></i>
            Clear search and show all subscriptions
          </a>
        <% } else { %>
          <h3 class="text-lg font-medium text-white mb-2">No subscriptions found</h3>
          <p class="text-gray-400">No subscriptions have been created yet.</p>
        <% } %>
      </div>
    <% } %>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="p-6 border-t border-gray-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          <% if (search) { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalSubscriptions) %> of <%= totalSubscriptions %> subscriptions
            <span class="text-primary">matching "<%= search %>"</span>
          <% } else { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalSubscriptions) %> of <%= totalSubscriptions %> subscriptions
          <% } %>
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Button -->
          <% if (hasPrevPage) { %>
            <a href="/admin/subscriptions?page=<%= prevPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            if (endPage - startPage < 4 && startPage > 1) {
              startPage = Math.max(1, endPage - 4);
            }
          %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === currentPage) { %>
              <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-primary text-white font-medium">
                <%= i %>
              </span>
            <% } else { %>
              <a href="/admin/subscriptions?page=<%= i %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
                 class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <!-- Next Button -->
          <% if (hasNextPage) { %>
            <a href="/admin/subscriptions?page=<%= nextPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Create Subscription Modal -->
<div id="createSubscriptionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-md mx-4">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.create_subscription') %></h3>
      <button type="button" onclick="hideCreateSubscriptionModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <form id="createSubscriptionForm">
      <div class="p-6 space-y-4">
        <div>
          <label for="createUserId" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.user') %></label>
          <input type="text" id="createUserId" placeholder="Enter User ID or Username" required
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">You can enter either the user ID (UUID) or username</p>
        </div>

        <div>
          <label for="createPlanId" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.plan') %></label>
          <select id="createPlanId" required
                  class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="">Select Plan</option>
            <% plans.forEach(function(plan) { %>
              <option value="<%= plan.id %>"><%= plan.name %> - <%= plan.currency === 'IDR' ? 'Rp' : '$' %><%= plan.price.toLocaleString() %></option>
            <% }); %>
          </select>
        </div>

        <div>
          <label for="createStartDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.start_date') %></label>
          <input type="datetime-local" id="createStartDate"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Leave empty for current date/time</p>
        </div>

        <div>
          <label for="createEndDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.end_date') %></label>
          <input type="datetime-local" id="createEndDate" required
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
        </div>

        <div>
          <label for="createPaymentMethod" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.payment_method') %></label>
          <select id="createPaymentMethod"
                  class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
            <option value="admin_manual">Admin Manual</option>
            <option value="midtrans">Midtrans</option>
            <option value="free">Free</option>
          </select>
        </div>
      </div>

      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
        <button type="button" onclick="hideCreateSubscriptionModal()"
                class="px-4 py-2 text-gray-400 hover:text-white transition-colors">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
          Create Subscription
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Extend Subscription Modal -->
<div id="extendSubscriptionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-md mx-4">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.extend_subscription') %></h3>
      <button type="button" onclick="hideExtendSubscriptionModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <form id="extendSubscriptionForm">
      <div class="p-6 space-y-4">
        <input type="hidden" id="extendSubscriptionId">

        <div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
          <div class="text-sm text-blue-300">
            <div><strong>User:</strong> <span id="extendUsername" class="text-white"></span></div>
            <div><strong>Current End Date:</strong> <span id="extendCurrentEndDate" class="text-white"></span></div>
          </div>
        </div>

        <div>
          <label for="extendDays" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.extension_days') %></label>
          <input type="number" id="extendDays" min="1" max="365" placeholder="30"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Number of days to extend from current end date</p>
        </div>

        <div class="text-center py-2">
          <span class="text-gray-400 font-medium">OR</span>
        </div>

        <div>
          <label for="extendNewEndDate" class="block text-sm font-medium text-gray-300 mb-2"><%= t('admin.custom_end_date') %></label>
          <input type="datetime-local" id="extendNewEndDate"
                 class="w-full px-3 py-2 bg-dark-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
          <p class="text-xs text-gray-400 mt-1">Set specific end date (overrides extension days)</p>
        </div>
      </div>

      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
        <button type="button" onclick="hideExtendSubscriptionModal()"
                class="px-4 py-2 text-gray-400 hover:text-white transition-colors">
          Cancel
        </button>
        <button type="submit"
                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
          Extend Subscription
        </button>
      </div>
    </form>
  </div>
</div>

<!-- User History Modal -->
<div id="userHistoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
  <div class="bg-dark-800 rounded-lg border border-gray-700 w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
    <div class="flex items-center justify-between p-6 border-b border-gray-700">
      <h3 class="text-lg font-semibold text-white"><%= t('admin.subscription_history') %></h3>
      <button type="button" onclick="hideUserHistoryModal()" class="text-gray-400 hover:text-white">
        <i class="ti ti-x text-xl"></i>
      </button>
    </div>

    <div class="p-6 flex-1 overflow-y-auto">
      <div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-6">
        <div class="text-sm text-blue-300">
          <strong>User:</strong> <span id="historyUsername" class="text-white"></span>
        </div>
      </div>

      <div id="historyContent">
        <div class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p class="text-gray-400 mt-2">Loading...</p>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-end p-6 border-t border-gray-700">
      <button type="button" onclick="hideUserHistoryModal()"
              class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
        Close
      </button>
    </div>
  </div>
</div>

<script>
function refreshSubscriptions() {
  window.location.reload();
}

// Auto-submit search form with debounce
let searchTimeout;
document.getElementById('searchSubscriptions').addEventListener('input', function(e) {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    e.target.form.submit();
  }, 500); // 500ms debounce
});

// Filter subscriptions by status
function filterSubscriptions() {
  const statusFilter = document.getElementById('statusFilter').value;
  const rows = document.querySelectorAll('.subscription-row');

  rows.forEach(row => {
    const status = row.getAttribute('data-status');
    if (!statusFilter || status === statusFilter) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

// Show create subscription modal
function showCreateSubscriptionModal() {
  // Set default end date to 30 days from now
  const defaultEndDate = new Date();
  defaultEndDate.setDate(defaultEndDate.getDate() + 30);
  document.getElementById('createEndDate').value = defaultEndDate.toISOString().slice(0, 16);

  document.getElementById('createSubscriptionModal').classList.remove('hidden');
}

// Hide create subscription modal
function hideCreateSubscriptionModal() {
  document.getElementById('createSubscriptionModal').classList.add('hidden');
}

// Handle create subscription form
console.log('Setting up create subscription form event listener...');
const createForm = document.getElementById('createSubscriptionForm');
console.log('Create form element:', createForm);
if (createForm) {
  createForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    console.log('Create subscription form submitted');
    const formData = {
    userId: document.getElementById('createUserId').value,
    planId: document.getElementById('createPlanId').value,
    startDate: document.getElementById('createStartDate').value || null,
    endDate: document.getElementById('createEndDate').value,
    paymentMethod: document.getElementById('createPaymentMethod').value
  };

  console.log('Form data:', formData);
  try {
    console.log('Sending request to create subscription...');
    const response = await fetch('/admin/subscriptions/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(formData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    const result = await response.json();
    console.log('Response result:', result);
    if (result.success) {
      alert('<%= t("admin.subscription_created") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    console.error('Error creating subscription:', error);
    alert('Error: ' + error.message);
  }
  });
} else {
  console.error('Create subscription form not found!');
}

// Show extend subscription modal
function showExtendModal(subscriptionId, username, currentEndDate) {
  document.getElementById('extendSubscriptionId').value = subscriptionId;
  document.getElementById('extendUsername').textContent = username;
  document.getElementById('extendCurrentEndDate').textContent = new Date(currentEndDate).toLocaleString();

  // Clear form
  document.getElementById('extendDays').value = '';
  document.getElementById('extendNewEndDate').value = '';

  document.getElementById('extendSubscriptionModal').classList.remove('hidden');
}

// Hide extend subscription modal
function hideExtendSubscriptionModal() {
  document.getElementById('extendSubscriptionModal').classList.add('hidden');
}

// Handle extend subscription form
document.getElementById('extendSubscriptionForm').addEventListener('submit', async function(e) {
  e.preventDefault();

  const subscriptionId = document.getElementById('extendSubscriptionId').value;
  const extensionDays = document.getElementById('extendDays').value;
  const newEndDate = document.getElementById('extendNewEndDate').value;

  if (!extensionDays && !newEndDate) {
    alert('Please specify extension days or new end date');
    return;
  }

  const formData = {
    extensionDays: extensionDays || null,
    newEndDate: newEndDate || null
  };

  try {
    const response = await fetch(`/admin/subscriptions/${subscriptionId}/extend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      },
      body: JSON.stringify(formData)
    });

    const result = await response.json();

    if (result.success) {
      alert('<%= t("admin.subscription_extended") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Error: ' + error.message);
  }
});

// Cancel subscription
async function cancelSubscription(subscriptionId, username) {
  if (!confirm(`Are you sure you want to cancel subscription for ${username}?`)) {
    return;
  }

  try {
    const response = await fetch(`/admin/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': '<%= csrfToken %>'
      }
    });

    const result = await response.json();

    if (result.success) {
      alert('<%= t("admin.subscription_cancelled") %>');
      location.reload();
    } else {
      alert('Error: ' + result.error);
    }
  } catch (error) {
    alert('Error: ' + error.message);
  }
}

// View user subscription history
async function viewUserHistory(userId, username) {
  document.getElementById('historyUsername').textContent = username;
  document.getElementById('historyContent').innerHTML = `
    <div class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p class="text-gray-400 mt-2">Loading...</p>
    </div>
  `;

  document.getElementById('userHistoryModal').classList.remove('hidden');

  try {
    const response = await fetch(`/admin/users/${userId}/subscriptions`);
    const result = await response.json();

    if (result.success) {
      let historyHtml = '';

      if (result.subscriptions.length === 0) {
        historyHtml = '<div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 text-blue-300">No subscription history found.</div>';
      } else {
        historyHtml = `
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Plan</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Start Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">End Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Payment Method</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-700">
        `;

        result.subscriptions.forEach(sub => {
          let statusClass = 'bg-gray-100 text-gray-800';
          if (sub.status === 'active') statusClass = 'bg-green-100 text-green-800';
          else if (sub.status === 'expired') statusClass = 'bg-yellow-100 text-yellow-800';
          else if (sub.status === 'cancelled') statusClass = 'bg-red-100 text-red-800';

          historyHtml += `
            <tr class="hover:bg-dark-700/50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">${sub.plan_name}</span>
                ${sub.plan_price > 0 ? `<div class="text-sm text-gray-400 mt-1">${sub.plan_currency === 'IDR' ? 'Rp' : '$'}${sub.plan_price.toLocaleString()}</div>` : ''}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">${sub.status}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${new Date(sub.start_date).toLocaleDateString()}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${new Date(sub.end_date).toLocaleDateString()}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">${sub.payment_method}</span>
              </td>
            </tr>
          `;
        });

        historyHtml += '</tbody></table></div>';
      }

      document.getElementById('historyContent').innerHTML = historyHtml;
    } else {
      document.getElementById('historyContent').innerHTML = `
        <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-red-300">Error: ${result.error}</div>
      `;
    }
  } catch (error) {
    document.getElementById('historyContent').innerHTML = `
      <div class="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-red-300">Error: ${error.message}</div>
    `;
  }
}

// Hide user history modal
function hideUserHistoryModal() {
  document.getElementById('userHistoryModal').classList.add('hidden');
}

// CENTRALIZED SUBSCRIPTION MANAGEMENT: Inconsistency management functions

// Show inconsistencies modal
async function showInconsistenciesModal() {
  try {
    const response = await fetch('/admin/api/subscription-inconsistencies');
    const result = await response.json();

    if (result.success) {
      let modalHtml = `
        <div id="inconsistenciesModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div class="modal-content bg-dark-800 rounded-lg p-4 sm:p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto border border-gray-700">
            <div class="flex items-center justify-between mb-4 sm:mb-6">
              <h3 class="text-lg sm:text-xl font-semibold text-white">Subscription Data Inconsistencies (${result.inconsistencies.length})</h3>
              <button onclick="hideInconsistenciesModal()" class="text-gray-400 hover:text-white touch-target">
                <i class="ti ti-x text-xl"></i>
              </button>
            </div>

            <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
              <p class="text-gray-300 text-sm">
                These users have mismatched data between their user records and subscription records.
              </p>
              <button onclick="fixAllInconsistencies()" class="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 py-2 rounded-lg text-sm transition-colors touch-target">
                <i class="ti ti-tools mr-1 sm:mr-2"></i>Fix All Issues
              </button>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full">
                <thead class="bg-gray-700">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">User</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Issue Type</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">User Record</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Subscription Record</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
      `;

      result.inconsistencies.forEach(item => {
        const issueTypeColors = {
          'no_subscription': 'bg-red-100 text-red-800',
          'plan_mismatch': 'bg-yellow-100 text-yellow-800',
          'slots_mismatch': 'bg-orange-100 text-orange-800',
          'storage_mismatch': 'bg-blue-100 text-blue-800'
        };

        modalHtml += `
          <tr class="hover:bg-dark-700/50 transition-colors">
            <td class="px-4 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-white">${item.username}</div>
            </td>
            <td class="px-4 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${issueTypeColors[item.inconsistency_type] || 'bg-gray-100 text-gray-800'}">
                ${item.inconsistency_type.replace('_', ' ').toUpperCase()}
              </span>
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-300">
              <div>Plan: ${item.user_plan}</div>
              <div>Slots: ${item.user_slots}</div>
              <div>Storage: ${item.user_storage}GB</div>
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-300">
              <div>Plan: ${item.subscription_plan || 'None'}</div>
              <div>Slots: ${item.subscription_slots || 'N/A'}</div>
              <div>Storage: ${item.subscription_storage || 'N/A'}GB</div>
              <div>Status: ${item.subscription_status || 'N/A'}</div>
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm">
              <button onclick="fixUserInconsistency('${item.user_id}', '${item.username}')"
                      class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors">
                <i class="ti ti-tools mr-1"></i>Fix
              </button>
            </td>
          </tr>
        `;
      });

      modalHtml += `
                </tbody>
              </table>
            </div>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', modalHtml);
    } else {
      showNotification('Error loading inconsistencies: ' + result.error, 'error');
    }
  } catch (error) {
    showNotification('Error: ' + error.message, 'error');
  }
}

// Hide inconsistencies modal
function hideInconsistenciesModal() {
  const modal = document.getElementById('inconsistenciesModal');
  if (modal) {
    modal.remove();
  }
}

// Fix single user inconsistency
async function fixUserInconsistency(userId, username) {
  if (!confirm(`Fix subscription inconsistency for user "${username}"?`)) {
    return;
  }

  try {
    const response = await fetch(`/admin/api/subscription-inconsistencies/${userId}/fix`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ action: 'sync_to_subscription' })
    });

    const result = await response.json();

    if (result.success) {
      showNotification(`Successfully fixed inconsistency for ${username}`, 'success');
      hideInconsistenciesModal();
      // Refresh the page to update counts
      setTimeout(() => window.location.reload(), 1000);
    } else {
      showNotification('Error: ' + result.error, 'error');
    }
  } catch (error) {
    showNotification('Error: ' + error.message, 'error');
  }
}

// Fix all inconsistencies - ENHANCED WITH BETTER ERROR HANDLING AND USER FEEDBACK
async function fixAllInconsistencies() {
  if (!confirm('Fix ALL subscription inconsistencies? This will update user records to match their subscription data.')) {
    return;
  }

  // Disable the button and show loading state
  const fixButton = document.querySelector('button[onclick="fixAllInconsistencies()"]');
  const originalText = fixButton ? fixButton.innerHTML : '';

  if (fixButton) {
    fixButton.disabled = true;
    fixButton.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Processing...';
    fixButton.classList.add('opacity-50', 'cursor-not-allowed');
  }

  try {
    showNotification('Processing all inconsistencies...', 'info');

    const response = await fetch('/admin/api/subscription-inconsistencies/fix-all', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Check if response is ok
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success) {
      showNotification(`✅ Successfully processed ${result.totalProcessed} users`, 'success');
      hideInconsistenciesModal();

      // Update the inconsistency count in the dashboard
      const inconsistencyCountElement = document.getElementById('inconsistencyCount');
      if (inconsistencyCountElement) {
        inconsistencyCountElement.textContent = '0';
      }

      // Refresh the page to update counts after a delay
      setTimeout(() => window.location.reload(), 2000);
    } else {
      showNotification('❌ Error: ' + (result.error || 'Unknown error occurred'), 'error');
    }
  } catch (error) {
    console.error('Fix all inconsistencies error:', error);
    showNotification('❌ Network Error: ' + error.message, 'error');
  } finally {
    // Re-enable the button
    if (fixButton) {
      fixButton.disabled = false;
      fixButton.innerHTML = originalText;
      fixButton.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  }
}
</script>

<!-- MOBILE RESPONSIVENESS: Custom CSS for touch targets and responsive design -->
<style>
/* Touch-friendly button targets for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Ensure modals are properly sized on mobile */
  .modal-content {
    max-height: 90vh;
    overflow-y: auto;
    margin: 1rem;
  }

  /* Improve table readability on small screens */
  .table-responsive {
    font-size: 0.875rem;
  }

  /* Stack form elements on mobile */
  .mobile-stack > * {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  /* Improve button spacing on mobile */
  .mobile-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .mobile-buttons > button {
    width: 100%;
    justify-content: center;
  }
}

/* Responsive modal sizing */
@media (min-width: 640px) {
  .modal-content {
    max-width: 90vw;
  }
}

@media (min-width: 1024px) {
  .modal-content {
    max-width: 80vw;
  }
}

@media (min-width: 1280px) {
  .modal-content {
    max-width: 70vw;
  }
}

/* Loading spinner animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Improved focus states for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Better hover states on touch devices */
@media (hover: hover) {
  .hover\:bg-opacity-80:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

/* Ensure text is readable on all screen sizes */
@media (max-width: 480px) {
  .text-responsive {
    font-size: 0.75rem;
  }

  .text-responsive-lg {
    font-size: 0.875rem;
  }
}
</style>
