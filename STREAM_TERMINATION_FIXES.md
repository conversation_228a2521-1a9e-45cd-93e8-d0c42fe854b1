# StreamOnPod - Stream Termination Fixes

## Issues Identified and Fixed

### 🚨 Issue 1: Automatic Stream Termination After ~1 Hour

**Root Cause**: The periodic health check function in `streamingService.js` was running every hour (`CLEANUP_INTERVAL = 60 * 60 * 1000`) and aggressively killing streams that appeared to be "orphaned" due to temporary database inconsistencies.

**Fixes Applied**:

1. **Increased Cleanup Interval**: Changed from 1 hour to 6 hours to reduce false terminations
   - Location: `services/streamingService.js` line 45
   - Old: `const CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour`
   - New: `const CLEANUP_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours`

2. **Enhanced Orphaned Process Detection**: Added additional validation before killing processes
   - Location: `services/streamingService.js` lines 654-687
   - Now checks process health and age before termination
   - Skips processes less than 30 seconds old to avoid killing during startup

3. **Added Critical Logging**: Enhanced logging to track when streams are terminated
   - Helps identify the exact cause of future terminations
   - Logs stream age in minutes when termination occurs

### 🎯 Issue 2: Thumbnail Timeframe Mismatch

**Root Cause**: The `start_time` was being set immediately when the stream status changed to 'live', but before the stream was actually outputting, causing duration display mismatches.

**Fix Applied**:

1. **Added Timing Delay**: Introduced a 2-second delay before setting start_time
   - Location: `services/streamingService.js` lines 1144-1145
   - Ensures stream is actually outputting before recording start time
   - Improves accuracy of duration display in thumbnails

## Diagnostic Tools Created

### 1. Stream Termination Monitor (`diagnose-stream-termination.js`)

**Purpose**: Real-time monitoring of stream terminations to identify the 1-hour bug

**Usage**:
```bash
node diagnose-stream-termination.js
```

**Features**:
- Monitors all live streams every 30 seconds
- Detects terminations and logs exact runtime
- Alerts when streams terminate around the 1-hour mark (55-65 minutes)
- Generates detailed termination reports
- Logs to `logs/stream-termination-diagnostic.log`

### 2. Stream Duration Fix Script (`fix-stream-durations.js`)

**Purpose**: Identifies and fixes streams with duration limits that cause automatic termination

**Usage**:
```bash
# Check for streams with duration limits
node fix-stream-durations.js

# Automatically remove all duration limits
node fix-stream-durations.js --fix
```

**Features**:
- Finds streams with duration limits
- Identifies suspicious ~60 minute limits
- Can automatically remove duration limits for 24/7 streaming

## Testing and Verification

### Immediate Actions Required:

1. **Check for Duration Limits**:
   ```bash
   node fix-stream-durations.js
   ```
   If any streams have ~60 minute duration limits, remove them:
   ```bash
   node fix-stream-durations.js --fix
   ```

2. **Start Monitoring**:
   ```bash
   node diagnose-stream-termination.js
   ```
   Let this run for several hours to monitor for terminations.

3. **Test Stream Longevity**:
   - Start a test stream
   - Monitor it for at least 2-3 hours
   - Check that it doesn't terminate at the 1-hour mark

### Expected Results:

- ✅ Streams should now run continuously beyond 1 hour
- ✅ Thumbnail duration display should be more accurate
- ✅ Reduced false terminations from health checks
- ✅ Better logging for debugging future issues

## Additional Recommendations

### 1. Monitor System Resources
- Keep an eye on memory usage during long streams
- The 6-hour cleanup interval may require monitoring

### 2. Database Consistency
- Consider adding database connection pooling if not already present
- Monitor for database lock issues that could cause temporary inconsistencies

### 3. Stream Health Monitoring
- The current health checks are now more conservative
- Consider implementing more sophisticated stream output monitoring

## Rollback Plan

If issues arise, you can quickly rollback by:

1. **Revert Cleanup Interval**:
   ```javascript
   const CLEANUP_INTERVAL = 60 * 60 * 1000; // Back to 1 hour
   ```

2. **Remove Timing Delay**:
   Remove the 2-second delay in the status update function

3. **Restore Original Health Check Logic**:
   Remove the additional validation in the orphaned process detection

## Files Modified

1. `services/streamingService.js` - Main fixes for termination and timing
2. `diagnose-stream-termination.js` - New diagnostic tool
3. `fix-stream-durations.js` - New duration fix script
4. `STREAM_TERMINATION_FIXES.md` - This documentation

## Next Steps

1. Deploy the fixes to your StreamOnPod instance
2. Run the diagnostic tools to verify the fixes
3. Monitor streams for 24+ hours to ensure 24/7 operation
4. Check thumbnail duration accuracy in the web interface

The fixes address both the automatic termination issue and the thumbnail timing problem while providing tools to monitor and prevent future occurrences.
