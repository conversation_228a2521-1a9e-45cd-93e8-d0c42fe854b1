const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

// Storage update locking mechanism to prevent race conditions
const storageLocks = new Map();

class Subscription {
  // Get all subscription plans
  static getAllPlans() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Get all subscription plans with subscriber counts
  static getAllPlansWithSubscribers() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT
          sp.*,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        WHERE sp.is_active = 1
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]'),
          subscriber_count: row.subscriber_count || 0
        })));
      });
    });
  }

  // Get plan by ID
  static getPlanById(planId) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE id = ?', [planId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get user's current subscription (excluding Preview plan as it's the default)
  static getUserSubscription(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND us.status = 'active' AND sp.name != 'Preview'
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');

          // Check if subscription has expired or was cancelled
          const isExpired = row.end_date && new Date(row.end_date) < new Date();
          const isCancelled = row.status === 'cancelled';

          if (isExpired || isCancelled) {
            console.log(`⚠️ Subscription ${isCancelled ? 'cancelled' : 'expired'} for user ${userId}, plan: ${row.plan_name}`);
            // Mark subscription as expired if not already
            if (row.status !== 'expired') {
              this.updateSubscriptionStatus(row.id, 'expired').catch(console.error);
            }
            resolve(null); // Return null for expired/cancelled subscription
            return;
          }
        }
        resolve(row);
      });
    });
  }

  // Get user's subscription including expired ones (for display purposes)
  static getUserSubscriptionIncludingExpired(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features, sp.price
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ? AND sp.name != 'Preview'
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');

          // Add expiration status - check both end_date and cancelled status
          const isExpiredByDate = row.end_date && new Date(row.end_date) < new Date();
          const isCancelled = row.status === 'cancelled';

          row.isExpired = isExpiredByDate || isCancelled;
          row.isCancelled = isCancelled; // Add specific cancelled flag for display purposes
        }
        resolve(row);
      });
    });
  }

  // Create new subscription
  static createSubscription(subscriptionData) {
    const id = uuidv4();
    const {
      user_id,
      plan_id,
      status = 'active',
      start_date = new Date().toISOString(),
      end_date,
      payment_method,
      payment_id
    } = subscriptionData;

    return new Promise(async (resolve, reject) => {
      try {
        // Get plan details to check if it's Preview plan
        const plan = await this.getPlanById(plan_id);

        let finalEndDate = end_date;

        // Ensure only Preview plan can have null end_date
        if (plan && plan.name !== 'Preview' && !finalEndDate) {
          // For non-Preview plans, set default 30-day duration if no end_date provided
          const defaultEndDate = new Date();
          defaultEndDate.setDate(defaultEndDate.getDate() + 30);
          finalEndDate = defaultEndDate.toISOString();

          console.log(`⚠️ Non-Preview plan "${plan.name}" created without end_date, setting default 30 days`);
        }

        db.run(
          `INSERT INTO user_subscriptions
           (id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [id, user_id, plan_id, status, start_date, finalEndDate, payment_method, payment_id],
          function (err) {
            if (err) {
              return reject(err);
            }
            resolve({ id, ...subscriptionData, end_date: finalEndDate });
          }
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  // Update subscription status
  static updateSubscriptionStatus(subscriptionId, status) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, status });
        }
      );
    });
  }

  // Handle expired subscriptions - downgrade users to Preview plan and delete history
  static async handleExpiredSubscription(userId) {
    try {
      const User = require('./User');

      // Get Preview plan details
      const previewPlan = await this.getPlanByName('Preview');
      if (!previewPlan) {
        console.error('❌ Preview plan not found for expired subscription handling');
        return false;
      }

      // Delete all expired subscriptions for this user (clean history)
      await this.deleteExpiredSubscriptions(userId);

      // Delete all user streams since Preview plan has 0 slots
      await this.deleteAllUserStreamsForPreviewPlan(userId);

      // Downgrade user to Preview plan
      await User.updatePlan(
        userId,
        'Preview',
        previewPlan.max_streaming_slots,
        previewPlan.max_storage_gb
      );

      // Create new Preview subscription (unlimited)
      await this.createSubscription({
        user_id: userId,
        plan_id: previewPlan.id,
        status: 'active',
        end_date: null, // Preview plan has no expiration
        payment_method: 'free'
      });

      console.log(`📉 User ${userId} downgraded to Preview plan and history cleaned`);
      return true;
    } catch (error) {
      console.error('❌ Error handling expired subscription:', error);
      return false;
    }
  }

  // Handle admin cancellation - downgrade user but keep subscription record for display
  static async handleAdminCancellation(userId) {
    try {
      const User = require('./User');

      // Get Preview plan details
      const previewPlan = await this.getPlanByName('Preview');
      if (!previewPlan) {
        console.error('❌ Preview plan not found for admin cancellation handling');
        return false;
      }

      // Delete all user streams since Preview plan has 0 slots
      await this.deleteAllUserStreamsForPreviewPlan(userId);

      // Downgrade user to Preview plan
      await User.updatePlan(
        userId,
        'Preview',
        previewPlan.max_streaming_slots,
        previewPlan.max_storage_gb
      );

      // Create new Preview subscription (unlimited)
      await this.createSubscription({
        user_id: userId,
        plan_id: previewPlan.id,
        status: 'active',
        end_date: null, // Preview plan has no expiration
        payment_method: 'free'
      });

      console.log(`📉 User ${userId} downgraded to Preview plan (admin cancellation - keeping history)`);
      return true;
    } catch (error) {
      console.error('❌ Error handling admin cancellation:', error);
      return false;
    }
  }

  // Delete expired subscriptions for a user (clean history)
  static async deleteExpiredSubscriptions(userId) {
    return new Promise((resolve, reject) => {
      db.run(`
        DELETE FROM user_subscriptions
        WHERE user_id = ?
        AND (
          status = 'expired'
          OR status = 'cancelled'
          OR (end_date IS NOT NULL AND end_date < datetime('now'))
        )
      `, [userId], function(err) {
        if (err) {
          console.error('❌ Error deleting expired/cancelled subscriptions:', err);
          return reject(err);
        }
        console.log(`🗑️ Deleted ${this.changes} expired/cancelled subscriptions for user ${userId}`);
        resolve({ deleted: this.changes });
      });
    });
  }

  // Delete all user streams when downgrading to Preview plan (0 slots)
  static async deleteAllUserStreamsForPreviewPlan(userId) {
    try {
      const Stream = require('./Stream');

      // Stop any active streams first
      const streamingService = require('../services/streamingService');
      const activeStreamIds = streamingService.getActiveStreams();

      // Get user's streams
      const userStreams = await new Promise((resolve, reject) => {
        db.all('SELECT id FROM streams WHERE user_id = ?', [userId], (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => row.id));
        });
      });

      // Stop any active streams belonging to this user
      for (const streamId of userStreams) {
        if (activeStreamIds.includes(streamId)) {
          console.log(`🛑 Stopping active stream ${streamId} due to subscription expiry`);
          try {
            await streamingService.stopStream(streamId);
            // Add delay to ensure stream is properly stopped
            await new Promise(resolve => setTimeout(resolve, 2000));
          } catch (error) {
            console.error(`❌ Error stopping stream ${streamId}:`, error.message);
          }
        }
      }

      // Delete all streams for the user
      const result = await Stream.deleteAllUserStreams(userId);

      if (result.deleted > 0) {
        console.log(`🗑️ Deleted ${result.deleted} streams for user ${userId} due to subscription expiry`);
      }

      return result;
    } catch (error) {
      console.error('❌ Error deleting user streams for Preview plan:', error);
      return { success: false, deleted: 0, streams: [] };
    }
  }

  // Check and handle all expired subscriptions (background job)
  static async checkAndHandleExpiredSubscriptions() {
    try {
      console.log('🔍 Checking for expired subscriptions...');
      const expiredSubscriptions = await new Promise((resolve, reject) => {
        db.all(`
          SELECT us.*, u.username, sp.name as plan_name
          FROM user_subscriptions us
          JOIN users u ON us.user_id = u.id
          JOIN subscription_plans sp ON us.plan_id = sp.id
          WHERE (
            (us.status = 'active' AND us.end_date < datetime('now'))
            OR us.status = 'cancelled'
          )
          AND sp.name != 'Preview'
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (expiredSubscriptions.length === 0) {
        console.log('✅ No expired subscriptions found');
        return { processed: 0, expired: [] };
      }

      console.log(`⚠️ Found ${expiredSubscriptions.length} expired subscriptions`);
      const processedUsers = [];
      for (const subscription of expiredSubscriptions) {
        try {
          console.log(`📉 Processing expired subscription for user ${subscription.username} (${subscription.plan_name})`);

          // Handle expired subscription (this will clean history, stop streams, and downgrade to Preview)
          const success = await this.handleExpiredSubscription(subscription.user_id);

          if (success) {
            processedUsers.push({
              userId: subscription.user_id,
              username: subscription.username,
              planName: subscription.plan_name,
              expiredDate: subscription.end_date,
              action: 'downgraded_to_preview'
            });
          }

        } catch (error) {
          console.error(`❌ Error processing expired subscription for user ${subscription.user_id}:`, error);
        }
      }

      console.log(`✅ Processed ${processedUsers.length} expired subscriptions`);
      return { processed: processedUsers.length, expired: processedUsers };

    } catch (error) {
      console.error('❌ Error checking expired subscriptions:', error);
      return { processed: 0, expired: [], error: error.message };
    }
  }

  // Force stop all active streams for users with insufficient slots
  static async enforceSlotLimits() {
    try {
      console.log('🔍 Enforcing slot limits for all users...');
      const User = require('./User');
      const Stream = require('./Stream');
      const streamingService = require('../services/streamingService');
      const stoppedStreams = [];
  
      // Get all users who have at least one stream
      const usersWithStreams = await new Promise((resolve, reject) => {
        db.all('SELECT DISTINCT user_id FROM streams', [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(r => r.user_id));
        });
      });
  
      if (usersWithStreams.length === 0) {
        console.log('✅ No users with streams to check');
        return { stopped: 0, streams: [] };
      }
  
      // Check each user's slot limits
      for (const userId of usersWithStreams) {
        try {
          const quotaInfo = await this.checkStreamingSlotLimit(userId);
          const totalStreamCount = quotaInfo.currentSlots;
  
          // Handle Preview plan (0 slots) - delete all streams regardless of status
          if (quotaInfo.maxSlots === 0 && totalStreamCount > 0) {
            console.log(`[ENFORCE] User ${userId} is on a 0-slot plan but has ${totalStreamCount} stream(s). Deleting all streams.`);
            const deletionResult = await this.deleteAllUserStreamsForPreviewPlan(userId);
            if (deletionResult.deleted > 0) {
              deletionResult.streams.forEach(s => stoppedStreams.push(s));
            }
          }
          // Handle other plans with active streams over the limit
          else if (quotaInfo.maxSlots !== 0 && quotaInfo.hasLimit) {
            const activeStreams = await Stream.findActiveByUserId(userId);
            const activeStreamCount = activeStreams.length;
  
            if (activeStreamCount > quotaInfo.maxSlots) {
              const excessStreams = activeStreamCount - quotaInfo.maxSlots;
              console.log(`⚠️ User ${userId} has ${activeStreamCount} active streams but only ${quotaInfo.maxSlots} slots allowed. Stopping ${excessStreams} streams.`);
              // Stop excess streams (oldest first)
              const streamsToStop = activeStreams.slice(0, excessStreams);
              for (const stream of streamsToStop) {
                try {
                  console.log(`🛑 Stopping excess stream ${stream.id} for user ${userId}`);
                  await streamingService.stopStream(stream.id);
                  stoppedStreams.push({
                    streamId: stream.id,
                    userId: userId,
                    title: stream.title,
                    reason: 'Exceeded slot limit'
                  });
                } catch (error) {
                  console.error(`❌ Error stopping excess stream ${stream.id}:`, error);
                }
              }
            }
          }
        } catch (error) {
          console.error(`❌ Error checking slot limits for user ${userId}:`, error);
        }
      }
  
      console.log(`✅ Slot limit enforcement completed. Processed ${usersWithStreams.length} users. Stopped/deleted ${stoppedStreams.length} streams.`);
      return { stopped: stoppedStreams.length, streams: stoppedStreams };
  
    } catch (error) {
      console.error('❌ Error enforcing slot limits:', error);
      return { stopped: 0, streams: [], error: error.message };
    }
  }

  // Get live streams count (for display purposes)
  static async getLiveStreamsCount(userId) {
    try {
      const liveStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ? AND status = 'live'",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });
      return liveStreams;
    } catch (error) {
      console.error('Error getting live streams count:', error);
      return 0;
    }
  }

  // Check if user has reached streaming slot limit for STARTING streams
  // This counts only LIVE streams, not offline ones that are about to be started
  static async checkStreamingSlotLimitForStarting(userId, excludeStreamId = null) {
    try {
      // STEP 1: Get active subscription (primary source of truth)
      const subscription = await this.getUserSubscription(userId);

      let maxSlots = 0; // Default to 0 slots (Preview plan default)
      let planSource = 'default';

      if (subscription) {
        maxSlots = subscription.max_streaming_slots;
        planSource = 'subscription';

        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Quota Check] User ${userId}: Using subscription data - Plan: ${subscription.plan_name}, Slots: ${maxSlots}`);
        }
      } else {
        // Fallback to user table data
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT max_streaming_slots FROM users WHERE id = ?', [userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });
        maxSlots = user ? user.max_streaming_slots : 0;
        planSource = 'user_table';

        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Quota Check] User ${userId}: Using user table data - Slots: ${maxSlots}`);
        }
      }

      // STEP 2: Count ONLY LIVE streams (not offline streams that haven't started yet)
      // This is the key difference from checkStreamingSlotLimit
      let query = "SELECT COUNT(*) as count FROM streams WHERE user_id = ? AND status = 'live'";
      let params = [userId];

      // Optionally exclude a specific stream (useful when starting a stream)
      if (excludeStreamId) {
        query += " AND id != ?";
        params.push(excludeStreamId);
      }

      const currentLiveStreams = await new Promise((resolve, reject) => {
        db.get(query, params, (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      // STEP 3: Determine if limit is reached
      const hasLimit = maxSlots !== -1 && currentLiveStreams >= maxSlots;

      // Enhanced logging for debugging
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Quota Check - Starting] User ${userId}: ${currentLiveStreams}/${maxSlots} live slots used (source: ${planSource})`);
      }

      return {
        hasLimit,
        maxSlots: maxSlots === -1 ? 'Unlimited' : maxSlots,
        currentSlots: currentLiveStreams,
        planSource: planSource,
        subscriptionId: subscription ? subscription.id : null,
        checkType: 'starting' // Indicate this is for starting validation
      };
    } catch (error) {
      console.error('Error checking streaming slot limit for starting:', error);
      return { hasLimit: true, maxSlots: 0, currentSlots: 0, checkType: 'starting' };
    }
  }

  // Check if user has reached streaming slot limit
  // CENTRALIZED SUBSCRIPTION MANAGEMENT: Always use subscription data as single source of truth
  static async checkStreamingSlotLimit(userId) {
    try {
      // STEP 1: Get active subscription (primary source of truth)
      const subscription = await this.getUserSubscription(userId);

      let maxSlots = 0; // Default to 0 slots (Preview plan default)
      let planSource = 'default';

      if (subscription) {
        // Use subscription data as authoritative source
        maxSlots = subscription.max_streaming_slots;
        planSource = 'subscription';

        // Log for debugging subscription inconsistencies
        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Quota Check] User ${userId}: Using subscription data - Plan: ${subscription.plan_name}, Slots: ${maxSlots}`);
        }
      } else {
        // FALLBACK: If no active subscription found, user should be on Preview plan (0 slots)
        // This handles cases where subscription expired or was cancelled
        maxSlots = 0;
        planSource = 'no_subscription';

        // Check if user table has different values (indicates data inconsistency)
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT plan_type, max_streaming_slots FROM users WHERE id = ?', [userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });

        if (user && (user.max_streaming_slots > 0 || user.plan_type !== 'Preview')) {
          // Data inconsistency detected - log warning
          console.warn(`[SUBSCRIPTION INCONSISTENCY] User ${userId}: No active subscription but user table shows plan_type=${user.plan_type}, slots=${user.max_streaming_slots}. Using 0 slots (Preview plan).`);

          // TODO: Consider auto-fixing this inconsistency by updating user table
          // For now, we enforce 0 slots to maintain security
        }
      }

      // STEP 2: Count current streams (all streams except deleted ones)
      // This includes 'offline', 'live', and 'scheduled' streams as they all occupy slots
      const currentStreams = await new Promise((resolve, reject) => {
        db.get(
          "SELECT COUNT(*) as count FROM streams WHERE user_id = ?",
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          }
        );
      });

      // STEP 3: Determine if limit is reached
      const hasLimit = maxSlots !== -1 && currentStreams >= maxSlots;

      // Enhanced logging for debugging
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Quota Check] User ${userId}: ${currentStreams}/${maxSlots} slots used (source: ${planSource})`);
      }

      return {
        hasLimit,
        maxSlots: maxSlots === -1 ? 'Unlimited' : maxSlots,
        currentSlots: currentStreams,
        planSource: planSource, // Added for debugging
        subscriptionId: subscription ? subscription.id : null // Added for tracking
      };
    } catch (error) {
      console.error('Error checking streaming slot limit:', error);
      return { hasLimit: true, maxSlots: 0, currentSlots: 0 };
    }
  }

  // Check storage limit
  static async checkStorageLimit(userId, additionalSizeGB = 0) {
    try {
      const subscription = await this.getUserSubscription(userId);

      // Get current storage usage from user table
      const user = await new Promise((resolve, reject) => {
        db.get('SELECT used_storage_gb, max_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      const currentStorage = user ? user.used_storage_gb || 0 : 0;

      // Use subscription limits if available, otherwise use user plan limits
      const maxStorage = subscription ? subscription.max_storage_gb : (user ? user.max_storage_gb || 5 : 5);
      const wouldExceed = (currentStorage + additionalSizeGB) > maxStorage;

      return {
        hasLimit: wouldExceed,
        maxStorage,
        currentStorage,
        availableStorage: maxStorage - currentStorage
      };
    } catch (error) {
      console.error('Error checking storage limit:', error);
      return { hasLimit: true, maxStorage: 5, currentStorage: 0 };
    }
  }

  // Update user storage usage with race condition protection
  static async updateStorageUsage(userId, sizeGB) {
    // Implement simple locking mechanism
    if (storageLocks.has(userId)) {
      await storageLocks.get(userId);
    }

    const lockPromise = this._updateStorageUsageWithLock(userId, sizeGB);
    storageLocks.set(userId, lockPromise);

    try {
      const result = await lockPromise;
      return result;
    } finally {
      storageLocks.delete(userId);
    }
  }

  // Internal storage update function with database serialization
  static _updateStorageUsageWithLock(userId, sizeGB) {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.get('SELECT used_storage_gb FROM users WHERE id = ?', [userId], (err, row) => {
          if (err) {
            console.error('❌ Error getting current storage:', err);
            return reject(err);
          }

          const currentStorage = row ? (row.used_storage_gb || 0) : 0;
          const newStorage = currentStorage + sizeGB;
          const finalStorage = Math.max(0, newStorage);

          if (newStorage < 0) {
            console.warn(`⚠️ Storage would go negative for user ${userId}: ${currentStorage}GB + ${sizeGB}GB = ${newStorage}GB. Setting to 0GB instead.`);
          }

          db.run(
            'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [finalStorage, userId],
            function (err) {
              if (err) {
                console.error('❌ Error updating storage:', err);
                return reject(err);
              }
              resolve({
                userId,
                addedStorage: sizeGB,
                previousStorage: currentStorage,
                newStorage: finalStorage,
                wasNegativePrevented: newStorage < 0
              });
            }
          );
        });
      });
    });
  }

  // Create new subscription plan
  static createPlan(planData) {
    const id = uuidv4();
    const {
      name,
      price,
      currency = 'USD',
      billing_period = 'monthly',
      max_streaming_slots = 1,
      max_storage_gb = 5,
      features = []
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO subscription_plans
         (id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, features)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [id, name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features)],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Update subscription plan
  static updatePlan(planId, planData) {
    const {
      name,
      price,
      currency,
      billing_period,
      max_streaming_slots,
      max_storage_gb,
      features
    } = planData;

    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE subscription_plans
         SET name = ?, price = ?, currency = ?, billing_period = ?,
             max_streaming_slots = ?, max_storage_gb = ?, features = ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, price, currency, billing_period, max_streaming_slots, max_storage_gb, JSON.stringify(features), planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({
            id: planId,
            name,
            price,
            currency,
            billing_period,
            max_streaming_slots,
            max_storage_gb,
            features
          });
        }
      );
    });
  }

  // Delete subscription plan
  static deletePlan(planId) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, deleted: true });
        }
      );
    });
  }

  // Get all plans including inactive ones (admin only)
  static getAllPlansAdmin() {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows.map(row => ({
          ...row,
          features: JSON.parse(row.features || '[]')
        })));
      });
    });
  }

  // Update plan status (activate/deactivate)
  static updatePlanStatus(planId, isActive) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE subscription_plans SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [isActive ? 1 : 0, planId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: planId, is_active: isActive });
        }
      );
    });
  }

  // Get subscribers for a specific plan
  static getPlanSubscribers(planId, planName) {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, email, role, is_active, created_at FROM users WHERE plan_type = ? ORDER BY created_at DESC',
        [planName],
        (err, rows) => {
          if (err) {
            return reject(err);
          }
          resolve(rows);
        }
      );
    });
  }

  // Check and handle expired trials
  static async checkExpiredTrials() {
    try {
      const User = require('./User');

      return new Promise((resolve, reject) => {
        db.all(`
          SELECT id, username, trial_end_date
          FROM users
          WHERE trial_end_date IS NOT NULL
          AND trial_end_date < datetime('now')
          AND (trial_slots > 0 OR trial_storage_gb > 0)
        `, [], async (err, rows) => {
          if (err) {
            return reject(err);
          }

          const expiredUsers = [];
          for (const user of rows) {
            try {
              await User.removeExpiredTrial(user.id);

              // After trial removal, check if user is now on Preview plan
              const updatedUser = await User.findById(user.id);
              if (updatedUser && updatedUser.plan_type === 'Preview') {
                console.log(`[TRIAL EXPIRY] User ${user.username} reverted to Preview plan. Deleting associated streams.`);
                await this.deleteAllUserStreamsForPreviewPlan(user.id);
              }
              
              expiredUsers.push({
                id: user.id,
                username: user.username,
                trial_end_date: user.trial_end_date,
                reverted_to_plan: updatedUser ? updatedUser.plan_type : 'Unknown'
              });
            } catch (error) {
              console.error(`Failed to process expired trial for user ${user.id}:`, error);
            }
          }

          resolve(expiredUsers);
        });
      });
    } catch (error) {
      console.error('Error checking expired trials:', error);
      throw error;
    }
  }

  // Get user's subscription including expired ones (for debugging)
  static getUserSubscriptionAll(userId) {
    return new Promise((resolve, reject) => {
      db.get(`
        SELECT us.*, sp.name as plan_name, sp.max_streaming_slots, sp.max_storage_gb, sp.features
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) {
          return reject(err);
        }
        if (row) {
          row.features = JSON.parse(row.features || '[]');
        }
        resolve(row);
      });
    });
  }

  // Get plan by name (only active plans, case-insensitive)
  static getPlanByName(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?)) AND is_active = 1',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // CENTRALIZED SUBSCRIPTION MANAGEMENT: New methods for enhanced admin functionality

  // Get subscription inconsistencies (users with mismatched plan data)
  static getSubscriptionInconsistencies() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          u.id as user_id,
          u.username,
          u.plan_type as user_plan,
          u.max_streaming_slots as user_slots,
          u.max_storage_gb as user_storage,
          us.id as subscription_id,
          sp.name as subscription_plan,
          sp.max_streaming_slots as subscription_slots,
          sp.max_storage_gb as subscription_storage,
          us.status as subscription_status,
          us.end_date,
          CASE
            WHEN us.id IS NULL THEN 'no_subscription'
            WHEN u.plan_type != sp.name THEN 'plan_mismatch'
            WHEN u.max_streaming_slots != sp.max_streaming_slots THEN 'slots_mismatch'
            WHEN u.max_storage_gb != sp.max_storage_gb THEN 'storage_mismatch'
            ELSE 'consistent'
          END as inconsistency_type
        FROM users u
        LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
        LEFT JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE (
          us.id IS NULL AND u.plan_type != 'Preview'
          OR (us.id IS NOT NULL AND (
            u.plan_type != sp.name
            OR u.max_streaming_slots != sp.max_streaming_slots
            OR u.max_storage_gb != sp.max_storage_gb
          ))
        )
        ORDER BY u.username
      `;

      db.all(query, [], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows);
      });
    });
  }

  // Fix subscription inconsistency for a specific user
  static async fixUserSubscriptionInconsistency(userId, action = 'sync_to_subscription') {
    try {
      const subscription = await this.getUserSubscription(userId);

      if (!subscription) {
        // No active subscription - user should be on Preview plan
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
            ['Preview', 0, 0.015, userId], // 15MB = 0.015GB
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        return {
          success: true,
          action: 'updated_user_to_preview',
          message: 'User updated to Preview plan (no active subscription)'
        };
      }

      if (action === 'sync_to_subscription') {
        // Update user table to match subscription
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
            [subscription.plan_name, subscription.max_streaming_slots, subscription.max_storage_gb, userId],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        return {
          success: true,
          action: 'synced_user_to_subscription',
          message: `User updated to match subscription: ${subscription.plan_name}`
        };
      }

      return {
        success: false,
        message: 'Invalid action specified'
      };

    } catch (error) {
      console.error('Error fixing subscription inconsistency:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Bulk fix all subscription inconsistencies
  static async fixAllSubscriptionInconsistencies() {
    try {
      const inconsistencies = await this.getSubscriptionInconsistencies();
      const results = [];

      for (const inconsistency of inconsistencies) {
        const result = await this.fixUserSubscriptionInconsistency(inconsistency.user_id);
        results.push({
          userId: inconsistency.user_id,
          username: inconsistency.username,
          inconsistencyType: inconsistency.inconsistency_type,
          ...result
        });
      }

      return {
        success: true,
        totalProcessed: results.length,
        results: results
      };

    } catch (error) {
      console.error('Error in bulk fix:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // Get plan by name including inactive plans (for admin operations, case-insensitive)
  static getPlanByNameAll(name) {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM subscription_plans WHERE LOWER(TRIM(name)) = LOWER(TRIM(?))',
        [name],
        (err, row) => {
          if (err) {
            return reject(err);
          }
          if (row) {
            resolve({
              ...row,
              features: JSON.parse(row.features || '[]')
            });
          } else {
            resolve(null);
          }
        }
      );
    });
  }

  // Calculate prorated upgrade pricing
  static async calculateProratedUpgrade(userId, newPlanId) {
    try {
      // Get current subscription
      const currentSubscription = await this.getUserSubscription(userId);
      if (!currentSubscription) {
        // No current subscription, return full price
        const newPlan = await this.getPlanById(newPlanId);
        if (!newPlan) {
          throw new Error('New plan not found');
        }

        return {
          isUpgrade: false,
          currentPlan: null,
          newPlan: newPlan,
          remainingDays: 0,
          remainingValue: 0,
          newPlanPrice: newPlan.price,
          upgradePrice: newPlan.price,
          adminFee: Math.round(newPlan.price * 0.01), // 1% admin fee
          totalPayment: newPlan.price + Math.round(newPlan.price * 0.01),
          savings: 0
        };
      }

      // Get current and new plan details
      const [currentPlan, newPlan] = await Promise.all([
        this.getPlanById(currentSubscription.plan_id),
        this.getPlanById(newPlanId)
      ]);

      if (!currentPlan || !newPlan) {
        throw new Error('Plan not found');
      }

      // Prevent downgrade or same plan
      if (newPlan.price <= currentPlan.price) {
        throw new Error('Cannot downgrade or select same plan');
      }

      // Calculate remaining days
      const now = new Date();
      const endDate = new Date(currentSubscription.end_date);
      const remainingDays = Math.max(0, Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));

      // Calculate remaining value from current plan
      const dailyRate = currentPlan.price / 30;
      const remainingValue = Math.round(dailyRate * remainingDays);

      // Calculate upgrade price (new plan price - remaining value)
      const upgradePrice = Math.max(0, newPlan.price - remainingValue);

      // Calculate admin fee (1% of upgrade price)
      const adminFee = Math.round(upgradePrice * 0.01);

      // Total payment including admin fee
      const totalPayment = upgradePrice + adminFee;

      // Calculate savings
      const savings = remainingValue;

      return {
        isUpgrade: true,
        currentPlan: currentPlan,
        newPlan: newPlan,
        remainingDays: remainingDays,
        remainingValue: remainingValue,
        newPlanPrice: newPlan.price,
        upgradePrice: upgradePrice,
        adminFee: adminFee,
        totalPayment: totalPayment,
        savings: savings,
        currentSubscriptionEndDate: currentSubscription.end_date
      };

    } catch (error) {
      console.error('Error calculating prorated upgrade:', error);
      throw error;
    }
  }

  // Create prorated upgrade subscription
  static async createProratedUpgrade(userId, newPlanId, paymentId = null) {
    try {
      // Get current subscription
      const currentSubscription = await this.getUserSubscription(userId);

      // Get new plan details
      const newPlan = await this.getPlanById(newPlanId);
      if (!newPlan) {
        throw new Error('New plan not found');
      }

      let endDate;

      if (currentSubscription) {
        // For upgrades, keep the same end date
        endDate = currentSubscription.end_date;

        // Mark current subscription as upgraded
        await this.updateSubscriptionStatus(currentSubscription.id, 'upgraded');
      } else {
        // For new subscriptions, set 30 days from now
        const newEndDate = new Date();
        newEndDate.setDate(newEndDate.getDate() + 30);
        endDate = newEndDate.toISOString();
      }

      // Create new subscription
      const subscription = await this.createSubscription({
        user_id: userId,
        plan_id: newPlanId,
        status: 'active',
        end_date: endDate,
        payment_method: paymentId ? 'midtrans' : 'manual',
        payment_id: paymentId
      });

      // Update user's plan limits
      const User = require('./User');
      await User.updatePlan(
        userId,
        newPlan.name,
        newPlan.max_streaming_slots,
        newPlan.max_storage_gb
      );

      return subscription;

    } catch (error) {
      console.error('Error creating prorated upgrade:', error);
      throw error;
    }
  }

  // Renew expired subscription with the same plan
  static async renewSubscription(userId, planId, durationDays = 30, paymentId = null) {
    try {
      // Get the plan details
      const plan = await this.getPlanById(planId);
      if (!plan) {
        throw new Error('Plan not found');
      }

      // Check if user has an expired subscription for this plan
      const expiredSubscription = await this.getUserSubscriptionIncludingExpired(userId);

      if (expiredSubscription && !expiredSubscription.isExpired) {
        throw new Error('User already has an active subscription');
      }

      // Calculate new end date
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + durationDays);

      // Create new subscription
      const subscription = await this.createSubscription({
        user_id: userId,
        plan_id: planId,
        status: 'active',
        end_date: endDate.toISOString(),
        payment_method: paymentId ? 'midtrans' : 'manual',
        payment_id: paymentId
      });

      // Update user's plan limits
      const User = require('./User');
      await User.updatePlan(
        userId,
        plan.name,
        plan.max_streaming_slots,
        plan.max_storage_gb
      );

      console.log(`🔄 User ${userId} renewed subscription to ${plan.name} plan for ${durationDays} days`);
      // Send renewal success notification
      try {
        const notificationService = require('../services/notificationService');
        await notificationService.notifySubscriptionRenewed(userId, plan.name, endDate.toISOString());
      } catch (notifError) {
        console.error('Error sending renewal notification:', notifError);
      }

      return subscription;

    } catch (error) {
      console.error('Error renewing subscription:', error);
      throw error;
    }
  }

  // Extend existing active subscription
  static async extendSubscription(userId, additionalDays = 30, paymentId = null) {
    try {
      // Get current active subscription
      const currentSubscription = await this.getUserSubscription(userId);

      if (!currentSubscription) {
        throw new Error('No active subscription found to extend');
      }

      // Calculate new end date by adding days to current end date
      const currentEndDate = new Date(currentSubscription.end_date);
      const newEndDate = new Date(currentEndDate);
      newEndDate.setDate(newEndDate.getDate() + additionalDays);

      // Update the subscription end date
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE user_subscriptions
           SET end_date = ?, updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [newEndDate.toISOString(), currentSubscription.id],
          function (err) {
            if (err) {
              return reject(err);
            }
            resolve();
          }
        );
      });

      console.log(`⏰ Extended subscription ${currentSubscription.id} by ${additionalDays} days until ${newEndDate.toISOString()}`);

      return {
        ...currentSubscription,
        end_date: newEndDate.toISOString(),
        extended_days: additionalDays
      };

    } catch (error) {
      console.error('Error extending subscription:', error);
      throw error;
    }
  }

  // Check if user can renew (has expired subscription)
  static async canRenewSubscription(userId) {
    try {
      const subscription = await this.getUserSubscriptionIncludingExpired(userId);

      // User can renew if they have an expired subscription or no subscription at all
      return {
        canRenew: !subscription || subscription.isExpired,
        expiredSubscription: subscription && subscription.isExpired ? subscription : null,
        currentPlan: subscription ? subscription.plan_name : 'Preview'
      };
    } catch (error) {
      console.error('Error checking renewal eligibility:', error);
      return { canRenew: false, expiredSubscription: null, currentPlan: 'Preview' };
    }
  }

  // Check if user has an expired subscription (allows downgrades)
  static async hasExpiredSubscription(userId) {
    try {
      const subscription = await this.getUserSubscriptionIncludingExpired(userId);

      return {
        hasExpired: subscription && subscription.isExpired,
        expiredSubscription: subscription && subscription.isExpired ? subscription : null,
        previousPlan: subscription && subscription.isExpired ? subscription.plan_name : null,
        previousPrice: subscription && subscription.isExpired ? subscription.price : null
      };
    } catch (error) {
      console.error('Error checking expired subscription status:', error);
      return { hasExpired: false, expiredSubscription: null, previousPlan: null, previousPrice: null };
    }
  }

  // Get all subscriptions with user and plan details (for admin)
  static getAllSubscriptionsWithDetails(limit = 20, offset = 0, search = '') {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT
          s.*,
          u.username,
          u.email,
          u.plan_type as current_plan,
          p.name as plan_name,
          p.price as plan_price,
          p.currency as plan_currency
        FROM user_subscriptions s
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        WHERE 1=1
      `;

      const params = [];

      if (search && search.trim() !== '') {
        query += ` AND (
          u.username LIKE ? OR
          u.email LIKE ? OR
          p.name LIKE ? OR
          s.status LIKE ? OR
          s.payment_method LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
      }

      query += ` ORDER BY s.created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      db.all(query, params, (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  // Count all subscriptions
  static countAllSubscriptions(search = '') {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT COUNT(*) as count
        FROM user_subscriptions s
        LEFT JOIN users u ON s.user_id = u.id
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        WHERE 1=1
      `;

      const params = [];

      if (search && search.trim() !== '') {
        query += ` AND (
          u.username LIKE ? OR
          u.email LIKE ? OR
          p.name LIKE ? OR
          s.status LIKE ? OR
          s.payment_method LIKE ?
        )`;
        const searchTerm = `%${search.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
      }

      db.get(query, params, (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row.count || 0);
      });
    });
  }

  // Get subscription by ID
  static getSubscriptionById(subscriptionId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT s.*, p.name as plan_name, u.username
        FROM user_subscriptions s
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id = ?
      `;

      db.get(query, [subscriptionId], (err, row) => {
        if (err) {
          return reject(err);
        }
        resolve(row || null);
      });
    });
  }

  // Update subscription end date
  static updateSubscriptionEndDate(subscriptionId, endDate) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET end_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [endDate, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, end_date: endDate });
        }
      );
    });
  }

  // Get user subscription history
  static getUserSubscriptionHistory(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          s.*,
          p.name as plan_name,
          p.price as plan_price,
          p.currency as plan_currency
        FROM user_subscriptions s
        LEFT JOIN subscription_plans p ON s.plan_id = p.id
        WHERE s.user_id = ?
        ORDER BY s.created_at DESC
      `;

      db.all(query, [userId], (err, rows) => {
        if (err) {
          return reject(err);
        }
        resolve(rows || []);
      });
    });
  }

  // Update subscription status
  static updateSubscriptionStatus(subscriptionId, status) {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, subscriptionId],
        function (err) {
          if (err) {
            return reject(err);
          }
          resolve({ id: subscriptionId, status });
        }
      );
    });
  }

  // Get subscription statistics for admin dashboard
  static getSubscriptionStats() {
    return new Promise((resolve, reject) => {
      const queries = [
        'SELECT COUNT(*) as total FROM user_subscriptions',
        'SELECT COUNT(*) as active FROM user_subscriptions WHERE status = "active"',
        'SELECT COUNT(*) as expired FROM user_subscriptions WHERE status = "expired"',
        'SELECT COUNT(*) as cancelled FROM user_subscriptions WHERE status = "cancelled"',
        `SELECT
           p.name as plan_name,
           COUNT(*) as subscribers
         FROM user_subscriptions s
         LEFT JOIN subscription_plans p ON s.plan_id = p.id
         WHERE s.status = "active"
         GROUP BY s.plan_id, p.name`
      ];

      Promise.all(queries.map(query => {
        return new Promise((resolve, reject) => {
          if (query.includes('GROUP BY')) {
            db.all(query, (err, rows) => {
              if (err) reject(err);
              else resolve(rows || []);
            });
          } else {
            db.get(query, (err, row) => {
              if (err) reject(err);
              else resolve(row || {});
            });
          }
        });
      }))
      .then(results => {
        resolve({
          total: results[0].total || 0,
          active: results[1].active || 0,
          expired: results[2].expired || 0,
          cancelled: results[3].cancelled || 0,
          planDistribution: results[4] || []
        });
      })
      .catch(reject);
    });
  }

  // CENTRALIZED SUBSCRIPTION MANAGEMENT: Enhanced admin functionality

  // Enhanced subscription editing for admin interface
  static async updateSubscriptionDetails(subscriptionId, updates) {
    try {
      const allowedFields = ['plan_id', 'status', 'start_date', 'end_date', 'payment_method'];
      const updateFields = [];
      const updateValues = [];

      // Validate and build update query
      for (const [field, value] of Object.entries(updates)) {
        if (allowedFields.includes(field) && value !== undefined) {
          updateFields.push(`${field} = ?`);
          updateValues.push(value);
        }
      }

      if (updateFields.length === 0) {
        return { success: false, message: 'No valid fields to update' };
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      updateValues.push(subscriptionId);

      const query = `UPDATE user_subscriptions SET ${updateFields.join(', ')} WHERE id = ?`;

      await new Promise((resolve, reject) => {
        db.run(query, updateValues, function (err) {
          if (err) reject(err);
          else resolve();
        });
      });

      // If plan_id was updated, sync user table
      if (updates.plan_id) {
        const subscription = await this.getSubscriptionById(subscriptionId);
        if (subscription) {
          const plan = await this.getPlanById(updates.plan_id);
          if (plan) {
            await new Promise((resolve, reject) => {
              db.run(
                'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
                [plan.name, plan.max_streaming_slots, plan.max_storage_gb, subscription.user_id],
                function (err) {
                  if (err) reject(err);
                  else resolve();
                }
              );
            });
          }
        }
      }

      return { success: true, message: 'Subscription updated successfully' };

    } catch (error) {
      console.error('Error updating subscription details:', error);
      return { success: false, message: error.message };
    }
  }

  // Get detailed subscription analytics for admin dashboard
  static async getSubscriptionAnalytics() {
    try {
      const analytics = await new Promise((resolve, reject) => {
        const query = `
          SELECT
            sp.name as plan_name,
            COUNT(us.id) as total_subscriptions,
            COUNT(CASE WHEN us.status = 'active' THEN 1 END) as active_subscriptions,
            COUNT(CASE WHEN us.status = 'expired' THEN 1 END) as expired_subscriptions,
            COUNT(CASE WHEN us.status = 'cancelled' THEN 1 END) as cancelled_subscriptions,
            AVG(sp.price) as avg_price,
            SUM(CASE WHEN us.status = 'active' THEN sp.price ELSE 0 END) as active_revenue
          FROM subscription_plans sp
          LEFT JOIN user_subscriptions us ON sp.id = us.plan_id
          WHERE sp.is_active = 1
          GROUP BY sp.id, sp.name
          ORDER BY sp.price ASC
        `;

        db.all(query, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // Get inconsistency count
      const inconsistencies = await this.getSubscriptionInconsistencies();

      return {
        planAnalytics: analytics,
        inconsistencyCount: inconsistencies.length,
        totalInconsistencies: inconsistencies
      };

    } catch (error) {
      console.error('Error getting subscription analytics:', error);
      throw error;
    }
  }

  // Create subscription with automatic user sync - ENHANCED WITH TRANSACTION SAFETY
  static async createSubscriptionWithSync(subscriptionData) {
    return new Promise((resolve, reject) => {
      // Start database transaction
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const rollback = (error) => {
          db.run('ROLLBACK', () => {
            console.error('Transaction rolled back:', error);
            resolve({
              success: false,
              message: error.message || 'Transaction failed'
            });
          });
        };

        const commit = (result) => {
          db.run('COMMIT', (err) => {
            if (err) {
              rollback(err);
            } else {
              resolve(result);
            }
          });
        };

        try {
          // Validate required fields
          const { user_id, plan_id, status = 'active', start_date, end_date, payment_method = 'admin_manual' } = subscriptionData;

          if (!user_id || !plan_id) {
            throw new Error('User ID and Plan ID are required');
          }

          // Step 1: Get plan details
          db.get('SELECT * FROM subscription_plans WHERE id = ? AND is_active = 1', [plan_id], (err, plan) => {
            if (err) return rollback(err);
            if (!plan) return rollback(new Error('Plan not found'));

            // Step 2: Check for existing active subscriptions and mark them as superseded
            db.all('SELECT id FROM user_subscriptions WHERE user_id = ? AND status = ?', [user_id, 'active'], (err, existingSubscriptions) => {
              if (err) return rollback(err);

              // Mark all existing active subscriptions as superseded
              if (existingSubscriptions.length > 0) {
                const updatePromises = existingSubscriptions.map(sub => {
                  return new Promise((resolve, reject) => {
                    db.run(
                      'UPDATE user_subscriptions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                      ['superseded', sub.id],
                      function (err) {
                        if (err) reject(err);
                        else resolve();
                      }
                    );
                  });
                });

                Promise.all(updatePromises).then(() => {
                  createNewSubscription();
                }).catch(rollback);
              } else {
                createNewSubscription();
              }

              function createNewSubscription() {
                // Step 3: Create new subscription
                const subscriptionId = uuidv4();
                const subscriptionStartDate = start_date || new Date().toISOString();

                db.run(
                  `INSERT INTO user_subscriptions (id, user_id, plan_id, status, start_date, end_date, payment_method, payment_id, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
                  [subscriptionId, user_id, plan_id, status, subscriptionStartDate, end_date, payment_method, null],
                  function (err) {
                    if (err) return rollback(err);

                    // Step 4: Update user table to match subscription
                    db.run(
                      'UPDATE users SET plan_type = ?, max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
                      [plan.name, plan.max_streaming_slots, plan.max_storage_gb, user_id],
                      function (err) {
                        if (err) return rollback(err);

                        // Success - commit transaction
                        commit({
                          success: true,
                          subscription: {
                            id: subscriptionId,
                            user_id,
                            plan_id,
                            status,
                            start_date: subscriptionStartDate,
                            end_date,
                            payment_method
                          },
                          plan,
                          message: 'Subscription created and user data synchronized with transaction safety'
                        });
                      }
                    );
                  }
                );
              }
            });
          });

        } catch (error) {
          rollback(error);
        }
      });
    });
  }
}

module.exports = Subscription;
