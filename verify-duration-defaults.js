#!/usr/bin/env node

/**
 * Verify Duration Defaults
 * Checks if duration defaults are working correctly in StreamOnPod
 */

const fs = require('fs');

console.log('🔍 Verifying Duration Default Settings');
console.log('=====================================\n');

class DurationDefaultVerifier {
  constructor() {
    this.findings = [];
  }

  async runVerification() {
    console.log('🚀 Starting duration default verification...\n');

    try {
      // 1. Check frontend form defaults
      await this.checkFrontendDefaults();
      
      // 2. Check backend processing
      await this.checkBackendProcessing();
      
      // 3. Check database current state
      await this.checkDatabaseState();
      
      // 4. Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    }
  }

  async checkFrontendDefaults() {
    console.log('📱 1. CHECKING FRONTEND FORM DEFAULTS');
    console.log('====================================\n');

    try {
      // Check dashboard.ejs for default values
      const dashboardPath = './views/dashboard.ejs';
      
      if (fs.existsSync(dashboardPath)) {
        const content = fs.readFileSync(dashboardPath, 'utf8');
        
        // Check create form default
        const createFormMatch = content.match(/<input[^>]*id="duration"[^>]*value="0"[^>]*>/);
        if (createFormMatch) {
          this.findings.push({
            type: 'SUCCESS',
            area: 'Frontend - Create Form',
            details: 'Duration field has default value="0"',
            impact: 'New streams will default to indefinite duration'
          });
          console.log('✅ Create form: Duration field defaults to 0');
        } else {
          this.findings.push({
            type: 'WARNING',
            area: 'Frontend - Create Form',
            details: 'Duration field may not have default value',
            impact: 'Users might create streams with undefined duration'
          });
          console.log('⚠️  Create form: Duration field default not found');
        }

        // Check edit form default handling
        const editFormMatch = content.match(/document\.getElementById\('editDuration'\)\.value = '0'/);
        if (editFormMatch) {
          this.findings.push({
            type: 'SUCCESS',
            area: 'Frontend - Edit Form',
            details: 'Edit form defaults empty duration to 0',
            impact: 'Existing streams without duration will show 0'
          });
          console.log('✅ Edit form: Empty duration defaults to 0');
        } else {
          this.findings.push({
            type: 'WARNING',
            area: 'Frontend - Edit Form',
            details: 'Edit form may not handle empty duration properly',
            impact: 'Editing streams might not preserve indefinite duration'
          });
          console.log('⚠️  Edit form: Default handling not found');
        }

        // Check for helper text
        const helperTextMatch = content.match(/Leave empty or set to 0 for indefinite streaming/);
        if (helperTextMatch) {
          this.findings.push({
            type: 'SUCCESS',
            area: 'Frontend - User Guidance',
            details: 'Helper text explains indefinite streaming',
            impact: 'Users understand how to create indefinite streams'
          });
          console.log('✅ User guidance: Helper text found');
        }

      } else {
        this.findings.push({
          type: 'ERROR',
          area: 'Frontend',
          details: 'Dashboard file not found',
          impact: 'Cannot verify frontend defaults'
        });
      }

      console.log('');

    } catch (error) {
      console.error('❌ Frontend check failed:', error);
    }
  }

  async checkBackendProcessing() {
    console.log('⚙️  2. CHECKING BACKEND PROCESSING');
    console.log('=================================\n');

    try {
      // Check app.js for duration processing
      const appPath = './app.js';
      
      if (fs.existsSync(appPath)) {
        const content = fs.readFileSync(appPath, 'utf8');
        
        // Check create stream processing
        const createProcessingMatch = content.match(/streamData\.duration = 0.*Default to 0 for indefinite streaming/);
        if (createProcessingMatch) {
          this.findings.push({
            type: 'SUCCESS',
            area: 'Backend - Create Processing',
            details: 'Create stream defaults empty duration to 0',
            impact: 'New streams without duration will be indefinite'
          });
          console.log('✅ Create processing: Defaults empty duration to 0');
        } else {
          this.findings.push({
            type: 'WARNING',
            area: 'Backend - Create Processing',
            details: 'Create processing may not default duration to 0',
            impact: 'New streams might have null duration'
          });
          console.log('⚠️  Create processing: Default logic not found');
        }

        // Check update stream processing
        const updateProcessingMatch = content.match(/req\.body\.duration === '' \? 0 : parseInt/);
        if (updateProcessingMatch) {
          this.findings.push({
            type: 'SUCCESS',
            area: 'Backend - Update Processing',
            details: 'Update stream handles empty duration as 0',
            impact: 'Editing streams preserves indefinite duration'
          });
          console.log('✅ Update processing: Handles empty duration as 0');
        } else {
          this.findings.push({
            type: 'WARNING',
            area: 'Backend - Update Processing',
            details: 'Update processing may not handle empty duration',
            impact: 'Editing streams might break indefinite duration'
          });
          console.log('⚠️  Update processing: Empty duration handling not found');
        }

      } else {
        this.findings.push({
          type: 'ERROR',
          area: 'Backend',
          details: 'App.js file not found',
          impact: 'Cannot verify backend processing'
        });
      }

      console.log('');

    } catch (error) {
      console.error('❌ Backend check failed:', error);
    }
  }

  async checkDatabaseState() {
    console.log('🗄️  3. CHECKING DATABASE STATE');
    console.log('=============================\n');

    try {
      const { db } = require('./db/database');
      
      // Check current streams duration distribution
      const durationStats = await new Promise((resolve, reject) => {
        db.all(`
          SELECT 
            CASE 
              WHEN duration IS NULL THEN 'NULL'
              WHEN duration = 0 THEN 'ZERO'
              WHEN duration > 0 THEN 'POSITIVE'
              ELSE 'OTHER'
            END as duration_type,
            COUNT(*) as count
          FROM streams 
          WHERE created_at > datetime('now', '-7 days')
          GROUP BY duration_type
          ORDER BY count DESC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      console.log('📊 Duration Distribution (Last 7 Days):');
      let totalStreams = 0;
      let indefiniteStreams = 0;

      durationStats.forEach(stat => {
        console.log(`   ${stat.duration_type}: ${stat.count} streams`);
        totalStreams += stat.count;
        if (stat.duration_type === 'NULL' || stat.duration_type === 'ZERO') {
          indefiniteStreams += stat.count;
        }
      });

      if (totalStreams > 0) {
        const indefinitePercentage = (indefiniteStreams / totalStreams) * 100;
        console.log(`\n📈 Indefinite Streams: ${indefiniteStreams}/${totalStreams} (${indefinitePercentage.toFixed(1)}%)`);

        this.findings.push({
          type: indefinitePercentage > 50 ? 'SUCCESS' : 'INFO',
          area: 'Database - Current State',
          details: `${indefinitePercentage.toFixed(1)}% of recent streams are indefinite`,
          impact: indefinitePercentage > 50 ? 'Most streams are set for indefinite duration' : 'Some streams still have duration limits'
        });

        if (indefinitePercentage < 50) {
          console.log('\n💡 Recommendation: Consider updating existing streams to indefinite duration');
        }
      } else {
        console.log('📭 No streams found in the last 7 days');
      }

      // Check for streams that might be affected by duration limits
      const limitedStreams = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, duration, status, created_at
          FROM streams 
          WHERE duration > 0 
          AND status IN ('live', 'scheduled')
          ORDER BY created_at DESC
          LIMIT 10
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      if (limitedStreams.length > 0) {
        console.log(`\n⚠️  Found ${limitedStreams.length} active streams with duration limits:`);
        limitedStreams.forEach(stream => {
          console.log(`   • "${stream.title}" - ${stream.duration} minutes (${stream.status})`);
        });

        this.findings.push({
          type: 'WARNING',
          area: 'Database - Active Streams',
          details: `${limitedStreams.length} active streams have duration limits`,
          impact: 'These streams will auto-terminate when duration is reached'
        });
      } else {
        console.log('\n✅ No active streams with duration limits found');
      }

      console.log('');

    } catch (error) {
      console.error('❌ Database check failed:', error);
      this.findings.push({
        type: 'ERROR',
        area: 'Database',
        details: `Database check failed: ${error.message}`,
        impact: 'Cannot verify current stream duration settings'
      });
    }
  }

  generateReport() {
    console.log('📋 DURATION DEFAULTS VERIFICATION REPORT');
    console.log('========================================\n');

    const successCount = this.findings.filter(f => f.type === 'SUCCESS').length;
    const warningCount = this.findings.filter(f => f.type === 'WARNING').length;
    const errorCount = this.findings.filter(f => f.type === 'ERROR').length;

    console.log(`📊 SUMMARY:`);
    console.log(`   ✅ Success: ${successCount}`);
    console.log(`   ⚠️  Warnings: ${warningCount}`);
    console.log(`   ❌ Errors: ${errorCount}\n`);

    // Group findings by type
    ['SUCCESS', 'WARNING', 'ERROR', 'INFO'].forEach(type => {
      const findings = this.findings.filter(f => f.type === type);
      if (findings.length > 0) {
        const icon = type === 'SUCCESS' ? '✅' : type === 'WARNING' ? '⚠️' : type === 'ERROR' ? '❌' : 'ℹ️';
        console.log(`${icon} ${type} FINDINGS:`);
        
        findings.forEach((finding, index) => {
          console.log(`\n${index + 1}. ${finding.area}`);
          console.log(`   Details: ${finding.details}`);
          console.log(`   Impact: ${finding.impact}`);
        });
        console.log('');
      }
    });

    // Overall assessment
    if (errorCount === 0 && warningCount <= 1) {
      console.log('🎉 OVERALL ASSESSMENT: EXCELLENT');
      console.log('   Duration defaults are properly configured!');
      console.log('   ✅ New streams will default to indefinite duration');
      console.log('   ✅ Users receive proper guidance');
      console.log('   ✅ Backend processing handles defaults correctly');
    } else if (errorCount === 0 && warningCount <= 3) {
      console.log('👍 OVERALL ASSESSMENT: GOOD');
      console.log('   Duration defaults are mostly working correctly');
      console.log('   ⚠️  Some minor improvements recommended');
    } else {
      console.log('⚠️  OVERALL ASSESSMENT: NEEDS ATTENTION');
      console.log('   Duration defaults may not be working as expected');
      console.log('   🔧 Review and fix the identified issues');
    }

    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Test creating a new stream - duration should default to 0');
    console.log('2. Test editing an existing stream - empty duration should become 0');
    console.log('3. Monitor new streams to ensure they default to indefinite duration');
    console.log('4. Consider updating existing streams with duration limits to 0');

    console.log('\n🧪 QUICK TEST:');
    console.log('   Create a new stream without setting duration');
    console.log('   Expected: Stream should have duration = 0 (indefinite)');
    console.log('   Verify: Check database or stream details');
  }
}

// Run the verification
if (require.main === module) {
  const verifier = new DurationDefaultVerifier();
  verifier.runVerification().catch(console.error);
}

module.exports = DurationDefaultVerifier;
