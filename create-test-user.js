#!/usr/bin/env node

/**
 * Create Test User for PodLite Testing
 * Creates a test user with PodLite subscription and 0 streams
 */

const { db } = require('./db/database');
const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcrypt');

async function createTestUser() {
  try {
    console.log('🧪 Creating Test User for PodLite Testing');
    console.log('='.repeat(50));

    // Check if test user already exists
    const existingUser = await new Promise((resolve, reject) => {
      db.get('SELECT id, username FROM users WHERE username = ?', ['podlite_test_user'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    let userId;
    if (existingUser) {
      console.log(`✅ Test user already exists: ${existingUser.username}`);
      userId = existingUser.id;
    } else {
      // Create new test user
      userId = uuidv4();
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO users (id, username, email, password, plan_type, max_streaming_slots, max_storage_gb)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [userId, 'podlite_test_user', `test_${Date.now()}@example.com`, hashedPassword, 'Preview', 0, 0.015],
          function (err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log(`✅ Created test user: podlite_test_user (${userId})`);
    }

    // Get PodLite plan
    const podlitePlan = await new Promise((resolve, reject) => {
      db.get('SELECT id FROM subscription_plans WHERE name = ?', ['PodLite'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!podlitePlan) {
      throw new Error('PodLite plan not found!');
    }

    // Check if user already has active PodLite subscription
    const existingSubscription = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM user_subscriptions WHERE user_id = ? AND status = ? AND plan_id = ?',
        [userId, 'active', podlitePlan.id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingSubscription) {
      console.log('✅ Test user already has active PodLite subscription');
    } else {
      // Create PodLite subscription
      const subscriptionId = uuidv4();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 1); // 1 month from now

      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO user_subscriptions (id, user_id, plan_id, status, start_date, end_date)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [subscriptionId, userId, podlitePlan.id, 'active', new Date().toISOString(), endDate.toISOString()],
          function (err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log('✅ Created PodLite subscription for test user');
    }

    // Remove any existing streams for clean testing
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [userId], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Cleaned up any existing streams for test user');

    // Verify the setup
    const Subscription = require('./models/Subscription');
    const quotaCheck = await Subscription.checkStreamingSlotLimit(userId);

    console.log('\n📊 Test User Setup Verification:');
    console.log(`User ID: ${userId}`);
    console.log(`Username: podlite_test_user`);
    console.log(`Max Slots: ${quotaCheck.maxSlots}`);
    console.log(`Current Slots: ${quotaCheck.currentSlots}`);
    console.log(`Has Limit: ${quotaCheck.hasLimit}`);
    console.log(`Can Create Stream: ${!quotaCheck.hasLimit ? '✅ YES' : '❌ NO'}`);

    if (!quotaCheck.hasLimit && quotaCheck.maxSlots === 1 && quotaCheck.currentSlots === 0) {
      console.log('\n🎉 Test user setup successful!');
      console.log('User can be used to test PodLite streaming with 0 streams.');
    } else {
      console.log('\n⚠️  Test user setup may have issues:');
      console.log(`Expected: maxSlots=1, currentSlots=0, hasLimit=false`);
      console.log(`Actual: maxSlots=${quotaCheck.maxSlots}, currentSlots=${quotaCheck.currentSlots}, hasLimit=${quotaCheck.hasLimit}`);
    }

    console.log('\n' + '='.repeat(50));

  } catch (error) {
    console.error('❌ Failed to create test user:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Creation interrupted by user');
  process.exit(1);
});

// Run the creation
createTestUser().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
