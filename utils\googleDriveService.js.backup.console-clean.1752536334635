const fs = require('fs-extra');
const path = require('path');
const { google } = require('googleapis');
const { paths, getUniqueFilename } = require('./storage');
const crypto = require('crypto');

// Configuration for chunked downloads
const CHUNK_SIZE = 50 * 1024 * 1024; // 50MB chunks (safe for Cloudflare)
const CHUNKED_DOWNLOAD_THRESHOLD = 100 * 1024 * 1024; // 100MB threshold

class ChunkedGoogleDriveDownloader {
  constructor() {
    this.activeDownloads = new Map();
    this.chunksDir = path.join(paths.videos, 'gdrive_chunks');

    // Ensure chunks directory exists
    fs.ensureDirSync(this.chunksDir);
  }

  /**
   * Initialize a chunked download session
   */
  async initializeDownload(apiKey, fileId, fileMetadata) {
    try {
      const downloadId = crypto.randomUUID();
      const fileSize = parseInt(fileMetadata.size, 10) || 0;
      const totalChunks = Math.ceil(fileSize / CHUNK_SIZE);

      const originalFilename = fileMetadata.name;
      const uniqueFilename = getUniqueFilename(originalFilename);
      const finalFilePath = path.join(paths.videos, uniqueFilename);

      // Store download metadata
      const downloadMetadata = {
        downloadId,
        apiKey,
        fileId,
        originalFilename,
        uniqueFilename,
        finalFilePath,
        fileSize,
        totalChunks,
        downloadedChunks: new Set(),
        createdAt: new Date(),
        lastActivity: new Date(),
        mimeType: fileMetadata.mimeType
      };

      this.activeDownloads.set(downloadId, downloadMetadata);

      console.log(`📦 Initialized chunked download: ${originalFilename} (${totalChunks} chunks)`);

      return {
        downloadId,
        totalChunks,
        chunkSize: CHUNK_SIZE,
        finalFilename: uniqueFilename
      };
    } catch (error) {
      throw new Error(`Failed to initialize chunked download: ${error.message}`);
    }
  }

  /**
   * Download a specific chunk
   */
  async downloadChunk(downloadId, chunkIndex) {
    try {
      const downloadMetadata = this.activeDownloads.get(downloadId);
      if (!downloadMetadata) {
        throw new Error('Download session not found or expired');
      }

      // Check if chunk already downloaded
      if (downloadMetadata.downloadedChunks.has(chunkIndex)) {
        console.log(`⚠️ Chunk ${chunkIndex} already downloaded, skipping`);
        return {
          chunkIndex,
          downloaded: downloadMetadata.downloadedChunks.size,
          total: downloadMetadata.totalChunks,
          isComplete: downloadMetadata.downloadedChunks.size === downloadMetadata.totalChunks
        };
      }

      const drive = createDriveService(downloadMetadata.apiKey);

      // Calculate byte range for this chunk
      const start = chunkIndex * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE - 1, downloadMetadata.fileSize - 1);

      console.log(`📥 Downloading chunk ${chunkIndex + 1}/${downloadMetadata.totalChunks} (bytes ${start}-${end})`);

      // Download chunk with range header
      const response = await drive.files.get({
        fileId: downloadMetadata.fileId,
        alt: 'media'
      }, {
        responseType: 'stream',
        headers: {
          'Range': `bytes=${start}-${end}`
        }
      });

      // Save chunk to temporary file
      const chunkPath = path.join(this.chunksDir, `${downloadId}_${chunkIndex}.chunk`);
      const writeStream = fs.createWriteStream(chunkPath);

      return new Promise((resolve, reject) => {
        let chunkSize = 0;

        response.data
          .on('data', (chunk) => {
            chunkSize += chunk.length;
          })
          .on('end', () => {
            // Mark chunk as downloaded
            downloadMetadata.downloadedChunks.add(chunkIndex);
            downloadMetadata.lastActivity = new Date();

            const result = {
              chunkIndex,
              downloaded: downloadMetadata.downloadedChunks.size,
              total: downloadMetadata.totalChunks,
              isComplete: downloadMetadata.downloadedChunks.size === downloadMetadata.totalChunks,
              chunkSize
            };

            console.log(`✅ Chunk ${chunkIndex + 1} downloaded successfully (${chunkSize} bytes)`);
            resolve(result);
          })
          .on('error', (error) => {
            // Clean up failed chunk
            if (fs.existsSync(chunkPath)) {
              fs.removeSync(chunkPath);
            }
            reject(new Error(`Failed to download chunk ${chunkIndex}: ${error.message}`));
          })
          .pipe(writeStream);

        writeStream.on('error', (error) => {
          if (fs.existsSync(chunkPath)) {
            fs.removeSync(chunkPath);
          }
          reject(new Error(`Failed to write chunk ${chunkIndex}: ${error.message}`));
        });
      });
    } catch (error) {
      throw new Error(`Chunk download failed: ${error.message}`);
    }
  }

  /**
   * Merge all chunks into final file
   */
  async finalizeDownload(downloadId) {
    try {
      const downloadMetadata = this.activeDownloads.get(downloadId);
      if (!downloadMetadata) {
        throw new Error('Download session not found or expired');
      }

      // Verify all chunks are downloaded
      if (downloadMetadata.downloadedChunks.size !== downloadMetadata.totalChunks) {
        throw new Error(`Missing chunks. Downloaded: ${downloadMetadata.downloadedChunks.size}, Expected: ${downloadMetadata.totalChunks}`);
      }

      console.log(`🔄 Merging ${downloadMetadata.totalChunks} chunks into final file...`);

      // Create write stream for final file
      const writeStream = fs.createWriteStream(downloadMetadata.finalFilePath);

      try {
        // Merge chunks in order
        for (let i = 0; i < downloadMetadata.totalChunks; i++) {
          const chunkPath = path.join(this.chunksDir, `${downloadId}_${i}.chunk`);

          if (!await fs.pathExists(chunkPath)) {
            throw new Error(`Chunk ${i} file not found`);
          }

          // Read and append chunk to final file
          const chunkData = await fs.readFile(chunkPath);
          writeStream.write(chunkData);
        }

        // Close the write stream
        await new Promise((resolve, reject) => {
          writeStream.end((error) => {
            if (error) reject(error);
            else resolve();
          });
        });

        // Verify final file size
        const finalStats = await fs.stat(downloadMetadata.finalFilePath);
        if (finalStats.size !== downloadMetadata.fileSize) {
          await fs.remove(downloadMetadata.finalFilePath);
          throw new Error(`File size mismatch. Expected: ${downloadMetadata.fileSize}, Got: ${finalStats.size}`);
        }

        // Clean up chunks and metadata
        await this.cleanupDownload(downloadId);

        console.log(`✅ Chunked download completed: ${downloadMetadata.originalFilename}`);

        return {
          filename: downloadMetadata.uniqueFilename,
          originalFilename: downloadMetadata.originalFilename,
          localFilePath: downloadMetadata.finalFilePath,
          mimeType: downloadMetadata.mimeType,
          fileSize: finalStats.size
        };
      } catch (error) {
        // Clean up partial final file if it exists
        if (await fs.pathExists(downloadMetadata.finalFilePath)) {
          await fs.remove(downloadMetadata.finalFilePath);
        }
        throw error;
      }
    } catch (error) {
      throw new Error(`Failed to finalize download: ${error.message}`);
    }
  }

  /**
   * Clean up download chunks and metadata
   */
  async cleanupDownload(downloadId) {
    try {
      const downloadMetadata = this.activeDownloads.get(downloadId);
      if (downloadMetadata) {
        // Remove all chunk files
        for (let i = 0; i < downloadMetadata.totalChunks; i++) {
          const chunkPath = path.join(this.chunksDir, `${downloadId}_${i}.chunk`);
          if (await fs.pathExists(chunkPath)) {
            await fs.remove(chunkPath);
          }
        }
      }

      // Remove metadata
      this.activeDownloads.delete(downloadId);
    } catch (error) {
      console.error(`Error cleaning up download ${downloadId}:`, error);
    }
  }

  /**
   * Get download status
   */
  getDownloadStatus(downloadId) {
    const downloadMetadata = this.activeDownloads.get(downloadId);
    if (!downloadMetadata) {
      return null;
    }

    return {
      downloadId,
      downloaded: downloadMetadata.downloadedChunks.size,
      total: downloadMetadata.totalChunks,
      isComplete: downloadMetadata.downloadedChunks.size === downloadMetadata.totalChunks,
      createdAt: downloadMetadata.createdAt,
      lastActivity: downloadMetadata.lastActivity,
      filename: downloadMetadata.originalFilename
    };
  }
}

// Create singleton instance
const chunkedDownloader = new ChunkedGoogleDriveDownloader();

function createDriveService(apiKey) {
  return google.drive({
    version: 'v3',
    auth: apiKey
  });
}

function extractFileId(driveUrl) {

  let match = driveUrl.match(/\/file\/d\/([^\/]+)/);
  if (match) return match[1];

  match = driveUrl.match(/\?id=([^&]+)/);
  if (match) return match[1];

  match = driveUrl.match(/\/d\/([^\/]+)/);
  if (match) return match[1];

  if (/^[a-zA-Z0-9_-]{25,}$/.test(driveUrl.trim())) {
    return driveUrl.trim();
  }

  throw new Error('Invalid Google Drive URL format');
}

/**
 * Enhanced download function with chunked download support
 */
async function downloadFileChunked(apiKey, fileId, progressCallback = null) {
  const drive = createDriveService(apiKey);

  try {
    console.log(`📥 Starting Google Drive download for file ID: ${fileId}`);

    // First, get file metadata
    let fileMetadata;
    try {
      fileMetadata = await drive.files.get({
        fileId: fileId,
        fields: 'name,mimeType,size,permissions'
      });
    } catch (metadataError) {
      console.error(`[Google Drive] Failed to get file metadata:`, metadataError.message);

      // Provide specific error messages for metadata access
      if (metadataError.code === 403) {
        throw new Error('Access denied. The file may be private or your API key lacks permission to access it. Make sure the file is shared publicly or with your API key.');
      } else if (metadataError.code === 404) {
        throw new Error('File not found. Please check if the Google Drive URL is correct and the file still exists.');
      } else if (metadataError.code === 401) {
        throw new Error('Authentication failed. Please check your Google Drive API key configuration.');
      } else {
        throw new Error(`Failed to access file metadata: ${metadataError.message}`);
      }
    }

    const fileSize = parseInt(fileMetadata.data.size, 10) || 0;
    console.log(`📊 File size: ${(fileSize / 1024 / 1024).toFixed(2)} MB`);

    // Check if file is a supported video format
    const supportedVideoTypes = [
      'video/mp4',
      'video/quicktime'
    ];

    const isVideoFile = supportedVideoTypes.includes(fileMetadata.data.mimeType) ||
                       fileMetadata.data.name.toLowerCase().match(/\.(mp4|mov)$/);

    if (!isVideoFile) {
      throw new Error(`Only MP4 and MOV video files are supported for optimal performance. File type: ${fileMetadata.data.mimeType}, Name: ${fileMetadata.data.name}`);
    }

    // Determine download method based on file size
    if (fileSize > CHUNKED_DOWNLOAD_THRESHOLD) {
      console.log(`🔄 Using chunked download for large file (${(fileSize / 1024 / 1024).toFixed(2)} MB)`);
      return await downloadFileInChunks(apiKey, fileId, fileMetadata.data, progressCallback);
    } else {
      console.log(`🔄 Using regular download for small file (${(fileSize / 1024 / 1024).toFixed(2)} MB)`);
      return await downloadFileRegular(apiKey, fileId, fileMetadata.data, progressCallback);
    }
  } catch (error) {
    console.error('Error downloading file from Google Drive:', error);
    throw error;
  }
}

/**
 * Chunked download implementation for large files
 */
async function downloadFileInChunks(apiKey, fileId, fileMetadata, progressCallback = null) {
  try {
    // Initialize chunked download
    const downloadInfo = await chunkedDownloader.initializeDownload(apiKey, fileId, fileMetadata);
    const { downloadId, totalChunks } = downloadInfo;

    console.log(`📦 Initialized chunked download: ${totalChunks} chunks`);

    // Download chunks sequentially
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount <= maxRetries) {
        try {
          const result = await chunkedDownloader.downloadChunk(downloadId, chunkIndex);

          // Update progress callback
          if (progressCallback) {
            const progress = Math.round((result.downloaded / result.total) * 100);
            progressCallback({
              id: fileId,
              filename: fileMetadata.name,
              progress: progress,
              chunk: chunkIndex + 1,
              totalChunks: totalChunks
            });
          }

          break; // Success, exit retry loop
        } catch (error) {
          retryCount++;
          if (retryCount > maxRetries) {
            // Clean up on final failure
            await chunkedDownloader.cleanupDownload(downloadId);
            throw new Error(`Failed to download chunk ${chunkIndex + 1} after ${maxRetries} retries: ${error.message}`);
          }

          console.warn(`⚠️ Chunk ${chunkIndex + 1} failed, retrying (${retryCount}/${maxRetries}): ${error.message}`);
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }
    }

    // Finalize download (merge chunks)
    console.log(`🔄 Finalizing chunked download...`);
    if (progressCallback) {
      progressCallback({
        id: fileId,
        filename: fileMetadata.name,
        progress: 100,
        status: 'finalizing'
      });
    }

    const result = await chunkedDownloader.finalizeDownload(downloadId);

    console.log(`✅ Chunked download completed: ${result.originalFilename}`);
    return result;
  } catch (error) {
    throw new Error(`Chunked download failed: ${error.message}`);
  }
}

/**
 * Regular download implementation for small files (backward compatibility)
 */
async function downloadFileRegular(apiKey, fileId, fileMetadata, progressCallback = null) {
  try {
    const originalFilename = fileMetadata.name;
    const uniqueFilename = getUniqueFilename(originalFilename);
    const localFilePath = path.join(paths.videos, uniqueFilename);

    // Ensure the directory exists
    const dir = path.dirname(localFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const drive = createDriveService(apiKey);
    const dest = fs.createWriteStream(localFilePath);

    // Try to download the file
    let response;
    try {
      response = await drive.files.get(
        {
          fileId: fileId,
          alt: 'media'
        },
        { responseType: 'stream' }
      );
    } catch (downloadError) {
      console.error(`[Google Drive] Failed to start download:`, downloadError.message);

      // Provide more specific error messages
      if (downloadError.code === 403) {
        throw new Error('Access denied. The file may be private or your API key lacks permission to access it.');
      } else if (downloadError.code === 404) {
        throw new Error('File not found. The file may have been deleted or the link is incorrect.');
      } else if (downloadError.code === 401) {
        throw new Error('Authentication failed. Please check your Google Drive API key.');
      } else {
        throw new Error(`Download failed: ${downloadError.message}`);
      }
    }

    const fileSize = parseInt(fileMetadata.size, 10) || 0;
    let downloaded = 0;

    // Handle case where file size is not available
    if (fileSize === 0) {
      console.warn(`[Google Drive] File size is 0 or not available - progress tracking will be limited`);
    }

    return new Promise((resolve, reject) => {
      let streamClosed = false;

      // Cleanup function to properly close streams and remove files
      const cleanup = () => {
        if (!streamClosed) {
          streamClosed = true;
          try {
            // Properly close the write stream
            if (dest && !dest.destroyed) {
              dest.destroy();
            }
          } catch (e) {
            // Ignore cleanup errors
            console.warn('Warning during stream cleanup:', e.message);
          }
        }
      };

      response.data
        .on('data', chunk => {
          downloaded += chunk.length;
          if (progressCallback) {
            // Calculate progress, handle case where fileSize is 0 or unknown
            let progress = 0;
            if (fileSize > 0) {
              progress = Math.round((downloaded / fileSize) * 100);
              progress = Math.min(progress, 100); // Cap at 100%
            } else {
              // If file size is unknown, show progress based on downloaded bytes
              progress = Math.min(Math.round(downloaded / (1024 * 1024)), 99); // Show MB downloaded, cap at 99%
            }

            progressCallback({
              id: fileId,
              filename: originalFilename,
              progress: progress
            });
          }
        })
        .on('end', () => {
          // Don't cleanup immediately - wait for file verification
          // console.log(`[Google Drive] Stream ended for ${originalFilename}`); // Removed for production
          // Add a small delay to ensure file is fully written to disk
          setTimeout(() => {
            try {
              // Verify file was actually written and has correct size
              if (!fs.existsSync(localFilePath)) {
                cleanup();
                reject(new Error(`File was not created: ${localFilePath}`));
                return;
              }

              const actualFileSize = fs.statSync(localFilePath).size;
              // console.log(`Downloaded file ${originalFilename} from Google Drive`); // Removed for production
              // console.log(`Expected size: ${fileSize} bytes, Actual size: ${actualFileSize} bytes`); // Removed for production
              // Allow some tolerance for size differences (metadata might not be exact)
              if (actualFileSize === 0) {
                cleanup();
                // Clean up the empty file
                try {
                  fs.unlinkSync(localFilePath);
                } catch (cleanupError) {
                  console.error('Error cleaning up empty file:', cleanupError.message);
                }
                reject(new Error(`Downloaded file is empty: ${localFilePath}`));
                return;
              }

              if (fileSize > 0 && Math.abs(actualFileSize - fileSize) > fileSize * 0.1) {
                console.warn(`File size mismatch - Expected: ${fileSize}, Got: ${actualFileSize}`);
              }

              // File verification successful - now cleanup streams
              cleanup();

              resolve({
                filename: uniqueFilename,
                originalFilename: originalFilename,
                localFilePath: localFilePath,
                mimeType: fileMetadata.mimeType,
                fileSize: actualFileSize // Use actual file size
              });
            } catch (verificationError) {
              cleanup();
              // Clean up the file if verification fails
              try {
                if (fs.existsSync(localFilePath)) {
                  fs.unlinkSync(localFilePath);
                }
              } catch (cleanupError) {
                console.error('Error cleaning up file after verification failure:', cleanupError.message);
              }
              reject(new Error(`File verification failed: ${verificationError.message}`));
            }
          }, 100); // 100ms delay to ensure file is fully written
        })
        .on('error', err => {
          cleanup();
          // Safe file cleanup with error handling
          try {
            if (fs.existsSync(localFilePath)) {
              fs.unlinkSync(localFilePath);
            }
          } catch (unlinkError) {
            console.error('Error cleaning up file after download error:', unlinkError.message);
          }
          reject(err);
        })
        .pipe(dest);

      // Handle write stream errors separately
      dest.on('error', (err) => {
        cleanup();
        try {
          if (fs.existsSync(localFilePath)) {
            fs.unlinkSync(localFilePath);
          }
        } catch (unlinkError) {
          console.error('Error cleaning up file after write error:', unlinkError.message);
        }
        reject(err);
      });
    });
  } catch (error) {
    console.error('Error downloading file from Google Drive:', error);
    throw error;
  }
}

// Backward compatibility - use the new chunked download function
async function downloadFile(apiKey, fileId, progressCallback = null) {
  return await downloadFileChunked(apiKey, fileId, progressCallback);
}

module.exports = {
  createDriveService,
  extractFileId,
  downloadFile,
  downloadFileChunked,
  downloadFileRegular,
  chunkedDownloader,
  CHUNK_SIZE,
  CHUNKED_DOWNLOAD_THRESHOLD
};