const { db } = require('./database');

class DatabaseOptimizer {
  constructor() {
    this.indexesCreated = false;
    this.pragmaSet = false;
  }

  // Create database indexes for better query performance
  async createIndexes() {
    if (this.indexesCreated) return;

    const indexes = [
      // User indexes
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_plan_type ON users(plan_type)',
      'CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)',

      // Stream indexes
      'CREATE INDEX IF NOT EXISTS idx_streams_user_id ON streams(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_streams_status ON streams(status)',
      'CREATE INDEX IF NOT EXISTS idx_streams_schedule_time ON streams(schedule_time)',
      'CREATE INDEX IF NOT EXISTS idx_streams_created_at ON streams(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_streams_user_status ON streams(user_id, status)',
      'CREATE INDEX IF NOT EXISTS idx_streams_platform ON streams(platform)',

      // Video indexes
      'CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_videos_upload_date ON videos(upload_date)',
      'CREATE INDEX IF NOT EXISTS idx_videos_format ON videos(format)',
      'CREATE INDEX IF NOT EXISTS idx_videos_user_upload ON videos(user_id, upload_date)',

      // Stream history indexes
      'CREATE INDEX IF NOT EXISTS idx_stream_history_user_id ON stream_history(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_stream_history_start_time ON stream_history(start_time)',
      'CREATE INDEX IF NOT EXISTS idx_stream_history_stream_id ON stream_history(stream_id)',

      // User subscription indexes (corrected table name)
      'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan_id ON user_subscriptions(plan_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status)',
      'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_end_date ON user_subscriptions(end_date)',

      // Subscription plans indexes
      'CREATE INDEX IF NOT EXISTS idx_subscription_plans_name ON subscription_plans(name)',
      'CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON subscription_plans(is_active)',

      // Role permissions indexes
      'CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role)',
      'CREATE INDEX IF NOT EXISTS idx_role_permissions_permission ON role_permissions(permission)',

      // Composite indexes for common queries
      'CREATE INDEX IF NOT EXISTS idx_streams_user_status_schedule ON streams(user_id, status, schedule_time)',
      'CREATE INDEX IF NOT EXISTS idx_videos_user_date ON videos(user_id, upload_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_stream_history_user_time ON stream_history(user_id, start_time DESC)'
    ];

    try {
      console.log('[DB Optimizer] Creating database indexes...');

      // Get list of existing tables to avoid errors on non-existent tables
      const existingTables = await this.getExistingTables();

      for (const indexSQL of indexes) {
        try {
          // Extract table name from the index SQL to check if table exists
          const tableMatch = indexSQL.match(/ON\s+(\w+)\s*\(/);
          const tableName = tableMatch ? tableMatch[1] : null;

          if (tableName && existingTables.includes(tableName)) {
            await this.runSQL(indexSQL);
          } else if (tableName) {
            console.log(`[DB Optimizer] Skipping index for non-existent table: ${tableName}`);
          } else {
            // If we can't parse table name, try to create the index anyway
            await this.runSQL(indexSQL);
          }
        } catch (indexError) {
          console.warn(`[DB Optimizer] Failed to create index: ${indexSQL.substring(0, 50)}...`, indexError.message);
          // Continue with other indexes instead of failing completely
        }
      }

      this.indexesCreated = true;
      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating database indexes:', error);
      // Don't throw error to prevent app startup failure
      console.log('⚠️  Continuing without some database optimizations...');
    }
  }

  // Helper method to get existing tables
  async getExistingTables() {
    try {
      const tables = await this.getAllSQL(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);
      return tables.map(table => table.name);
    } catch (error) {
      console.error('Error getting existing tables:', error);
      return [];
    }
  }

  // Set SQLite performance pragmas
  async setPragmas() {
    if (this.pragmaSet) return;

    const pragmas = [
      'PRAGMA journal_mode = WAL',           // Write-Ahead Logging for better concurrency
      'PRAGMA synchronous = NORMAL',         // Balance between safety and performance
      'PRAGMA cache_size = 10000',           // Increase cache size (10MB)
      'PRAGMA temp_store = MEMORY',          // Store temp tables in memory
      'PRAGMA mmap_size = 268435456',        // Memory-mapped I/O (256MB)
      'PRAGMA optimize'                      // Optimize query planner
    ];

    try {
      console.log('[DB Optimizer] Setting SQLite performance pragmas...');

      for (const pragma of pragmas) {
        await this.runSQL(pragma);
      }

      this.pragmaSet = true;
      console.log('✅ SQLite pragmas set successfully');
    } catch (error) {
      console.error('❌ Error setting SQLite pragmas:', error);
      throw error;
    }
  }

  // Helper method to run SQL with promise
  runSQL(sql, params = []) {
    return new Promise((resolve, reject) => {
      db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this);
        }
      });
    });
  }

  // Create enhanced indexes for stream operations
  async createStreamOptimizedIndexes() {
    if (this.streamIndexesCreated) return;

    // Get existing tables first
    const existingTables = await this.getExistingTables();
    console.log('📋 Existing tables:', existingTables);

    const streamIndexes = [
      // Stream-specific indexes for better performance
      { sql: 'CREATE INDEX IF NOT EXISTS idx_streams_stream_key ON streams(stream_key)', table: 'streams' },
      { sql: 'CREATE INDEX IF NOT EXISTS idx_streams_user_stream_key ON streams(user_id, stream_key)', table: 'streams' },
      { sql: 'CREATE INDEX IF NOT EXISTS idx_streams_status_updated ON streams(status, status_updated_at)', table: 'streams' },
      { sql: 'CREATE INDEX IF NOT EXISTS idx_streams_schedule_time ON streams(schedule_time)', table: 'streams' },

      // Subscription-related indexes for quota checks
      { sql: 'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_end_date ON user_subscriptions(end_date)', table: 'user_subscriptions' },
      { sql: 'CREATE INDEX IF NOT EXISTS idx_user_subscriptions_active ON user_subscriptions(user_id, status, end_date)', table: 'user_subscriptions' },

      // User-related indexes for plan checks
      { sql: 'CREATE INDEX IF NOT EXISTS idx_users_plan_type ON users(plan_type)', table: 'users' },
      { sql: 'CREATE INDEX IF NOT EXISTS idx_users_streaming_slots ON users(max_streaming_slots)', table: 'users' },

      // Permission indexes for admin checks (only if table exists)
      { sql: 'CREATE INDEX IF NOT EXISTS idx_permissions_user_role ON permissions(user_id, role)', table: 'permissions' }
    ];

    try {
      let createdCount = 0;
      let skippedCount = 0;

      for (const indexInfo of streamIndexes) {
        if (existingTables.includes(indexInfo.table)) {
          await this.runSQL(indexInfo.sql);
          console.log(`✅ Created index for table: ${indexInfo.table}`);
          createdCount++;
        } else {
          console.log(`⚠️  Skipped index for missing table: ${indexInfo.table}`);
          skippedCount++;
        }
      }

      this.streamIndexesCreated = true;
      console.log(`✅ Stream-optimized database indexes completed: ${createdCount} created, ${skippedCount} skipped`);
    } catch (error) {
      console.error('❌ Error creating stream-optimized indexes:', error);
      // Don't throw error to prevent app startup failure
      console.log('⚠️  Continuing without some database optimizations...');
    }
  }

  // Analyze database performance
  async analyzePerformance() {
    try {
      const stats = await this.getSQL('PRAGMA compile_options');
      const cacheInfo = await this.getSQL('PRAGMA cache_size');
      const journalMode = await this.getSQL('PRAGMA journal_mode');
      const pageSize = await this.getSQL('PRAGMA page_size');

      return {
        cacheSize: cacheInfo,
        journalMode: journalMode,
        pageSize: pageSize,
        compileOptions: stats
      };
    } catch (error) {
      console.error('Error analyzing database performance:', error);
      return null;
    }
  }

  // Helper method to get SQL result with promise
  getSQL(sql, params = []) {
    return new Promise((resolve, reject) => {
      db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Get all SQL results with promise
  getAllSQL(sql, params = []) {
    return new Promise((resolve, reject) => {
      db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Vacuum database to reclaim space and optimize
  async vacuumDatabase() {
    try {
      console.log('[DB Optimizer] Running database vacuum...');
      await this.runSQL('VACUUM');
      console.log('✅ Database vacuum completed');
    } catch (error) {
      console.error('❌ Error running database vacuum:', error);
      throw error;
    }
  }

  // Analyze query performance
  async analyzeQuery(sql, params = []) {
    try {
      const explainSQL = `EXPLAIN QUERY PLAN ${sql}`;
      const queryPlan = await this.getAllSQL(explainSQL, params);
      return queryPlan;
    } catch (error) {
      console.error('Error analyzing query:', error);
      return null;
    }
  }

  // Get database statistics
  async getDatabaseStats() {
    try {
      const stats = await this.getAllSQL(`
        SELECT
          name as table_name,
          (SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=m.name) as index_count
        FROM sqlite_master m
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);

      const dbSize = await this.getSQL('PRAGMA page_count');
      const pageSize = await this.getSQL('PRAGMA page_size');

      return {
        tables: stats,
        totalPages: dbSize?.page_count || 0,
        pageSize: pageSize?.page_size || 0,
        estimatedSize: ((dbSize?.page_count || 0) * (pageSize?.page_size || 0)) / (1024 * 1024) // MB
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      return null;
    }
  }

  // Initialize all optimizations
  async initialize() {
    try {
      console.log('[DB Optimizer] Initializing database optimizations...');

      await this.setPragmas();
      await this.createIndexes();
      await this.createStreamOptimizedIndexes();

      console.log('✅ Database optimizations initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize database optimizations:', error);
      return false;
    }
  }

  // Initialize stream-specific optimizations
  async initializeStreamOptimizations() {
    try {
      await this.createStreamOptimizedIndexes();
      console.log('✅ Stream-specific database optimizations initialized');
      return true;
    } catch (error) {
      console.error('❌ Error initializing stream optimizations:', error);
      return false;
    }
  }

  // Maintenance tasks
  async runMaintenance() {
    try {
      console.log('[DB Optimizer] Running database maintenance...');

      // Analyze database
      await this.runSQL('ANALYZE');

      // Update statistics
      await this.runSQL('PRAGMA optimize');

      console.log('✅ Database maintenance completed');
    } catch (error) {
      console.error('❌ Error running database maintenance:', error);
      throw error;
    }
  }
}

// Create singleton instance
const dbOptimizer = new DatabaseOptimizer();

module.exports = dbOptimizer;
