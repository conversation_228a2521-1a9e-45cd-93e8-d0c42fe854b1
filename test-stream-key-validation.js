#!/usr/bin/env node

/**
 * Test script for stream key validation fixes
 * This script helps verify that the stream key validation is working correctly
 */

const { db } = require('./db/database');
const Stream = require('./models/Stream');

async function testStreamKeyValidation() {
  console.log('🧪 Testing Stream Key Validation Fixes\n');

  const testUserId = 'test-user-123';
  const testStreamKey = 'test-stream-key-' + Date.now();

  try {
    // Test 1: Check empty database
    console.log('Test 1: Checking stream key in empty state...');
    const isEmpty = await Stream.isStreamKeyInUse(testStreamKey, testUserId, null, {
      debugMode: true
    });
    console.log(`✅ Empty state check: ${isEmpty ? 'FAIL - should be false' : 'PASS'}\n`);

    // Test 2: Create a test stream
    console.log('Test 2: Creating test stream...');
    const testStream = await Stream.create({
      title: 'Test Stream for Validation',
      rtmp_url: 'rtmp://test.example.com/live',
      stream_key: testStreamKey,
      platform: 'Test',
      user_id: testUserId
    });
    console.log(`✅ Test stream created: ${testStream.id}\n`);

    // Test 3: Check if key is now in use
    console.log('Test 3: Checking if stream key is now in use...');
    const isInUse = await Stream.isStreamKeyInUse(testStreamKey, testUserId, null, {
      debugMode: true
    });
    console.log(`✅ In-use check: ${isInUse ? 'PASS' : 'FAIL - should be true'}\n`);

    // Test 4: Check with exclude ID (for updates)
    console.log('Test 4: Checking with exclude ID (update scenario)...');
    const isInUseExcluded = await Stream.isStreamKeyInUse(testStreamKey, testUserId, testStream.id, {
      debugMode: true
    });
    console.log(`✅ Exclude ID check: ${isInUseExcluded ? 'FAIL - should be false when excluded' : 'PASS'}\n`);

    // Test 5: Check different user
    console.log('Test 5: Checking with different user...');
    const differentUser = await Stream.isStreamKeyInUse(testStreamKey, 'different-user', null, {
      debugMode: true
    });
    console.log(`✅ Different user check: ${differentUser ? 'FAIL - should be false for different user' : 'PASS'}\n`);

    // Test 6: Check global mode
    console.log('Test 6: Checking with global mode...');
    const globalCheck = await Stream.isStreamKeyInUse(testStreamKey, 'different-user', null, {
      globalCheck: true,
      debugMode: true
    });
    console.log(`✅ Global check: ${globalCheck ? 'PASS' : 'FAIL - should be true in global mode'}\n`);

    // Test 7: Get detailed stream information
    console.log('Test 7: Getting detailed stream information...');
    const streamDetails = await Stream.getStreamsWithKey(testStreamKey, testUserId);
    console.log(`✅ Stream details found: ${streamDetails.length} streams`);
    streamDetails.forEach(s => {
      console.log(`   - ${s.title} (${s.status}) - ${s.id}`);
    });
    console.log();

    // Test 8: Update stream to error status and test inactive filtering
    console.log('Test 8: Testing inactive stream filtering...');
    await Stream.updateStatus(testStream.id, 'error', testUserId);
    
    const activeCheck = await Stream.isStreamKeyInUse(testStreamKey, testUserId, null, {
      includeInactive: false,
      debugMode: true
    });
    const inactiveCheck = await Stream.isStreamKeyInUse(testStreamKey, testUserId, null, {
      includeInactive: true,
      debugMode: true
    });
    
    console.log(`✅ Active only check: ${activeCheck ? 'FAIL - error status should not be active' : 'PASS'}`);
    console.log(`✅ Include inactive check: ${inactiveCheck ? 'PASS' : 'FAIL - should include error status'}\n`);

    // Test 9: Cleanup test
    console.log('Test 9: Testing cleanup functionality...');
    const cleanupResult = await Stream.cleanupOldStreams({
      olderThanDays: 0, // Clean everything
      statusesToClean: ['error'],
      dryRun: true
    });
    console.log(`✅ Cleanup dry run: Would delete ${cleanupResult.wouldDelete} streams\n`);

    // Cleanup: Delete test stream
    console.log('Cleanup: Removing test stream...');
    await Stream.delete(testStream.id, testUserId);
    console.log('✅ Test stream deleted\n');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Try to cleanup on error
    try {
      const streams = await Stream.getStreamsWithKey(testStreamKey, testUserId, {
        includeInactive: true
      });
      for (const stream of streams) {
        await Stream.delete(stream.id, testUserId);
        console.log(`🧹 Cleaned up test stream: ${stream.id}`);
      }
    } catch (cleanupError) {
      console.error('❌ Cleanup failed:', cleanupError);
    }
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testStreamKeyValidation()
    .then(() => {
      console.log('\n✅ Test script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testStreamKeyValidation };
