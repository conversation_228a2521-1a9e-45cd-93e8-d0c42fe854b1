const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const { getUniqueFilename, paths } = require('./storage');
const { CHUNK_SIZE } = require('../middleware/uploadMiddleware');
const jobQueue = require('./jobQueue');

class ChunkedUploadService {
  constructor() {
    this.activeUploads = new Map(); // Store upload metadata
    this.chunksDir = path.join(paths.videos, 'chunks');
    this.cleanupInterval = 60 * 60 * 1000; // 1 hour cleanup interval
    
    // Ensure chunks directory exists
    fs.ensureDirSync(this.chunksDir);
    
    // Start cleanup process
    this.startCleanupProcess();
  }

  /**
   * Initialize a new chunked upload
   */
  async initializeUpload(userId, filename, fileSize, totalChunks) {
    try {
      // Validate inputs
      if (!userId || !filename || !fileSize || !totalChunks) {
        throw new Error('Missing required parameters for upload initialization');
      }

      if (fileSize <= 0 || totalChunks <= 0) {
        throw new Error('Invalid file size or chunk count');
      }

      // Validate file extension
      const ext = path.extname(filename).toLowerCase();
      if (!['.mp4', '.mov'].includes(ext)) {
        throw new Error('Only MP4 and MOV files are allowed for optimal performance');
      }

      // Generate unique upload ID
      const uploadId = crypto.randomUUID();
      const finalFilename = getUniqueFilename(filename);
      
      // Calculate expected chunk size
      const expectedChunkSize = Math.ceil(fileSize / totalChunks);
      if (expectedChunkSize > CHUNK_SIZE + (1024 * 1024)) { // 1MB buffer
        throw new Error(`Chunk size too large. Maximum chunk size is ${CHUNK_SIZE / (1024 * 1024)}MB`);
      }

      // Store upload metadata
      const uploadMetadata = {
        uploadId,
        userId,
        originalFilename: filename,
        finalFilename,
        fileSize,
        totalChunks,
        receivedChunks: new Set(),
        createdAt: new Date(),
        lastActivity: new Date()
      };

      this.activeUploads.set(uploadId, uploadMetadata);

      return {
        uploadId,
        chunkSize: CHUNK_SIZE,
        finalFilename
      };
    } catch (error) {
      throw new Error(`Failed to initialize upload: ${error.message}`);
    }
  }

  /**
   * Process a received chunk
   */
  async processChunk(uploadId, chunkIndex, tempChunkPath) {
    try {
      console.log(`📦 Processing chunk ${chunkIndex} for upload ${uploadId}`);
      console.log(`📁 Temp chunk path: ${tempChunkPath}`);
      const uploadMetadata = this.activeUploads.get(uploadId);
      if (!uploadMetadata) {
        console.error(`❌ Upload session not found: ${uploadId}`);
        // Clean up orphaned chunk
        if (await fs.pathExists(tempChunkPath)) {
          await fs.remove(tempChunkPath);
        }
        throw new Error('Upload session not found or expired');
      }

      console.log(`📊 Upload metadata:`, {
        totalChunks: uploadMetadata.totalChunks,
        receivedChunks: uploadMetadata.receivedChunks.size,
        userId: uploadMetadata.userId
      });
      // Validate chunk index
      if (chunkIndex < 0 || chunkIndex >= uploadMetadata.totalChunks) {
        console.error(`❌ Invalid chunk index: ${chunkIndex}, expected 0-${uploadMetadata.totalChunks - 1}`);
        if (await fs.pathExists(tempChunkPath)) {
          await fs.remove(tempChunkPath);
        }
        throw new Error(`Invalid chunk index: ${chunkIndex}`);
      }

      // Check if chunk already received
      if (uploadMetadata.receivedChunks.has(chunkIndex)) {
        console.warn(`⚠️ Chunk ${chunkIndex} already received, skipping`);
        if (await fs.pathExists(tempChunkPath)) {
          await fs.remove(tempChunkPath);
        }
        throw new Error(`Chunk ${chunkIndex} already received`);
      }

      // Verify chunk file exists and has content
      if (!await fs.pathExists(tempChunkPath)) {
        console.error(`❌ Chunk file not found: ${tempChunkPath}`);
        throw new Error(`Chunk file not found: ${tempChunkPath}`);
      }

      const chunkStats = await fs.stat(tempChunkPath);
      console.log(`📏 Chunk ${chunkIndex} size: ${chunkStats.size} bytes`);
      if (chunkStats.size === 0) {
        await fs.remove(tempChunkPath);
        throw new Error(`Chunk ${chunkIndex} is empty`);
      }

      // Rename temp file to proper chunk filename
      const finalChunkPath = path.join(this.chunksDir, `${uploadId}_${chunkIndex}.chunk`);
      await fs.move(tempChunkPath, finalChunkPath);
      console.log(`📁 Renamed chunk to: ${finalChunkPath}`);
      // Mark chunk as received
      uploadMetadata.receivedChunks.add(chunkIndex);
      uploadMetadata.lastActivity = new Date();

      const result = {
        chunkIndex,
        received: uploadMetadata.receivedChunks.size,
        total: uploadMetadata.totalChunks,
        isComplete: uploadMetadata.receivedChunks.size === uploadMetadata.totalChunks
      };

      console.log(`✅ Chunk ${chunkIndex} processed successfully:`, result);
      return result;
    } catch (error) {
      console.error(`❌ Failed to process chunk ${chunkIndex}:`, error);
      // Clean up temp file if it still exists
      if (await fs.pathExists(tempChunkPath)) {
        await fs.remove(tempChunkPath);
      }
      throw new Error(`Failed to process chunk: ${error.message}`);
    }
  }

  /**
   * Finalize upload by merging all chunks
   */
  async finalizeUpload(uploadId) {
    return jobQueue.add(async () => {
      try {
        const uploadMetadata = this.activeUploads.get(uploadId);
        if (!uploadMetadata) {
          throw new Error('Upload session not found or expired');
        }

        // Verify all chunks are received
        if (uploadMetadata.receivedChunks.size !== uploadMetadata.totalChunks) {
          throw new Error(`Missing chunks. Received: ${uploadMetadata.receivedChunks.size}, Expected: ${uploadMetadata.totalChunks}`);
        }

        // Create final file path
        const finalFilePath = path.join(paths.videos, uploadMetadata.finalFilename);
        
        // Create write stream for final file
        const writeStream = fs.createWriteStream(finalFilePath);
        
        try {
          // Merge chunks in order
          for (let i = 0; i < uploadMetadata.totalChunks; i++) {
            const chunkPath = path.join(this.chunksDir, `${uploadId}_${i}.chunk`);
            
            if (!await fs.pathExists(chunkPath)) {
              throw new Error(`Chunk ${i} file not found`);
            }

            // Read and append chunk to final file
            const chunkData = await fs.readFile(chunkPath);
            writeStream.write(chunkData);
          }

          // Close the write stream
          await new Promise((resolve, reject) => {
            writeStream.end((error) => {
              if (error) reject(error);
              else resolve();
            });
          });

          // Verify final file size
          const finalStats = await fs.stat(finalFilePath);
          if (finalStats.size !== uploadMetadata.fileSize) {
            await fs.remove(finalFilePath);
            throw new Error(`File size mismatch. Expected: ${uploadMetadata.fileSize}, Got: ${finalStats.size}`);
          }

          // Clean up chunks and metadata
          await this.cleanupUpload(uploadId);

          return {
            filename: uploadMetadata.finalFilename,
            originalName: uploadMetadata.originalFilename,
            size: finalStats.size,
            path: finalFilePath
          };
        } catch (error) {
          // Clean up partial final file if it exists
          if (await fs.pathExists(finalFilePath)) {
            await fs.remove(finalFilePath);
          }
          throw error;
        }
      } catch (error) {
        throw new Error(`Failed to finalize upload: ${error.message}`);
      }
    });
  }

  /**
   * Clean up upload chunks and metadata
   */
  async cleanupUpload(uploadId) {
    try {
      const uploadMetadata = this.activeUploads.get(uploadId);
      if (uploadMetadata) {
        // Remove all chunk files
        for (let i = 0; i < uploadMetadata.totalChunks; i++) {
          const chunkPath = path.join(this.chunksDir, `${uploadId}_${i}.chunk`);
          if (await fs.pathExists(chunkPath)) {
            await fs.remove(chunkPath);
          }
        }
      }

      // Remove metadata
      this.activeUploads.delete(uploadId);
    } catch (error) {
      console.error(`Error cleaning up upload ${uploadId}:`, error);
    }
  }

  /**
   * Get upload status
   */
  getUploadStatus(uploadId) {
    const uploadMetadata = this.activeUploads.get(uploadId);
    if (!uploadMetadata) {
      return null;
    }

    return {
      uploadId,
      received: uploadMetadata.receivedChunks.size,
      total: uploadMetadata.totalChunks,
      isComplete: uploadMetadata.receivedChunks.size === uploadMetadata.totalChunks,
      createdAt: uploadMetadata.createdAt,
      lastActivity: uploadMetadata.lastActivity
    };
  }

  /**
   * Start cleanup process for expired uploads
   */
  startCleanupProcess() {
    setInterval(async () => {
      const now = new Date();
      const expiredUploads = [];

      // Find expired uploads (older than 2 hours)
      for (const [uploadId, metadata] of this.activeUploads.entries()) {
        const timeSinceLastActivity = now - metadata.lastActivity;
        if (timeSinceLastActivity > 2 * 60 * 60 * 1000) { // 2 hours
          expiredUploads.push(uploadId);
        }
      }

      // Clean up expired uploads
      for (const uploadId of expiredUploads) {
        await this.cleanupUpload(uploadId);
      }

      if (expiredUploads.length > 0) {
        console.log(`Cleaned up ${expiredUploads.length} expired chunked uploads`);
      }
    }, this.cleanupInterval);
  }

  /**
   * Fast chunk processing without cache overhead
   */
  async processChunkFast(uploadId, chunkIndex, tempChunkPath) {
    const startTime = Date.now();
    
    try {
      // Skip cache operations, direct memory lookup
      const uploadMetadata = this.activeUploads.get(uploadId);
      if (!uploadMetadata) {
        throw new Error('Upload session not found');
      }

      // Fast file operations
      const finalChunkPath = path.join(this.chunksDir, `${uploadId}_${chunkIndex}.chunk`);
      await fs.move(tempChunkPath, finalChunkPath, { overwrite: true });
      
      // Update in-memory metadata only
      uploadMetadata.receivedChunks.add(chunkIndex);
      uploadMetadata.lastActivity = new Date();
      
      const processingTime = Date.now() - startTime;
      console.log(`⚡ Fast chunk ${chunkIndex} processed in ${processingTime}ms`);
      return {
        success: true,
        chunkIndex,
        totalChunks: uploadMetadata.totalChunks,
        receivedChunks: uploadMetadata.receivedChunks.size,
        isComplete: uploadMetadata.receivedChunks.size === uploadMetadata.totalChunks,
        processingTime
      };
    } catch (error) {
      console.error(`❌ Fast chunk processing error:`, error);
      throw error;
    }
  }
}

// Create singleton instance
const chunkedUploadService = new ChunkedUploadService();

module.exports = chunkedUploadService;
