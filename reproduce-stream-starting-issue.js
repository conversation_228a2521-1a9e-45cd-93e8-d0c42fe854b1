#!/usr/bin/env node

/**
 * Reproduce Stream Starting Issue
 * Tests the exact scenario: create stream (works) → start stream (fails)
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');
const { v4: uuidv4 } = require('uuid');

async function reproduceIssue() {
  try {
    console.log('🔍 StreamOnPod: Reproducing Stream Starting Issue');
    console.log('='.repeat(60));
    console.log(`Time: ${new Date().toISOString()}\n`);

    // Use our test user
    const testUser = await new Promise((resolve, reject) => {
      db.get('SELECT id, username FROM users WHERE username = ?', ['podlite_test_user'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!testUser) {
      console.log('❌ Test user not found. Run create-test-user.js first.');
      process.exit(1);
    }

    console.log(`🧪 Testing with user: ${testUser.username} (${testUser.id})`);

    // Clean up any existing streams
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE user_id = ?', [testUser.id], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Cleaned up existing streams');

    // STEP 1: Test quota check with 0 streams (should pass)
    console.log('\n📋 STEP 1: Initial Quota Check (0 streams)');
    console.log('-'.repeat(50));

    let quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`Quota result: hasLimit=${quotaCheck.hasLimit}, slots=${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);

    if (quotaCheck.hasLimit) {
      console.log('❌ UNEXPECTED: User blocked with 0 streams!');
      return;
    } else {
      console.log('✅ EXPECTED: User can create streams (0 streams)');
    }

    // STEP 2: Create a stream (simulate successful creation)
    console.log('\n📋 STEP 2: Create Stream (Simulate Stream Creation)');
    console.log('-'.repeat(50));

    const streamId = uuidv4();
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO streams (id, user_id, title, status, rtmp_url, stream_key) VALUES (?, ?, ?, ?, ?, ?)',
        [streamId, testUser.id, 'Test Stream', 'offline', 'rtmp://test.example.com/live', 'test_key_123'],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`✅ Stream created: ${streamId} (status: offline)`);

    // STEP 3: Test quota check after creating stream (this is where the issue occurs)
    console.log('\n📋 STEP 3: Quota Check After Stream Creation');
    console.log('-'.repeat(50));

    quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`Quota result: hasLimit=${quotaCheck.hasLimit}, slots=${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);

    if (quotaCheck.hasLimit) {
      console.log('🐛 ISSUE REPRODUCED!');
      console.log('❌ User is blocked from starting their own stream!');
      console.log('❌ This is the exact issue reported by users');
      console.log('\n🔍 Analysis:');
      console.log(`   - User has 1 offline stream`);
      console.log(`   - PodLite plan allows 1 streaming slot`);
      console.log(`   - Current logic: 1 >= 1 = blocked`);
      console.log(`   - Expected logic: Only count 'live' streams for starting validation`);
    } else {
      console.log('✅ No issue found (unexpected)');
    }

    // STEP 4: Show the difference between creation and starting logic
    console.log('\n📋 STEP 4: Logic Analysis');
    console.log('-'.repeat(50));

    // Check what streams are being counted
    const streamCounts = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          status,
          COUNT(*) as count
        FROM streams 
        WHERE user_id = ? 
        GROUP BY status
      `, [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Stream counts by status:');
    for (const row of streamCounts) {
      console.log(`   ${row.status}: ${row.count}`);
    }

    // Show current query being used
    const totalStreams = await new Promise((resolve, reject) => {
      db.get(
        "SELECT COUNT(*) as count FROM streams WHERE user_id = ?",
        [testUser.id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    console.log(`\nCurrent quota logic counts: ${totalStreams} streams (ALL statuses)`);
    console.log('This includes offline streams that haven\'t been started yet!');

    // STEP 5: Demonstrate the fix
    console.log('\n📋 STEP 5: Proposed Fix Demonstration');
    console.log('-'.repeat(50));

    // Count only live streams (what the logic should be for starting)
    const liveStreams = await new Promise((resolve, reject) => {
      db.get(
        "SELECT COUNT(*) as count FROM streams WHERE user_id = ? AND status = 'live'",
        [testUser.id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    console.log(`Live streams: ${liveStreams}`);
    console.log(`Max slots: ${quotaCheck.maxSlots}`);
    
    const shouldBeBlocked = liveStreams >= quotaCheck.maxSlots;
    console.log(`Should be blocked for starting: ${shouldBeBlocked} (${liveStreams} >= ${quotaCheck.maxSlots})`);

    if (!shouldBeBlocked) {
      console.log('✅ With fixed logic: User SHOULD be able to start their offline stream');
    }

    // STEP 6: Test the complete workflow
    console.log('\n📋 STEP 6: Complete Workflow Test');
    console.log('-'.repeat(50));

    console.log('Simulating the user experience:');
    console.log('1. User creates stream → Stream status: offline');
    console.log('2. User clicks "Start Stream" → Quota middleware runs');
    console.log('3. Quota check counts offline stream as occupying slot');
    console.log('4. User gets "Streaming Limit Reached" error');
    console.log('5. User is confused because they only have 1 stream and plan allows 1');

    // Clean up
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM streams WHERE id = ?', [streamId], function (err) {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('\n✅ Test stream cleaned up');

    // STEP 7: Summary and recommendations
    console.log('\n📊 ISSUE SUMMARY');
    console.log('='.repeat(60));
    console.log('🐛 ISSUE CONFIRMED: Stream starting quota logic is incorrect');
    console.log('');
    console.log('ROOT CAUSE:');
    console.log('   The checkStreamingSlotLimit method counts ALL streams (including offline)');
    console.log('   When starting a stream, the stream is still offline, so it gets counted');
    console.log('   This causes users to hit their limit before actually going live');
    console.log('');
    console.log('IMPACT:');
    console.log('   - PodLite users (1 slot) cannot start their created streams');
    console.log('   - Users get confusing "limit reached" errors');
    console.log('   - Stream creation works but starting fails');
    console.log('');
    console.log('RECOMMENDED FIX:');
    console.log('   1. Modify quota logic to count only live streams for starting validation');
    console.log('   2. OR create separate validation for stream starting vs creation');
    console.log('   3. OR exclude the current stream being started from the count');

    console.log('\n' + '='.repeat(60));

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Test interrupted by user');
  process.exit(1);
});

// Run the reproduction test
reproduceIssue().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
