#!/usr/bin/env node

/**
 * Stream Termination Diagnostic Tool
 * Monitors and logs stream termination events to identify the 1-hour termination issue
 */

const fs = require('fs');
const path = require('path');
const { db } = require('./db/database');

console.log('🔍 Stream Termination Diagnostic Tool');
console.log('=====================================\n');

class StreamTerminationDiagnostic {
  constructor() {
    this.logFile = path.join(__dirname, 'logs', 'stream-termination-diagnostic.log');
    this.monitoringActive = false;
    this.streamData = new Map();
    this.terminationEvents = [];
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    
    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async startMonitoring() {
    console.log('🚀 Starting stream termination monitoring...\n');
    this.monitoringActive = true;

    // Monitor every 30 seconds
    const monitorInterval = setInterval(async () => {
      if (!this.monitoringActive) {
        clearInterval(monitorInterval);
        return;
      }

      try {
        await this.checkStreams();
      } catch (error) {
        this.log(`ERROR: Monitoring failed: ${error.message}`);
      }
    }, 30000); // 30 seconds

    // Initial check
    await this.checkStreams();
    
    this.log('Stream termination monitoring started');
    console.log('📊 Monitoring active streams every 30 seconds...');
    console.log('📝 Logs are being written to:', this.logFile);
    console.log('Press Ctrl+C to stop monitoring\n');
  }

  async checkStreams() {
    try {
      // Get all live streams from database
      const liveStreams = await this.getLiveStreams();
      const currentTime = new Date();
      
      for (const stream of liveStreams) {
        const streamId = stream.id;
        const startTime = new Date(stream.start_time);
        const runningTime = Math.floor((currentTime - startTime) / 1000 / 60); // minutes
        
        // Store/update stream data
        const previousData = this.streamData.get(streamId);
        const currentData = {
          id: streamId,
          title: stream.title,
          startTime: startTime,
          runningTime: runningTime,
          duration: stream.duration,
          lastChecked: currentTime,
          status: 'live'
        };

        this.streamData.set(streamId, currentData);

        // Check for potential termination risks
        await this.analyzeStream(currentData, previousData);
      }

      // Check for streams that disappeared (terminated)
      for (const [streamId, data] of this.streamData.entries()) {
        const stillExists = liveStreams.some(s => s.id === streamId);
        if (!stillExists && data.status === 'live') {
          // Stream was terminated
          const terminationEvent = {
            streamId: streamId,
            title: data.title,
            startTime: data.startTime,
            runningTime: data.runningTime,
            terminatedAt: currentTime,
            duration: data.duration
          };
          
          this.terminationEvents.push(terminationEvent);
          this.log(`TERMINATION: "${data.title}" (${streamId}) terminated after ${data.runningTime} minutes`);
          
          // Check if this was around 1 hour (55-65 minutes)
          if (data.runningTime >= 55 && data.runningTime <= 65) {
            this.log(`⚠️  CRITICAL: Stream terminated around 1-hour mark! Runtime: ${data.runningTime} minutes`);
            console.log(`🚨 CRITICAL: Stream "${data.title}" terminated at ${data.runningTime} minutes - likely the 1-hour bug!`);
          }
          
          // Update local data
          data.status = 'terminated';
        }
      }

    } catch (error) {
      this.log(`ERROR: Failed to check streams: ${error.message}`);
    }
  }

  async analyzeStream(currentData, previousData) {
    const { id, title, runningTime, duration } = currentData;

    // Check for approaching 1-hour mark
    if (runningTime >= 50 && runningTime <= 70) {
      if (!previousData || Math.floor(previousData.runningTime / 5) !== Math.floor(runningTime / 5)) {
        this.log(`APPROACHING_1H: "${title}" (${id}) at ${runningTime} minutes - monitoring for termination`);
        console.log(`⚠️  WATCH: "${title}" at ${runningTime} minutes (approaching 1-hour mark)`);
      }
    }

    // Check for duration-based termination risk
    if (duration && duration > 0) {
      const remainingTime = duration - runningTime;
      if (remainingTime <= 5 && remainingTime > 0) {
        this.log(`DURATION_WARNING: "${title}" (${id}) will auto-terminate in ${remainingTime} minutes due to duration limit`);
        console.log(`⏰ Duration Warning: "${title}" will terminate in ${remainingTime} minutes`);
      }
    }

    // Log milestone minutes
    if (previousData && Math.floor(runningTime / 10) > Math.floor(previousData.runningTime / 10)) {
      const milestone = Math.floor(runningTime / 10) * 10;
      this.log(`MILESTONE: "${title}" (${id}) reached ${milestone} minutes`);
      console.log(`📊 Milestone: "${title}" reached ${milestone} minutes`);
    }
  }

  async getLiveStreams() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT id, title, start_time, duration, user_id, status
        FROM streams 
        WHERE status = 'live'
        ORDER BY start_time DESC
      `, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  generateReport() {
    console.log('\n📊 TERMINATION ANALYSIS REPORT');
    console.log('===============================\n');

    if (this.terminationEvents.length === 0) {
      console.log('✅ No stream terminations detected during monitoring period');
      return;
    }

    console.log(`📈 Total Terminations: ${this.terminationEvents.length}\n`);

    // Analyze termination times
    const oneHourTerminations = this.terminationEvents.filter(e => e.runningTime >= 55 && e.runningTime <= 65);
    
    if (oneHourTerminations.length > 0) {
      console.log(`🚨 CRITICAL: ${oneHourTerminations.length} streams terminated around 1-hour mark:`);
      oneHourTerminations.forEach(event => {
        console.log(`   - "${event.title}": ${event.runningTime} minutes`);
      });
      console.log('');
    }

    // Show all terminations
    console.log('📋 All Termination Events:');
    this.terminationEvents.forEach((event, index) => {
      console.log(`${index + 1}. "${event.title}"`);
      console.log(`   Runtime: ${event.runningTime} minutes`);
      console.log(`   Started: ${event.startTime.toLocaleString()}`);
      console.log(`   Terminated: ${event.terminatedAt.toLocaleString()}`);
      console.log('');
    });
  }

  stopMonitoring() {
    this.monitoringActive = false;
    this.log('Stream termination monitoring stopped');
    this.generateReport();
  }
}

// Handle graceful shutdown
const diagnostic = new StreamTerminationDiagnostic();

process.on('SIGINT', () => {
  console.log('\n🛑 Stopping monitoring...');
  diagnostic.stopMonitoring();
  process.exit(0);
});

process.on('SIGTERM', () => {
  diagnostic.stopMonitoring();
  process.exit(0);
});

// Start monitoring
diagnostic.startMonitoring().catch(error => {
  console.error('Failed to start monitoring:', error);
  process.exit(1);
});
