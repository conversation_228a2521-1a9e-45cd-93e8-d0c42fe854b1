#!/usr/bin/env node

/**
 * Check for Streaming Issues
 * Identifies users who should be able to stream but are being incorrectly blocked
 */

const { db } = require('./db/database');
const Subscription = require('./models/Subscription');

class StreamingIssueChecker {
  constructor() {
    this.issues = [];
  }

  async run() {
    try {
      console.log('🔍 StreamOnPod: Streaming Issue Checker');
      console.log('='.repeat(50));
      console.log(`Time: ${new Date().toISOString()}\n`);

      // Check all users with active subscriptions
      await this.checkAllActiveSubscriptions();
      
      // Check for specific PodLite issues
      await this.checkPodLiteSpecifically();
      
      // Summary
      await this.printSummary();

    } catch (error) {
      console.error('❌ Check failed:', error.message);
      process.exit(1);
    }
  }

  async checkAllActiveSubscriptions() {
    console.log('📋 Checking All Active Subscriptions');
    console.log('-'.repeat(40));

    const users = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          u.id,
          u.username,
          sp.name as plan_name,
          sp.max_streaming_slots,
          us.status,
          us.end_date,
          (SELECT COUNT(*) FROM streams WHERE user_id = u.id) as total_streams,
          (SELECT COUNT(*) FROM streams WHERE user_id = u.id AND status = 'live') as live_streams
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active' AND sp.name != 'Preview'
        ORDER BY sp.name, u.username
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${users.length} users with active paid subscriptions:\n`);

    for (const user of users) {
      console.log(`👤 ${user.username} (${user.plan_name})`);
      console.log(`   Max slots: ${user.max_streaming_slots}`);
      console.log(`   Total streams: ${user.total_streams}`);
      console.log(`   Live streams: ${user.live_streams}`);
      
      // Check if user should be able to create streams
      const shouldBeAbleToStream = user.total_streams < user.max_streaming_slots;
      
      try {
        const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
        const isBlocked = quotaCheck.hasLimit;
        
        console.log(`   Quota check: ${isBlocked ? '🚫 BLOCKED' : '✅ ALLOWED'} (${quotaCheck.currentSlots}/${quotaCheck.maxSlots})`);
        
        // Identify potential issues
        if (shouldBeAbleToStream && isBlocked) {
          console.log(`   ⚠️  POTENTIAL ISSUE: User should be able to stream but is blocked!`);
          this.issues.push({
            type: 'incorrectly_blocked',
            user: user.username,
            userId: user.id,
            plan: user.plan_name,
            maxSlots: user.max_streaming_slots,
            currentStreams: user.total_streams,
            quotaResult: quotaCheck
          });
        } else if (!shouldBeAbleToStream && !isBlocked) {
          console.log(`   ⚠️  POTENTIAL ISSUE: User should be blocked but is allowed!`);
          this.issues.push({
            type: 'incorrectly_allowed',
            user: user.username,
            userId: user.id,
            plan: user.plan_name,
            maxSlots: user.max_streaming_slots,
            currentStreams: user.total_streams,
            quotaResult: quotaCheck
          });
        } else {
          console.log(`   ✅ Working correctly`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking quota: ${error.message}`);
        this.issues.push({
          type: 'quota_check_error',
          user: user.username,
          userId: user.id,
          error: error.message
        });
      }
      
      console.log('');
    }
  }

  async checkPodLiteSpecifically() {
    console.log('🎯 Specific PodLite Analysis');
    console.log('-'.repeat(40));

    const podliteUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          u.id,
          u.username,
          us.id as subscription_id,
          us.status,
          us.created_at,
          us.end_date,
          sp.max_streaming_slots,
          (SELECT COUNT(*) FROM streams WHERE user_id = u.id) as stream_count
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE sp.name = 'PodLite'
        ORDER BY us.created_at DESC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${podliteUsers.length} PodLite subscriptions:\n`);

    for (const user of podliteUsers) {
      console.log(`👤 ${user.username}`);
      console.log(`   Subscription: ${user.subscription_id}`);
      console.log(`   Status: ${user.status}`);
      console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
      console.log(`   Expires: ${user.end_date ? new Date(user.end_date).toLocaleDateString() : 'Never'}`);
      console.log(`   Streams: ${user.stream_count}/${user.max_streaming_slots}`);

      if (user.status === 'active') {
        // Test the specific scenario that was reported
        if (user.stream_count === 0) {
          console.log(`   🧪 Testing: User with 0 streams should be able to create 1 stream`);
          
          try {
            const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
            if (quotaCheck.hasLimit) {
              console.log(`   ❌ ISSUE FOUND: User with 0 streams is blocked from streaming!`);
              this.issues.push({
                type: 'podlite_zero_streams_blocked',
                user: user.username,
                userId: user.id,
                quotaResult: quotaCheck
              });
            } else {
              console.log(`   ✅ Correct: User can create streams`);
            }
          } catch (error) {
            console.log(`   ❌ Error testing quota: ${error.message}`);
          }
        }
      }
      
      console.log('');
    }
  }

  async printSummary() {
    console.log('📊 ISSUE SUMMARY');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0) {
      console.log('✅ No streaming issues found!');
      console.log('All users are correctly able to stream within their plan limits.');
    } else {
      console.log(`⚠️  Found ${this.issues.length} potential issues:\n`);
      
      for (let i = 0; i < this.issues.length; i++) {
        const issue = this.issues[i];
        console.log(`${i + 1}. ${issue.type.toUpperCase()}`);
        console.log(`   User: ${issue.user} (${issue.userId})`);
        
        if (issue.type === 'incorrectly_blocked') {
          console.log(`   Problem: User should be able to stream but is blocked`);
          console.log(`   Plan: ${issue.plan} (${issue.maxSlots} slots)`);
          console.log(`   Current streams: ${issue.currentStreams}`);
          console.log(`   Quota result: ${JSON.stringify(issue.quotaResult, null, 4)}`);
        } else if (issue.type === 'podlite_zero_streams_blocked') {
          console.log(`   Problem: PodLite user with 0 streams cannot create streams`);
          console.log(`   Quota result: ${JSON.stringify(issue.quotaResult, null, 4)}`);
        } else if (issue.error) {
          console.log(`   Error: ${issue.error}`);
        }
        
        console.log('');
      }
      
      console.log('RECOMMENDED ACTIONS:');
      console.log('1. Check for duplicate subscriptions');
      console.log('2. Verify subscription data integrity');
      console.log('3. Test quota logic with affected users');
      console.log('4. Check database for corruption issues');
    }
    
    console.log('='.repeat(50));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Check interrupted by user');
  process.exit(1);
});

// Run the check
const checker = new StreamingIssueChecker();
checker.run().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
