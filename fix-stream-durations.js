#!/usr/bin/env node

/**
 * Stream Duration Fix Script
 * Checks for and fixes streams with duration limits that might cause 1-hour terminations
 */

const { db } = require('./db/database');

console.log('🔧 Stream Duration Fix Script');
console.log('==============================\n');

async function checkStreamDurations() {
  return new Promise((resolve, reject) => {
    db.all(`
      SELECT id, title, duration, status, start_time, created_at
      FROM streams 
      WHERE duration IS NOT NULL AND duration > 0
      ORDER BY created_at DESC
      LIMIT 20
    `, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

async function removeStreamDuration(streamId) {
  return new Promise((resolve, reject) => {
    db.run(`
      UPDATE streams 
      SET duration = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [streamId], function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

async function main() {
  try {
    console.log('🔍 Checking for streams with duration limits...\n');
    
    const streamsWithDuration = await checkStreamDurations();
    
    if (streamsWithDuration.length === 0) {
      console.log('✅ No streams found with duration limits');
      return;
    }
    
    console.log(`📊 Found ${streamsWithDuration.length} streams with duration limits:\n`);
    
    // Display streams with duration limits
    streamsWithDuration.forEach((stream, index) => {
      const startTime = stream.start_time ? new Date(stream.start_time) : null;
      const runningTime = startTime ? Math.floor((new Date() - startTime) / 1000 / 60) : 0;
      
      console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
      console.log(`   Duration Limit: ${stream.duration} minutes`);
      console.log(`   Status: ${stream.status}`);
      if (startTime) {
        console.log(`   Running Time: ${runningTime} minutes`);
        console.log(`   Started: ${startTime.toLocaleString()}`);
      }
      console.log(`   Created: ${new Date(stream.created_at).toLocaleString()}`);
      console.log('');
    });
    
    // Check for streams with ~60 minute duration limits (potential cause of 1-hour termination)
    const suspiciousStreams = streamsWithDuration.filter(s => s.duration >= 55 && s.duration <= 65);
    
    if (suspiciousStreams.length > 0) {
      console.log(`🚨 CRITICAL: Found ${suspiciousStreams.length} streams with ~60 minute duration limits!`);
      console.log('These may be causing the 1-hour automatic termination issue:\n');
      
      suspiciousStreams.forEach(stream => {
        console.log(`⚠️  "${stream.title}" - Duration: ${stream.duration} minutes`);
      });
      
      console.log('\n🔧 RECOMMENDATION: Remove duration limits for 24/7 streaming');
      console.log('Run this script with --fix flag to automatically remove duration limits\n');
    }
    
    // Check if --fix flag is provided
    if (process.argv.includes('--fix')) {
      console.log('🔧 Fixing streams by removing duration limits...\n');
      
      let fixedCount = 0;
      for (const stream of streamsWithDuration) {
        try {
          const changes = await removeStreamDuration(stream.id);
          if (changes > 0) {
            console.log(`✅ Fixed: "${stream.title}" - removed ${stream.duration} minute duration limit`);
            fixedCount++;
          }
        } catch (error) {
          console.error(`❌ Error fixing "${stream.title}": ${error.message}`);
        }
      }
      
      console.log(`\n🎉 Fixed ${fixedCount} streams by removing duration limits`);
      console.log('These streams can now run indefinitely for 24/7 operation');
    } else {
      console.log('💡 To automatically remove all duration limits, run:');
      console.log('   node fix-stream-durations.js --fix');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Usage: node fix-stream-durations.js [--fix]');
  console.log('');
  console.log('Options:');
  console.log('  --fix    Automatically remove duration limits from all streams');
  console.log('  --help   Show this help message');
  process.exit(0);
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
