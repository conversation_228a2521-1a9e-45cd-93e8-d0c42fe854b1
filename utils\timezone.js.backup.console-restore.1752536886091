/**
 * Timezone Utilities for StreamOnPod
 * 
 * Provides robust timezone conversion and management using moment-timezone.
 */
const moment = require('moment-timezone');
const axios = require('axios');

/**
 * Get list of all available IANA timezones.
 * This provides a comprehensive list for users worldwide.
 * @returns {string[]} Array of IANA timezone names.
 */
function getTimezoneList() {
  return moment.tz.names();
}

/**
 * Get the user's timezone from their IP address using timeapi.io.
 * @param {string} ipAddress - The user's IP address.
 * @returns {string} The IANA timezone identifier.
 */
async function getTimezoneFromIp(ipAddress) {
  try {
    const response = await axios.get(`https://timeapi.io/api/time/current/ip?ipAddress=${ipAddress}`, {
      timeout: 2000 // 2 second timeout
    });
    return response.data.timeZone;
  } catch (error) {
    console.error('Error fetching timezone from IP:', error.message);
    return getDefaultTimezone(); // Fallback to default
  }
}

/**
 * Get the current UTC time from an external API to ensure accuracy.
 * @returns {string} The current time in UTC ISO 8601 format.
 */
async function getCurrentUTC() {
  try {
    // Use a shorter timeout to avoid blocking server startup
    const response = await axios.get('https://timeapi.io/api/time/current/zone?timeZone=UTC', {
      timeout: 2000 // 2 second timeout
    });
    return response.data.dateTime;
  } catch (error) {
    console.error('Error fetching current UTC time:', error.message);
    // Fallback to local system time if API fails
    return new Date().toISOString();
  }
}

/**
 * Get default timezone (Indonesia WIB)
 * @returns {string} Default timezone identifier
 */
function getDefaultTimezone() {
  return 'Asia/Jakarta';
}


/**
 * Validate IANA timezone identifier
 * @param {string} timezone - Timezone to validate
 * @returns {boolean} True if valid timezone
 */
function isValidTimezone(timezone) {
  return moment.tz.names().includes(timezone);
}

/**
 * Converts a given date-time string from a specific timezone to a UTC ISO string.
 * This is crucial for standardizing all schedule times in the database.
 * @param {string} dateTime - The date-time string to convert (e.g., "2025-12-31 23:59").
 * @param {string} timezone - The IANA timezone of the input string (e.g., "Asia/Jakarta").
 * @returns {string} The date-time in UTC ISO 8601 format.
 */
function convertToUTC(dateTime, timezone) {
  if (!isValidTimezone(timezone)) {
    // Fallback to default timezone if provided one is invalid
    timezone = getDefaultTimezone();
  }
  return moment.tz(dateTime, timezone).utc().toISOString();
}

/**
 * Converts a datetime-local string from a form input into a UTC ISO string.
 * This function correctly interprets the naive datetime string in the user's timezone.
 * @param {string} localDateTime - The datetime string from a datetime-local input (e.g., "2025-06-07T17:28").
 * @param {string} timezone - The user's IANA timezone (e.g., "Asia/Jakarta").
 * @returns {string} The date-time in UTC ISO 8601 format.
 */
function datetimeLocalToUTC(localDateTime, timezone) {
  if (!isValidTimezone(timezone)) {
    timezone = getDefaultTimezone();
  }
  const zonedTime = moment.tz(localDateTime, "YYYY-MM-DDTHH:mm", timezone);
  const result = zonedTime.utc().toISOString();
  
  // Debug logging
  // console.log(`[TIMEZONE DEBUG] Input: ${localDateTime}, Timezone: ${timezone}, Output: ${result}`); // Removed for production
  return result;
}

/**
 * Convert datetime-local input to UTC using specified timezone
 * @param {string} localDateTime - The datetime string from datetime-local input
 * @param {string} timezone - The timezone to use for conversion (default: Asia/Jakarta)
 * @returns {string} UTC ISO string
 */
function datetimeLocalToUTCFixed(localDateTime, timezone = 'Asia/Jakarta') {
  if (!localDateTime) return null;
  
  try {
    // Parse the datetime-local input in the specified timezone
    const zonedTime = moment.tz(localDateTime, "YYYY-MM-DDTHH:mm", timezone);
    const utcTime = zonedTime.utc();
    
    console.log(`[Timezone Conversion] Input: ${localDateTime} (${timezone})`);
    console.log(`[Timezone Conversion] Output: ${utcTime.toISOString()}`);
    
    return utcTime.toISOString();
  } catch (error) {
    console.error(`[Timezone Conversion Error] Input: ${localDateTime}, Timezone: ${timezone}`, error);
    return null;
  }
}

module.exports = {
  getTimezoneList,
  getDefaultTimezone,
  isValidTimezone,
  convertToUTC,
  datetimeLocalToUTC,
  getTimezoneFromIp,
  getCurrentUTC,
  datetimeLocalToUTCFixed,
};
