#!/usr/bin/env node

/**
 * Real-Time Stream Monitor
 * Monitors actual stream duration vs database duration to detect discrepancies
 */

const { db } = require('./db/database');
const fs = require('fs');
const path = require('path');

console.log('🔍 Real-Time Stream Duration Monitor');
console.log('====================================\n');

class RealTimeStreamMonitor {
  constructor() {
    this.logFile = path.join(__dirname, 'logs', 'real-time-stream-monitor.log');
    this.monitoringActive = false;
    this.streamStartTimes = new Map(); // Track when we first see streams as live
    this.discrepancies = [];
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    
    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async startMonitoring() {
    console.log('🚀 Starting real-time stream monitoring...\n');
    this.monitoringActive = true;

    // Monitor every 30 seconds for accurate tracking
    const monitorInterval = setInterval(async () => {
      if (!this.monitoringActive) {
        clearInterval(monitorInterval);
        return;
      }

      try {
        await this.checkStreams();
      } catch (error) {
        this.log(`ERROR: Monitoring failed: ${error.message}`);
      }
    }, 30000); // 30 seconds

    this.log('Real-time stream monitoring started');
    console.log('📊 Monitoring streams every 30 seconds...');
    console.log('📝 Logs are being written to:', this.logFile);
    console.log('Press Ctrl+C to stop monitoring\n');
  }

  async checkStreams() {
    try {
      const liveStreams = await this.getLiveStreams();
      const currentTime = new Date();
      
      for (const stream of liveStreams) {
        const streamId = stream.id;
        
        // Track when we first see this stream as live (our reference time)
        if (!this.streamStartTimes.has(streamId)) {
          this.streamStartTimes.set(streamId, currentTime);
          this.log(`STREAM_DETECTED: "${stream.title}" (${streamId}) first seen as live`);
          console.log(`🎬 New live stream detected: "${stream.title}"`);
        }
        
        // Calculate durations
        const ourStartTime = this.streamStartTimes.get(streamId);
        const dbStartTime = stream.start_time ? new Date(stream.start_time) : null;
        
        const ourDuration = Math.floor((currentTime - ourStartTime) / 1000 / 60); // minutes
        const dbDuration = dbStartTime ? Math.floor((currentTime - dbStartTime) / 1000 / 60) : 0;
        
        // Check for discrepancies
        const discrepancy = Math.abs(ourDuration - dbDuration);
        
        if (discrepancy > 5) { // More than 5 minutes difference
          const discrepancyEntry = {
            streamId: streamId,
            title: stream.title,
            ourDuration: ourDuration,
            dbDuration: dbDuration,
            discrepancy: discrepancy,
            ourStartTime: ourStartTime,
            dbStartTime: dbStartTime,
            detectedAt: currentTime
          };
          
          this.discrepancies.push(discrepancyEntry);
          
          this.log(`DISCREPANCY: "${stream.title}" - Our: ${ourDuration}min, DB: ${dbDuration}min, Diff: ${discrepancy}min`);
          console.log(`🚨 DURATION DISCREPANCY: "${stream.title}"`);
          console.log(`   Our calculation: ${ourDuration} minutes`);
          console.log(`   DB calculation: ${dbDuration} minutes`);
          console.log(`   Difference: ${discrepancy} minutes`);
          console.log(`   Our start time: ${ourStartTime.toISOString()}`);
          console.log(`   DB start time: ${dbStartTime ? dbStartTime.toISOString() : 'NULL'}`);
          console.log('');
        }
        
        // Log milestones
        if (ourDuration > 0 && ourDuration % 10 === 0) { // Every 10 minutes
          this.log(`MILESTONE: "${stream.title}" - Our: ${ourDuration}min, DB: ${dbDuration}min`);
          
          if (ourDuration === 60) { // 1 hour mark - critical for 59-minute bug
            console.log(`🎯 CRITICAL MILESTONE: "${stream.title}" reached 1 hour by our calculation`);
            console.log(`   Our time: ${ourDuration} minutes`);
            console.log(`   DB time: ${dbDuration} minutes`);
            console.log(`   This is the critical 59-minute termination point!`);
          }
        }
      }

      // Check for streams that disappeared (terminated)
      for (const [streamId, startTime] of this.streamStartTimes.entries()) {
        const stillExists = liveStreams.some(s => s.id === streamId);
        if (!stillExists) {
          const terminationTime = currentTime;
          const actualDuration = Math.floor((terminationTime - startTime) / 1000 / 60);
          
          this.log(`TERMINATION: Stream ${streamId} terminated after ${actualDuration} minutes of actual runtime`);
          console.log(`🚨 STREAM TERMINATED: ${streamId}`);
          console.log(`   Actual runtime: ${actualDuration} minutes`);
          console.log(`   Started (our time): ${startTime.toISOString()}`);
          console.log(`   Terminated: ${terminationTime.toISOString()}`);
          
          // Check if this was a ~1 hour termination
          if (actualDuration >= 55 && actualDuration <= 65) {
            console.log(`   🎯 CRITICAL: Stream terminated at ~1 hour mark! This confirms the 59-minute bug!`);
            this.log(`CRITICAL_TERMINATION: 1-hour termination confirmed - ${actualDuration} minutes actual runtime`);
          }
          
          // Clean up
          this.streamStartTimes.delete(streamId);
        }
      }

    } catch (error) {
      this.log(`ERROR: Failed to check streams: ${error.message}`);
    }
  }

  async getLiveStreams() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT id, title, start_time, status, user_id
        FROM streams 
        WHERE status = 'live'
        ORDER BY start_time DESC
      `, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  generateReport() {
    console.log('\n📊 REAL-TIME MONITORING REPORT');
    console.log('===============================\n');

    console.log(`📈 Total Discrepancies Detected: ${this.discrepancies.length}`);
    console.log(`📊 Streams Monitored: ${this.streamStartTimes.size}`);

    if (this.discrepancies.length > 0) {
      console.log('\n🚨 DURATION DISCREPANCIES:');
      this.discrepancies.forEach((disc, index) => {
        console.log(`${index + 1}. "${disc.title}"`);
        console.log(`   Our Duration: ${disc.ourDuration} minutes`);
        console.log(`   DB Duration: ${disc.dbDuration} minutes`);
        console.log(`   Discrepancy: ${disc.discrepancy} minutes`);
        console.log(`   Our Start: ${disc.ourStartTime.toISOString()}`);
        console.log(`   DB Start: ${disc.dbStartTime ? disc.dbStartTime.toISOString() : 'NULL'}`);
        console.log('');
      });
    }

    if (this.discrepancies.length > 0) {
      console.log('🔧 RECOMMENDATIONS:');
      console.log('- Database start_time is incorrect');
      console.log('- Duration calculations are unreliable');
      console.log('- Fix start_time setting in Stream.updateStatus()');
      console.log('- Use real-time monitoring for accurate duration tracking');
    } else {
      console.log('✅ No significant duration discrepancies detected');
    }
  }

  stopMonitoring() {
    this.monitoringActive = false;
    this.log('Real-time stream monitoring stopped');
    this.generateReport();
  }
}

// Handle graceful shutdown
const monitor = new RealTimeStreamMonitor();

process.on('SIGINT', () => {
  console.log('\n🛑 Stopping monitoring...');
  monitor.stopMonitoring();
  process.exit(0);
});

process.on('SIGTERM', () => {
  monitor.stopMonitoring();
  process.exit(0);
});

// Start monitoring
monitor.startMonitoring().catch(error => {
  console.error('Failed to start monitoring:', error);
  process.exit(1);
});
