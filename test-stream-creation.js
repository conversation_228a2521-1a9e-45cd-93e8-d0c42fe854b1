/**
 * Test Stream Creation Functionality
 * This script tests the stream creation API endpoint to identify issues
 */

const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:7575';
const TEST_USER = {
  username: 'kim<PERSON><PERSON>',
  password: 'password123'
};

// Test data for stream creation
const TEST_STREAM_DATA = {
  streamTitle: 'Test Stream Creation',
  videoId: 'dd49eb22-f50b-4792-8167-fc8d2ba2ca68', // Using existing video ID from logs
  rtmpUrl: 'rtmp://a.rtmp.youtube.com/live2',
  streamKey: 'test-stream-key-' + Date.now(),
  bitrate: '2500',
  fps: '30',
  loopVideo: true,
  orientation: 'horizontal',
  resolution: '1280x720',
  useAdvancedSettings: false
};

class StreamCreationTester {
  constructor() {
    this.sessionCookie = null;
    this.csrfToken = null;
  }

  async login() {
    console.log('🔐 Logging in...');
    try {
      // First get the login page to get CSRF token
      const loginPageResponse = await axios.get(`${BASE_URL}/login`);
      
      // Extract CSRF token from the response
      const csrfMatch = loginPageResponse.data.match(/name="_csrf" value="([^"]+)"/);
      if (csrfMatch) {
        this.csrfToken = csrfMatch[1];
        console.log('✅ CSRF token extracted:', this.csrfToken.substring(0, 20) + '...');
      }

      // Extract session cookie
      const cookies = loginPageResponse.headers['set-cookie'];
      if (cookies) {
        this.sessionCookie = cookies.find(cookie => cookie.startsWith('connect.sid='));
        console.log('✅ Session cookie extracted');
      }

      // Perform login
      const loginResponse = await axios.post(`${BASE_URL}/login`, {
        username: TEST_USER.username,
        password: TEST_USER.password,
        _csrf: this.csrfToken
      }, {
        headers: {
          'Cookie': this.sessionCookie,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 400
      });

      console.log('✅ Login successful');
      return true;
    } catch (error) {
      console.error('❌ Login failed:', error.message);
      return false;
    }
  }

  async testStreamCreation() {
    console.log('🧪 Testing stream creation...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/streams`, TEST_STREAM_DATA, {
        headers: {
          'Cookie': this.sessionCookie,
          'Content-Type': 'application/json',
          'X-CSRF-Token': this.csrfToken
        }
      });

      console.log('✅ Stream creation successful!');
      console.log('📊 Response:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error) {
      console.error('❌ Stream creation failed!');
      console.error('Status:', error.response?.status);
      console.error('Status Text:', error.response?.statusText);
      console.error('Response Data:', JSON.stringify(error.response?.data, null, 2));
      console.error('Error Message:', error.message);
      return null;
    }
  }

  async testFormValidation() {
    console.log('🧪 Testing form validation...');
    
    const invalidData = {
      streamTitle: '', // Empty title
      videoId: '',     // Empty video ID
      rtmpUrl: '',     // Empty RTMP URL
      streamKey: ''    // Empty stream key
    };

    try {
      const response = await axios.post(`${BASE_URL}/api/streams`, invalidData, {
        headers: {
          'Cookie': this.sessionCookie,
          'Content-Type': 'application/json',
          'X-CSRF-Token': this.csrfToken
        }
      });

      console.log('⚠️ Validation test unexpected success:', response.data);
    } catch (error) {
      console.log('✅ Validation working correctly');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data?.error);
    }
  }

  async checkQuota() {
    console.log('🔍 Checking quota...');
    
    try {
      const response = await axios.get(`${BASE_URL}/subscription/quota`, {
        headers: {
          'Cookie': this.sessionCookie
        }
      });

      console.log('✅ Quota check successful');
      console.log('📊 Quota data:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error) {
      console.error('❌ Quota check failed:', error.message);
      return null;
    }
  }

  async run() {
    console.log('🚀 Starting Stream Creation Test Suite\n');

    // Step 1: Login
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.log('❌ Test suite failed - cannot login');
      return;
    }

    console.log('');

    // Step 2: Check quota
    await this.checkQuota();
    console.log('');

    // Step 3: Test form validation
    await this.testFormValidation();
    console.log('');

    // Step 4: Test actual stream creation
    const result = await this.testStreamCreation();
    
    console.log('\n🏁 Test Suite Complete');
    
    if (result && result.success) {
      console.log('✅ Stream creation is working correctly!');
    } else {
      console.log('❌ Stream creation has issues that need to be fixed');
    }
  }
}

// Run the test
const tester = new StreamCreationTester();
tester.run().catch(console.error);
