<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Admin Video Deletion</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Debug Admin Video Deletion</h1>
        
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test API Response</h2>
            <button id="testApiBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white">
                Test API Call
            </button>
            <div id="apiResult" class="mt-4 p-4 bg-gray-700 rounded hidden">
                <h3 class="font-semibold mb-2">API Response:</h3>
                <pre id="apiResponse" class="text-sm overflow-auto"></pre>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Console Logs</h2>
            <div id="consoleLogs" class="bg-gray-700 p-4 rounded text-sm font-mono max-h-96 overflow-auto">
                <p class="text-gray-400">Console logs will appear here...</p>
            </div>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('consoleLogs');
        
        function addLogEntry(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type === 'error' ? 'text-red-400' : 'text-green-400';
            logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogEntry('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogEntry('error', ...args);
        };

        // Test API call
        document.getElementById('testApiBtn').addEventListener('click', async () => {
            console.log('Testing admin video deletion API...');
            
            try {
                // Use a video ID that we know exists from the logs
                const videoId = 'e52338ef-29e8-4e45-96b6-73410ef62ef8'; // SS live video
                const url = `/admin/api/videos/${videoId}`;
                
                console.log('Making DELETE request to:', url);
                
                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));
                
                const responseText = await response.text();
                console.log('Raw response text:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                    console.log('Parsed JSON result:', result);
                    console.log('Result success property:', result.success);
                    console.log('Result success type:', typeof result.success);
                } catch (parseError) {
                    console.error('Failed to parse JSON:', parseError);
                    result = { error: 'Failed to parse response as JSON', rawResponse: responseText };
                }
                
                // Display result
                const apiResult = document.getElementById('apiResult');
                const apiResponse = document.getElementById('apiResponse');
                apiResponse.textContent = JSON.stringify({
                    status: response.status,
                    ok: response.ok,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: result
                }, null, 2);
                apiResult.classList.remove('hidden');
                
                // Test the logic that determines success/error
                if (response.ok) {
                    console.log('Response is OK, checking result.success...');
                    if (result.success) {
                        console.log('✅ SUCCESS: Should show success state');
                    } else {
                        console.log('❌ FAILURE: Should show error state - result.success is falsy');
                    }
                } else {
                    console.log('❌ HTTP ERROR: Should show error state');
                }
                
            } catch (error) {
                console.error('Fetch error:', error);
                console.error('Error name:', error.name);
                console.error('Error message:', error.message);
            }
        });
        
        console.log('Debug page loaded');
    </script>
</body>
</html>
