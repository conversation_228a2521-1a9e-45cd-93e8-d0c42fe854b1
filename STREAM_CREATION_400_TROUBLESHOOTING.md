# Stream Creation 400 Error Troubleshooting Guide

## Overview
This guide helps diagnose and fix HTTP 400 Bad Request errors when creating streams in StreamOnPod application.

## Validation Flow Analysis

The stream creation endpoint (`POST /api/streams`) has multiple validation layers:

### 1. Authentication Check
- **Middleware**: `isAuthenticated`
- **Error**: 401 if not logged in

### 2. Subscription Validation
- **Middleware**: `QuotaMiddleware.checkValidSubscription()`
- **Checks**:
  - Active subscription existence
  - Subscription expiry status
  - Preview plan handling
- **Errors**: 403 with specific messages

### 3. Streaming Quota Validation
- **Middleware**: `QuotaMiddleware.checkStreamingQuota()`
- **Checks**:
  - Current vs maximum streaming slots
  - Preview plan restrictions (0 slots)
- **Errors**: 403 with quota details

### 4. Field Validation
- **Middleware**: `express-validator`
- **Required Fields**:
  - `streamTitle`: Must be non-empty after trim
  - `rtmpUrl`: Must be non-empty after trim
  - `streamKey`: Must be non-empty after trim
- **Error**: 400 with field-specific message

### 5. RTMP Configuration Validation
- **Validator**: `StreamKeyValidator.validateRtmpConfig()`
- **Checks**:
  - RTMP URL format (must start with rtmp:// or rtmps://)
  - Platform-specific validation
  - Stream key format and length
  - Placeholder detection
- **Error**: 400 with detailed validation errors

### 6. Stream Key Uniqueness
- **Check**: `Stream.isStreamKeyInUse()`
- **Error**: 400 if stream key already in use

### 7. Video Validation (if videoId provided)
- **Checks**:
  - Video exists and belongs to user
  - Video processing status is 'completed'
- **Errors**: 404 for not found, 400 for processing status

### 8. Advanced Settings Validation
- **Check**: Plan price >= 49900 for advanced settings
- **Error**: 403 if plan doesn't support advanced settings

### 9. Schedule Time Validation
- **Check**: Schedule time must be at least 1 minute in future
- **Error**: 400 if schedule time is in past

## Common 400 Error Causes & Solutions

### 1. Missing Required Fields
**Error Message**: "Title is required" / "RTMP URL is required" / "Stream key is required"

**Cause**: Request body missing required fields or fields are empty/whitespace

**Solution**:
```javascript
// Ensure all required fields are present and non-empty
const streamData = {
  streamTitle: "My Stream Title",        // Required, non-empty
  rtmpUrl: "rtmp://a.rtmp.youtube.com/live2",  // Required, non-empty
  streamKey: "your-actual-stream-key"    // Required, non-empty
};
```

### 2. Invalid RTMP URL Format
**Error Message**: "Invalid RTMP configuration"

**Common Issues**:
- URL doesn't start with `rtmp://` or `rtmps://`
- Using placeholder URLs like `rtmp://your-server.com`
- Using local URLs like `rtmp://localhost`

**Solution**:
```javascript
// Valid RTMP URLs
const validUrls = [
  "rtmp://a.rtmp.youtube.com/live2",           // YouTube
  "rtmp://live.twitch.tv/live",                // Twitch
  "rtmps://live-api-s.facebook.com/rtmp/123",  // Facebook
  "rtmp://your-actual-server.com/live"         // Custom
];
```

### 3. Invalid Stream Key
**Error Message**: "Invalid RTMP configuration" with stream key details

**Common Issues**:
- Stream key too short or too long
- Using placeholder keys like "test", "123", "your_stream_key"
- Invalid characters in stream key

**Solution**:
```javascript
// Platform-specific stream key requirements
const streamKeyRequirements = {
  youtube: "20-50 characters, alphanumeric + dash/underscore",
  twitch: "live_123456_abcdef... format",
  facebook: "15-50 characters, alphanumeric + dash/underscore",
  custom: "4-100 characters, alphanumeric + special chars"
};
```

### 4. Duplicate Stream Key
**Error Message**: "This stream key is already in use. Please use a different key."

**Solution**: Use a unique stream key that's not already used by another stream

### 5. Video Processing Not Complete
**Error Message**: "Video is still waiting to be processed" / "Video is currently being processed"

**Solution**: Wait for video processing to complete before creating stream, or create stream without video

### 6. Schedule Time in Past
**Error Message**: "Schedule time must be at least 1 minute in the future"

**Solution**:
```javascript
// Set schedule time at least 1 minute in future
const scheduleTime = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes from now
const streamData = {
  // ... other fields
  scheduleTime: scheduleTime.toISOString().slice(0, 19) // Format: YYYY-MM-DDTHH:mm:ss
};
```

### 7. Advanced Settings Without Proper Plan
**Error Message**: "Advanced settings hanya tersedia di plan premium"

**Solution**: Upgrade to a plan with price >= Rp. 49,900 or remove advanced settings

## Subscription/Quota Related 403 Errors

### 1. No Active Subscription
**Error Message**: "You need an active subscription plan to use streaming features"

**Solution**: Subscribe to a paid plan or ensure Preview plan users are handled correctly

### 2. Expired Subscription
**Error Message**: "Your subscription has expired and you have been downgraded to Preview plan"

**Solution**: Renew subscription or upgrade to a new plan

### 3. Preview Plan Streaming Restriction
**Error Message**: "Preview plan does not allow streaming"

**Solution**: Upgrade to Basic plan or higher (Preview plan has 0 streaming slots)

### 4. Streaming Slot Limit Reached
**Error Message**: "You have reached your streaming limit of X concurrent streams"

**Solution**: Stop existing streams or upgrade to a plan with more streaming slots

## Debugging Steps

### 1. Check Server Logs
Look for detailed error messages in the console output:
```bash
# Look for these log patterns
grep "Error creating stream" logs/app.log
grep "🚫" logs/app.log  # Blocked operations
grep "⚠️" logs/app.log  # Warnings
```

### 2. Use Diagnostic Script
Run the diagnostic script to test various scenarios:
```bash
node diagnose-stream-creation-400.js
```

### 3. Check Network Request
Inspect the actual HTTP request being sent:
- Verify Content-Type is `application/json`
- Check all required fields are present
- Ensure session cookies are included

### 4. Test with Minimal Data
Start with minimal valid data and add fields incrementally:
```javascript
const minimalStream = {
  streamTitle: "Test Stream",
  rtmpUrl: "rtmp://a.rtmp.youtube.com/live2",
  streamKey: "test-key-12345678901234567890"
};
```

### 5. Check User Status
Verify user account status:
- Login status
- Subscription status
- Plan type and limits
- Current streaming slot usage

## Quick Fix Checklist

- [ ] All required fields present and non-empty
- [ ] RTMP URL starts with rtmp:// or rtmps://
- [ ] Stream key is valid format and unique
- [ ] User has active subscription (or is on Preview plan)
- [ ] Streaming slots available
- [ ] Video processing completed (if videoId provided)
- [ ] Schedule time in future (if provided)
- [ ] Advanced settings only used with premium plan
- [ ] No placeholder values used

## Error Response Format

400 errors typically return:
```json
{
  "success": false,
  "error": "Specific error message",
  "details": {
    // Additional error details if available
    "errors": ["List of validation errors"],
    "warnings": ["List of warnings"],
    "suggestions": ["List of suggestions"]
  }
}
```

## Prevention Tips

1. **Client-side Validation**: Implement validation on frontend before sending request
2. **Real-time Feedback**: Validate fields as user types
3. **Clear Error Messages**: Display specific error messages to users
4. **Subscription Checks**: Check subscription status before allowing stream creation
5. **Testing**: Use diagnostic tools to test various scenarios

This troubleshooting guide should help identify and resolve most 400 errors encountered during stream creation.
