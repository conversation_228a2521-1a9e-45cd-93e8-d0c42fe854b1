/**
 * Deployment script for UI responsiveness enhancements
 * This script safely adds the enhanced UI components to StreamOnPod
 */

const fs = require('fs');
const path = require('path');

class UIEnhancementDeployer {
  constructor() {
    this.backupDir = './backups';
    this.deploymentLog = [];
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    this.deploymentLog.push(logMessage);
  }

  async createBackup(filePath) {
    try {
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
      }

      if (fs.existsSync(filePath)) {
        const backupPath = path.join(this.backupDir, `${path.basename(filePath)}.backup.${Date.now()}`);
        fs.copyFileSync(filePath, backupPath);
        this.log(`✅ Backup created: ${backupPath}`);
        return backupPath;
      }
    } catch (error) {
      this.log(`❌ Backup failed for ${filePath}: ${error.message}`);
      throw error;
    }
  }

  async addScriptToLayout() {
    const layoutPath = './views/layout.ejs';
    
    try {
      if (!fs.existsSync(layoutPath)) {
        this.log(`❌ Layout file not found: ${layoutPath}`);
        return false;
      }

      // Create backup
      await this.createBackup(layoutPath);

      // Read current layout
      let layoutContent = fs.readFileSync(layoutPath, 'utf8');

      // Check if script is already added
      if (layoutContent.includes('enhanced-stream-modal.js')) {
        this.log('⚠️  Enhanced stream modal script already exists in layout');
        return true;
      }

      // Add script before closing body tag
      const scriptTag = '  <script src="/js/enhanced-stream-modal.js" defer></script>\n</body>';
      layoutContent = layoutContent.replace('</body>', scriptTag);

      // Add notification styles to head
      const notificationStyles = `
  <style>
    .notification-container {
      position: fixed;
      top: 1rem;
      right: 1rem;
      z-index: 9999;
      max-width: 400px;
    }
    .notification {
      transform: translateX(100%);
      transition: transform 0.3s ease, opacity 0.3s ease;
      opacity: 0;
      margin-bottom: 0.5rem;
    }
    .notification.show {
      transform: translateX(0);
      opacity: 1;
    }
    @media (max-width: 640px) {
      .notification-container {
        top: 0.5rem;
        right: 0.5rem;
        left: 0.5rem;
        max-width: none;
      }
    }
  </style>
</head>`;

      layoutContent = layoutContent.replace('</head>', notificationStyles);

      // Write updated layout
      fs.writeFileSync(layoutPath, layoutContent);
      this.log('✅ Enhanced UI script and styles added to layout');
      return true;

    } catch (error) {
      this.log(`❌ Failed to update layout: ${error.message}`);
      return false;
    }
  }

  async verifyFiles() {
    const requiredFiles = [
      './public/js/enhanced-stream-modal.js',
      './views/layout.ejs'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        this.log(`❌ Required file missing: ${file}`);
        return false;
      }
    }

    this.log('✅ All required files exist');
    return true;
  }

  async testDeployment() {
    try {
      this.log('🧪 Testing deployment...');

      // Check if enhanced script loads without syntax errors
      const scriptPath = './public/js/enhanced-stream-modal.js';
      const scriptContent = fs.readFileSync(scriptPath, 'utf8');

      // Basic syntax check
      if (!scriptContent.includes('NotificationSystem') || 
          !scriptContent.includes('StreamControlHandler')) {
        this.log('❌ Enhanced script appears to be incomplete');
        return false;
      }

      this.log('✅ Enhanced script appears to be complete');
      return true;

    } catch (error) {
      this.log(`❌ Deployment test failed: ${error.message}`);
      return false;
    }
  }

  async deploy() {
    this.log('🚀 Starting UI Enhancement Deployment');
    this.log('=====================================');

    try {
      // Step 1: Verify files
      if (!(await this.verifyFiles())) {
        throw new Error('File verification failed');
      }

      // Step 2: Test deployment
      if (!(await this.testDeployment())) {
        throw new Error('Deployment test failed');
      }

      // Step 3: Add script to layout
      if (!(await this.addScriptToLayout())) {
        throw new Error('Failed to update layout');
      }

      this.log('=====================================');
      this.log('✅ UI Enhancement Deployment Complete!');
      this.log('');
      this.log('📋 What was deployed:');
      this.log('   • Enhanced stream modal with loading states');
      this.log('   • Modern toast notification system');
      this.log('   • Button state management');
      this.log('   • Request timeout and retry handling');
      this.log('   • Improved error messages');
      this.log('');
      this.log('🔄 Next steps:');
      this.log('   1. Restart your application');
      this.log('   2. Test stream creation and starting');
      this.log('   3. Verify loading states and notifications work');
      this.log('');
      this.log('🔙 Rollback instructions:');
      this.log('   • Backups created in ./backups/ directory');
      this.log('   • Remove enhanced-stream-modal.js script from layout to rollback');

      return true;

    } catch (error) {
      this.log(`❌ Deployment failed: ${error.message}`);
      this.log('');
      this.log('🔙 Rollback may be needed if any changes were made');
      this.log('   • Check ./backups/ directory for backup files');
      return false;
    }
  }

  async rollback() {
    this.log('🔄 Starting UI Enhancement Rollback');
    this.log('===================================');

    try {
      const layoutPath = './views/layout.ejs';
      const backupFiles = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('layout.ejs.backup'))
        .sort()
        .reverse(); // Get most recent backup

      if (backupFiles.length === 0) {
        this.log('❌ No backup files found');
        return false;
      }

      const latestBackup = path.join(this.backupDir, backupFiles[0]);
      fs.copyFileSync(latestBackup, layoutPath);
      
      this.log(`✅ Rollback complete - restored from ${latestBackup}`);
      return true;

    } catch (error) {
      this.log(`❌ Rollback failed: ${error.message}`);
      return false;
    }
  }

  async generateReport() {
    const reportPath = './deployment-report.txt';
    const report = [
      'StreamOnPod UI Enhancement Deployment Report',
      '==========================================',
      '',
      ...this.deploymentLog,
      '',
      'Files involved:',
      '• ./views/layout.ejs - Updated with enhanced script and styles',
      '• ./public/js/enhanced-stream-modal.js - Enhanced UI components',
      '• ./backups/ - Backup files for rollback',
      '',
      'Generated at: ' + new Date().toISOString()
    ].join('\n');

    fs.writeFileSync(reportPath, report);
    this.log(`📄 Deployment report saved: ${reportPath}`);
  }
}

// CLI interface
async function main() {
  const deployer = new UIEnhancementDeployer();
  const command = process.argv[2] || 'deploy';

  switch (command) {
    case 'deploy':
      const success = await deployer.deploy();
      await deployer.generateReport();
      process.exit(success ? 0 : 1);
      break;

    case 'rollback':
      const rollbackSuccess = await deployer.rollback();
      await deployer.generateReport();
      process.exit(rollbackSuccess ? 0 : 1);
      break;

    case 'test':
      const testSuccess = await deployer.testDeployment();
      process.exit(testSuccess ? 0 : 1);
      break;

    default:
      console.log('Usage: node deploy-ui-enhancements.js [deploy|rollback|test]');
      console.log('');
      console.log('Commands:');
      console.log('  deploy   - Deploy UI enhancements (default)');
      console.log('  rollback - Rollback to previous version');
      console.log('  test     - Test deployment without making changes');
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Deployment script error:', error);
    process.exit(1);
  });
}

module.exports = UIEnhancementDeployer;
