const Subscription = require('../models/Subscription');
const Permission = require('../models/Permission');
const {
  createAuthError,
  createAuthzError,
  createStorageError,
  formatErrorResponse,
  errorHandlerMiddleware
} = require('../utils/errorHandler');

class QuotaMiddleware {
  // Hapus semua implementasi cache
  // static quotaCache = new Map();
  // static async getFromCache(key) { ... }
  // static async setCache(key, data) { ... }
  // static clearUserCache(userId) { ... }

  // Check if user has a valid subscription plan
  static checkValidSubscription() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          throw createAuthError('Authentication required');
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Check if user has an active subscription
        const subscription = await Subscription.getUserSubscription(req.session.userId);

        if (!subscription) {
          // TEMPORARY FIX: Allow Preview plan users (they don't have subscriptions in user_subscriptions table)
          // Check if user is on Preview plan
          const User = require('../models/User');
          const user = await User.findById(req.session.userId);

          if (user && user.plan_type === 'Preview') {
            console.log('⚠️ TEMPORARY: Allowing Preview plan user to proceed (no subscription required)');
            return next();
          }

          throw createAuthzError('You need an active subscription plan to use streaming features. Please subscribe to a plan first.');
        }

        // Check if subscription has expired
        if (subscription.end_date && new Date(subscription.end_date) < new Date()) {
          // Handle expired subscription - downgrade to Preview plan
          const Subscription = require('../models/Subscription');
          await Subscription.handleExpiredSubscription(req.session.userId);

          throw createAuthzError('Your subscription has expired and you have been downgraded to Preview plan. Please renew to continue streaming.');
        }

        next();
      } catch (error) {
        return errorHandlerMiddleware(error, req, res, next);
      }
    };
  }

  // Check streaming slot quota
  static checkStreamingQuota() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        const quotaCheck = await Subscription.checkStreamingSlotLimit(req.session.userId);

        if (quotaCheck.hasLimit) {
          // Special message for Preview plan users (0 slots)
          const message = quotaCheck.maxSlots === 0
            ? 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.'
            : `You have reached your streaming limit of ${quotaCheck.maxSlots} concurrent streams. Please upgrade your plan or stop an existing stream.`;

          // Use consistent error format that matches the frontend expectations
          return res.status(403).json({
            success: false,
            error: message,
            code: quotaCheck.maxSlots === 0 ? 'PREVIEW_PLAN_LIMIT' : 'STREAMING_SLOT_LIMIT',
            details: {
              currentSlots: quotaCheck.currentSlots,
              maxSlots: quotaCheck.maxSlots,
              isPreviewPlan: quotaCheck.maxSlots === 0,
              planType: quotaCheck.maxSlots === 0 ? 'Preview' : 'Paid'
            }
          });
        }

        next();
      } catch (error) {
        console.error('Streaming quota check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check storage quota
  static checkStorageQuota(additionalSizeGB = 0) {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Calculate file size if it's an upload
        let fileSizeGB = additionalSizeGB;
        if (req.file && req.file.size) {
          fileSizeGB = req.file.size / (1024 * 1024 * 1024); // Convert bytes to GB
        } else if (req.files && req.files.length > 0) {
          fileSizeGB = req.files.reduce((total, file) => total + file.size, 0) / (1024 * 1024 * 1024);
        }

        const quotaCheck = await Subscription.checkStorageLimit(req.session.userId, fileSizeGB);

        if (quotaCheck.hasLimit) {
          // Format storage amounts for user-friendly display
          const formatStorage = (sizeGB) => {
            if (sizeGB < 1) {
              return `${Math.round(sizeGB * 1024)}MB`;
            } else {
              return `${sizeGB.toFixed(2)}GB`;
            }
          };

          const availableFormatted = formatStorage(quotaCheck.availableStorage);
          const maxFormatted = formatStorage(quotaCheck.maxStorage);
          const requestedFormatted = formatStorage(fileSizeGB);

          // Clean up uploaded file if quota exceeded
          if (req.file && req.file.path) {
            try {
              const fs = require('fs');
              if (fs.existsSync(req.file.path)) {
                fs.unlinkSync(req.file.path);
                // console.log(`🗑️ Cleaned up uploaded file due to quota exceeded: ${req.file.path}`); // Removed for production
              }
            } catch (cleanupError) {
              console.error('❌ Error cleaning up uploaded file:', cleanupError.message);
            }
          }

          return res.status(413).json({
            error: 'Storage limit exceeded',
            details: {
              currentStorage: quotaCheck.currentStorage,
              maxStorage: quotaCheck.maxStorage,
              availableStorage: quotaCheck.availableStorage,
              requestedSize: fileSizeGB,
              message: `Upload failed: This file (${requestedFormatted}) would exceed your storage limit. You have ${availableFormatted} available out of ${maxFormatted} total.`
            }
          });
        }

        // Store the file size for later use
        req.uploadSizeGB = fileSizeGB;
        next();
      } catch (error) {
        console.error('Storage quota check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Update storage usage after successful upload
  static updateStorageUsage() {
    return async (req, res, next) => {
      try {
        if (req.uploadSizeGB && req.session.userId) {
          const isProduction = process.env.NODE_ENV === 'production';
          const enableConsoleLogging = false; // Disabled for cleaner output

          if (!isProduction && enableConsoleLogging) {
            console.log(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
          }
          await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
          if (!isProduction && enableConsoleLogging) {
            // console.log('✅ Storage updated successfully'); // Removed for production
          }
        }
        next();
      } catch (error) {
        console.error('❌ Storage usage update error:', error);
        // Don't fail the request, just log the error
        next();
      }
    };
  }

  // Check if user account is active
  static checkActiveAccount() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const User = require('../models/User');
        const user = await User.findById(req.session.userId);

        if (!user || !user.is_active) {
          req.session.destroy();
          return res.status(403).json({
            error: 'Account suspended',
            message: 'Your account has been suspended. Please contact support.'
          });
        }

        next();
      } catch (error) {
        console.error('Account status check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Check subscription validity
  static checkSubscription() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (bypass subscription check)
        const isAdmin = await Permission.isAdmin(req.session.userId);
        if (isAdmin) {
          return next();
        }

        const subscription = await Subscription.getUserSubscription(req.session.userId);

        if (!subscription) {
          return res.status(403).json({
            error: 'No active subscription',
            message: 'You need an active subscription to use this feature.'
          });
        }

        // Check if subscription has expired
        if (subscription.end_date && new Date(subscription.end_date) < new Date()) {
          // Handle expired subscription - downgrade to Preview plan
          const Subscription = require('../models/Subscription');
          await Subscription.handleExpiredSubscription(req.session.userId);

          return res.status(403).json({
            error: 'Subscription expired',
            message: 'Your subscription has expired and you have been downgraded to Preview plan. Please renew to continue using this feature.',
            canRenew: true,
            expiredPlan: subscription.plan_name
          });
        }

        next();
      } catch (error) {
        console.error('Subscription check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Get user quota information - tanpa cache
  static async getUserQuotaInfo(userId) {
    try {
      // console.log(`🔄 Fetching quota info for user ${userId}`); // Removed for production
      // Get user info first to check their plan
      const User = require('../models/User');
      const user = await User.findById(userId);
  
      if (!user) {
        return null;
      }
  
      // Check for an active trial first
      const trialInfo = await User.hasActiveTrial(userId);
  
      // Calculate actual storage from videos table for accuracy
      const { db } = require('../db/database');
      const actualStorageResult = await new Promise((resolve, reject) => {
        db.get(
          'SELECT COALESCE(SUM(file_size), 0) as total_storage_bytes FROM videos WHERE user_id = ?',
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
  
      const actualStorageGB = actualStorageResult.total_storage_bytes / (1024 * 1024 * 1024);
      
      // Auto-sync: Update cached value if different
      const cachedStorageGB = user.used_storage_gb || 0;
      const storageDifference = Math.abs(actualStorageGB - cachedStorageGB);
      
      // If difference is more than 0.01GB (10MB), update the cache
      if (storageDifference > 0.01) {
        // console.log(`🔄 Auto-syncing storage for user ${userId}: ${cachedStorageGB}GB -> ${actualStorageGB}GB`); // Removed for production
        await this.syncUserStorageCache(userId, actualStorageGB);
      }
  
      const [streamingQuota, subscription] = await Promise.all([
        Subscription.checkStreamingSlotLimit(userId),
        Subscription.getUserSubscription(userId)
      ]);
  
      // Get plan details including price
      let planDetails = null;
  
      if (subscription && subscription.plan_id) {
        // User has active subscription - get plan by subscription plan_id
        planDetails = await new Promise((resolve, reject) => {
          db.get(
            'SELECT id, name, price, currency FROM subscription_plans WHERE id = ?',
            [subscription.plan_id],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });
      } else if (user.plan_type) {
        // User has no active subscription but has plan_type - get plan by name
        planDetails = await new Promise((resolve, reject) => {
          db.get(
            'SELECT id, name, price, currency FROM subscription_plans WHERE name = ? AND is_active = 1',
            [user.plan_type],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });
      }
  
      // Determine max slots and storage, prioritizing trial plan if active
      let maxSlots, maxStorage;
  
      if (trialInfo) {
        // Trial is active, use trial plan's quota
        maxSlots = trialInfo.trial_slots;
        maxStorage = trialInfo.trial_storage_gb;
        // console.log(`[QUOTA] User ${userId} is on trial. Using trial quotas: ${maxSlots} slots, ${maxStorage}GB storage.`); // Removed for production
      } else {
        // No trial, use regular subscription or user plan limits
        maxSlots = streamingQuota.maxSlots !== undefined ? streamingQuota.maxSlots : user.max_streaming_slots || 0;
        const defaultStorage = user.plan_type === 'Preview' ? 1 : 2;
        maxStorage = subscription ? subscription.max_storage_gb : user.max_storage_gb || defaultStorage;
      }
      
      // Use actual calculated storage for accuracy
      const currentStorage = actualStorageGB;
  
      // Format storage display - show in MB if 1GB or less, otherwise GB
      const formatStorageDisplay = (storageGB, maxStorageGB) => {
        if (maxStorageGB <= 1 && storageGB <= 1) {
          return {
            current: Math.round(storageGB * 1024),
            max: Math.round(maxStorageGB * 1024),
            unit: 'MB'
          };
        } else {
          return {
            current: parseFloat(storageGB.toFixed(2)),
            max: maxStorageGB,
            unit: 'GB'
          };
        }
      };
  
      const storageDisplay = formatStorageDisplay(currentStorage, maxStorage);
  
      const result = {
        streaming: {
          current: streamingQuota.currentSlots || 0,
          max: maxSlots,
          percentage: maxSlots > 0 ? Math.round(((streamingQuota.currentSlots || 0) / maxSlots) * 100) : 0
        },
        storage: {
          current: storageDisplay.current,
          max: storageDisplay.max,
          unit: storageDisplay.unit,
          percentage: storageDisplay.max > 0 ? Math.round((storageDisplay.current / storageDisplay.max) * 100) : 0,
          actualGB: currentStorage // Include actual GB value for internal use
        },
        subscription: subscription ? {
          plan: subscription.plan_name,
          status: subscription.status,
          endDate: subscription.end_date
        } : null,
        plan: {
          name: planDetails ? planDetails.name : (user.plan_type || 'Preview'),
          price: planDetails ? planDetails.price : 0,
          currency: planDetails ? planDetails.currency : 'IDR'
        }
      };
      
      // Hapus bagian caching
      // await this.setCache(cacheKey, { data: result, timestamp: Date.now() });
      // console.log(`💾 Cached quota info for user ${userId}`);
      
      return result;
    } catch (error) {
      console.error('Error getting quota info:', error);
      return null;
    }
  }

  // Hapus atau simplifikasi syncUserStorageCache
  static async syncUserStorageCache(userId, actualStorageGB) {
    const { db } = require('../db/database');
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [actualStorageGB, userId],
        function(err) {
          if (err) {
            console.error('❌ Error syncing storage cache:', err);
            reject(err);
          } else {
            // console.log(`✅ Storage cache synced for user ${userId}: ${actualStorageGB}GB`); // Removed for production
            // Hapus invalidation cache
            // QuotaMiddleware.invalidateUserQuotaCache(userId);
            resolve(this.changes);
          }
        }
      );
    });
  }

  // Hapus fungsi invalidateUserQuotaCache
  // static invalidateUserQuotaCache(userId) { ... }

  // New method for bulk storage sync (for maintenance)
  static async syncAllUsersStorageCache() {
    const { db } = require('../db/database');
    // console.log('🔄 Starting bulk storage cache sync...'); // Removed for production
    return new Promise((resolve, reject) => {
      db.all(
        `UPDATE users 
         SET used_storage_gb = (
           SELECT COALESCE(SUM(file_size), 0) / (1024 * 1024 * 1024)
           FROM videos 
           WHERE videos.user_id = users.id
         ),
         updated_at = CURRENT_TIMESTAMP
         WHERE id IN (
           SELECT DISTINCT user_id FROM videos
         )`,
        [],
        function(err) {
          if (err) {
            console.error('❌ Error in bulk storage sync:', err);
            reject(err);
          } else {
            // console.log(`✅ Bulk storage sync completed. Updated ${this.changes} users.`); // Removed for production
            resolve(this.changes);
          }
        }
      );
    });
  }

  // Get plans that support advanced settings (price >= 49900)
  static async getAdvancedSettingsEligiblePlans() {
    const { db } = require('../db/database');
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT name, price FROM subscription_plans WHERE price >= 49900 AND is_active = 1 ORDER BY price ASC',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  // Get plans that support Google Drive import (price >= 49000)
  static async getGoogleDriveEligiblePlans() {
    const { db } = require('../db/database');
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT name, price FROM subscription_plans WHERE price >= 49000 AND is_active = 1 ORDER BY price ASC',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }
}

module.exports = QuotaMiddleware;
