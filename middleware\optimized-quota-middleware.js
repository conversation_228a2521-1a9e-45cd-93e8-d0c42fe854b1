/**
 * Optimized Quota Middleware
 * Reduces database queries and improves response times for stream operations
 */

const Subscription = require('../models/Subscription');
const Permission = require('../models/Permission');
const {
  createAuthError,
  createAuthzError,
  createStorageError,
  formatErrorResponse,
  errorHandlerMiddleware
} = require('../utils/errorHandler');

// In-memory cache for frequently accessed data
class QuotaCache {
  constructor() {
    this.cache = new Map();
    this.cacheTTL = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes
  }

  set(key, value, ttl = this.defaultTTL) {
    this.cache.set(key, value);
    this.cacheTTL.set(key, Date.now() + ttl);
  }

  get(key) {
    const expiry = this.cacheTTL.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheTTL.delete(key);
      return null;
    }
    return this.cache.get(key);
  }

  delete(key) {
    this.cache.delete(key);
    this.cacheTTL.delete(key);
  }

  clear() {
    this.cache.clear();
    this.cacheTTL.clear();
  }

  // Clear user-specific cache entries
  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(`user:${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.delete(key));
  }
}

const quotaCache = new QuotaCache();

class OptimizedQuotaMiddleware {
  
  // Combined subscription and quota validation
  static checkSubscriptionAndQuota() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          throw createAuthError('Authentication required');
        }

        // Check if user is admin (unlimited access)
        const isAdmin = await this.checkAdminStatus(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Get combined user data (subscription + quota info)
        const userQuotaData = await this.getCombinedUserQuotaData(req.session.userId);

        // Validate subscription
        this.validateSubscription(userQuotaData);

        // Validate quota limits
        this.validateQuotaLimits(userQuotaData);

        next();
      } catch (error) {
        return errorHandlerMiddleware(error, req, res, next);
      }
    };
  }

  // Optimized admin check with caching
  static async checkAdminStatus(userId) {
    const cacheKey = `admin:${userId}`;
    let isAdmin = quotaCache.get(cacheKey);

    if (isAdmin === null) {
      try {
        // Check admin status directly from users table (more efficient)
        const { db } = require('../db/database');
        const user = await new Promise((resolve, reject) => {
          db.get('SELECT role FROM users WHERE id = ?', [userId], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });

        isAdmin = user && user.role === 'admin';
        quotaCache.set(cacheKey, isAdmin, 10 * 60 * 1000); // Cache for 10 minutes
      } catch (error) {
        console.error('Error checking admin status:', error);
        isAdmin = false;
      }
    }

    return isAdmin;
  }

  // Combined database query for user subscription and quota data
  static async getCombinedUserQuotaData(userId) {
    const cacheKey = `quota:${userId}`;
    let cachedData = quotaCache.get(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    // Single optimized query to get all required data
    const { db } = require('../db/database');
    
    const userData = await new Promise((resolve, reject) => {
      db.get(`
        SELECT 
          u.id as user_id,
          u.plan_type,
          u.max_streaming_slots as user_max_slots,
          u.max_storage_gb as user_max_storage,
          u.used_storage_gb,
          us.id as subscription_id,
          us.status as subscription_status,
          us.end_date as subscription_end_date,
          sp.name as plan_name,
          sp.max_streaming_slots as subscription_max_slots,
          sp.max_storage_gb as subscription_max_storage,
          sp.features,
          (SELECT COUNT(*) FROM streams WHERE user_id = u.id) as current_streams
        FROM users u
        LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
        LEFT JOIN subscription_plans sp ON us.plan_id = sp.id AND sp.name != 'Preview'
        WHERE u.id = ?
        ORDER BY us.created_at DESC
        LIMIT 1
      `, [userId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!userData) {
      throw createAuthError('User not found');
    }

    // Process the combined data
    const processedData = this.processUserQuotaData(userData);
    
    // Cache the result for 2 minutes (shorter TTL for quota data)
    quotaCache.set(cacheKey, processedData, 2 * 60 * 1000);
    
    return processedData;
  }

  // Process combined user data into structured format
  static processUserQuotaData(userData) {
    const hasActiveSubscription = userData.subscription_id && userData.subscription_status === 'active';
    const subscriptionExpired = hasActiveSubscription && 
      userData.subscription_end_date && 
      new Date(userData.subscription_end_date) < new Date();

    return {
      userId: userData.user_id,
      planType: userData.plan_type,
      
      // Subscription info
      hasActiveSubscription: hasActiveSubscription && !subscriptionExpired,
      subscriptionExpired,
      subscriptionEndDate: userData.subscription_end_date,
      planName: userData.plan_name,
      
      // Streaming quota
      maxStreamingSlots: hasActiveSubscription && !subscriptionExpired 
        ? userData.subscription_max_slots 
        : userData.user_max_slots || 0,
      currentStreams: userData.current_streams || 0,
      
      // Storage quota
      maxStorage: hasActiveSubscription && !subscriptionExpired 
        ? userData.subscription_max_storage 
        : userData.user_max_storage || (userData.plan_type === 'Preview' ? 1 : 2),
      currentStorage: userData.used_storage_gb || 0,
      
      // Features
      features: userData.features ? JSON.parse(userData.features) : []
    };
  }

  // Validate subscription status
  static validateSubscription(userQuotaData) {
    // Handle Preview plan users (they don't need active subscriptions)
    if (userQuotaData.planType === 'Preview') {
      return; // Preview plan users are allowed
    }

    // Check for active subscription
    if (!userQuotaData.hasActiveSubscription) {
      if (userQuotaData.subscriptionExpired) {
        // Handle expired subscription asynchronously
        this.handleExpiredSubscriptionAsync(userQuotaData.userId);
        throw createAuthzError('Your subscription has expired and you have been downgraded to Preview plan. Please renew to continue streaming.');
      } else {
        throw createAuthzError('You need an active subscription plan to use streaming features. Please subscribe to a plan first.');
      }
    }
  }

  // Validate quota limits
  static validateQuotaLimits(userQuotaData) {
    const { maxStreamingSlots, currentStreams } = userQuotaData;

    // Check streaming slot limits
    if (maxStreamingSlots !== -1 && currentStreams >= maxStreamingSlots) {
      const message = maxStreamingSlots === 0
        ? 'Preview plan does not allow streaming. Please upgrade to Basic plan to start streaming.'
        : `You have reached your streaming limit of ${maxStreamingSlots} concurrent streams. Please upgrade your plan or stop an existing stream.`;

      const error = createAuthzError(message);
      error.details = {
        currentSlots: currentStreams,
        maxSlots: maxStreamingSlots,
        message: message,
        isPreviewPlan: maxStreamingSlots === 0,
        errorType: maxStreamingSlots === 0 ? 'Streaming not allowed' : 'Streaming slot limit reached'
      };
      throw error;
    }
  }

  // Handle expired subscription asynchronously (non-blocking)
  static async handleExpiredSubscriptionAsync(userId) {
    try {
      const Subscription = require('../models/Subscription');
      await Subscription.handleExpiredSubscription(userId);
      
      // Clear cache for this user
      quotaCache.clearUserCache(userId);
    } catch (error) {
      console.error('Error handling expired subscription:', error);
    }
  }

  // Storage quota check with caching
  static checkStorageQuota(additionalSizeGB = 0) {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        // Check if user is admin (unlimited)
        const isAdmin = await this.checkAdminStatus(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Get user quota data (will use cache if available)
        const userQuotaData = await this.getCombinedUserQuotaData(req.session.userId);

        // Calculate file size if it's an upload
        let fileSizeGB = additionalSizeGB;
        if (req.file && req.file.size) {
          fileSizeGB = req.file.size / (1024 * 1024 * 1024);
        } else if (req.files && req.files.length > 0) {
          fileSizeGB = req.files.reduce((total, file) => total + file.size, 0) / (1024 * 1024 * 1024);
        }

        const { maxStorage, currentStorage } = userQuotaData;
        const wouldExceed = (currentStorage + fileSizeGB) > maxStorage;

        if (wouldExceed) {
          const availableStorage = maxStorage - currentStorage;
          const requestedFormatted = `${fileSizeGB.toFixed(2)}GB`;
          const availableFormatted = `${availableStorage.toFixed(2)}GB`;
          const maxFormatted = `${maxStorage}GB`;

          return res.status(413).json({
            error: 'Storage limit exceeded',
            details: {
              currentStorage: currentStorage,
              maxStorage: maxStorage,
              availableStorage: availableStorage,
              requestedSize: fileSizeGB,
              message: `Upload failed: This file (${requestedFormatted}) would exceed your storage limit. You have ${availableFormatted} available out of ${maxFormatted} total.`
            }
          });
        }

        // Store the file size for later use
        req.uploadSizeGB = fileSizeGB;
        next();
      } catch (error) {
        console.error('Storage quota check error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Get user quota information (optimized version)
  static async getUserQuotaInfo(userId) {
    try {
      const userQuotaData = await this.getCombinedUserQuotaData(userId);
      
      return {
        streaming: {
          current: userQuotaData.currentStreams,
          max: userQuotaData.maxStreamingSlots,
          available: userQuotaData.maxStreamingSlots === -1 
            ? 'Unlimited' 
            : Math.max(0, userQuotaData.maxStreamingSlots - userQuotaData.currentStreams)
        },
        storage: {
          current: userQuotaData.currentStorage,
          max: userQuotaData.maxStorage,
          available: Math.max(0, userQuotaData.maxStorage - userQuotaData.currentStorage),
          unit: 'GB'
        },
        plan: {
          name: userQuotaData.planName || userQuotaData.planType,
          features: userQuotaData.features
        }
      };
    } catch (error) {
      console.error('Error getting user quota info:', error);
      throw error;
    }
  }

  // Clear cache when user data changes
  static clearUserCache(userId) {
    quotaCache.clearUserCache(userId);
  }

  // Clear all cache (for maintenance)
  static clearAllCache() {
    quotaCache.clear();
  }

  // Legacy methods for backward compatibility
  static checkValidSubscription() {
    return this.checkSubscriptionAndQuota();
  }

  static checkStreamingQuota() {
    // Return a no-op middleware since quota is checked in combined method
    return (req, res, next) => next();
  }

  // Check active account (for upload middleware)
  static checkActiveAccount() {
    return async (req, res, next) => {
      try {
        if (!req.session.userId) {
          throw createAuthError('Authentication required');
        }

        // Check if user is admin (unlimited access)
        const isAdmin = await this.checkAdminStatus(req.session.userId);
        if (isAdmin) {
          return next();
        }

        // Get user data to check account status
        const userQuotaData = await this.getCombinedUserQuotaData(req.session.userId);

        // Validate subscription for active account
        this.validateSubscription(userQuotaData);

        next();
      } catch (error) {
        return errorHandlerMiddleware(error, req, res, next);
      }
    };
  }

  // Update storage usage after upload
  static updateStorageUsage() {
    return async (req, res, next) => {
      try {
        if (req.uploadSizeGB && req.session.userId) {
          const User = require('../models/User');
          await User.updateStorageUsage(req.session.userId, req.uploadSizeGB);

          // Clear cache for this user since storage data changed
          this.clearUserCache(req.session.userId);
        }
        next();
      } catch (error) {
        console.error('Error updating storage usage:', error);
        next(); // Don't fail the request for storage update errors
      }
    };
  }

  // Get Google Drive eligible plans
  static async getGoogleDriveEligiblePlans() {
    try {
      const { db } = require('../db/database');

      return new Promise((resolve, reject) => {
        db.all(`
          SELECT id, name, price, features
          FROM subscription_plans
          WHERE price >= 49000 AND name != 'Preview'
          ORDER BY price ASC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
    } catch (error) {
      console.error('Error getting Google Drive eligible plans:', error);
      return [];
    }
  }

  // Get advanced settings eligible plans
  static async getAdvancedSettingsEligiblePlans() {
    try {
      const { db } = require('../db/database');

      return new Promise((resolve, reject) => {
        db.all(`
          SELECT id, name, price, features
          FROM subscription_plans
          WHERE price >= 49900 AND name != 'Preview'
          ORDER BY price ASC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });
    } catch (error) {
      console.error('Error getting advanced settings eligible plans:', error);
      return [];
    }
  }

  // Check subscription (legacy method)
  static checkSubscription() {
    return this.checkValidSubscription();
  }

  // Sync user storage cache
  static async syncUserStorageCache(userId, actualStorageGB) {
    try {
      const { db } = require('../db/database');

      return new Promise((resolve, reject) => {
        db.run(
          'UPDATE users SET used_storage_gb = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [actualStorageGB, userId],
          function(err) {
            if (err) {
              console.error('Error syncing user storage cache:', err);
              reject(err);
            } else {
              // Clear cache for this user since storage data changed
              quotaCache.clearUserCache(userId);
              resolve(this.changes);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error in syncUserStorageCache:', error);
      throw error;
    }
  }

  // Sync all users storage cache (for maintenance)
  static async syncAllUsersStorageCache() {
    const { db } = require('../db/database');
    console.log('🔄 Starting bulk storage cache sync...');
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT u.id, u.used_storage_gb as cached_storage,
         COALESCE(SUM(v.file_size_gb), 0) as actual_storage
         FROM users u
         LEFT JOIN videos v ON u.id = v.user_id
         GROUP BY u.id
         HAVING ABS(cached_storage - actual_storage) > 0.01`,
        [],
        (err, rows) => {
          if (err) {
            console.error('❌ Error in bulk storage sync:', err);
            reject(err);
          } else {
            console.log(`✅ Bulk storage sync completed. Updated ${rows.length} users.`);
            // Clear all cache since we updated storage data
            quotaCache.clear();
            resolve(rows);
          }
        }
      );
    });
  }
}

module.exports = OptimizedQuotaMiddleware;
