#!/usr/bin/env node

/**
 * YouTube Stream Monitor
 * Monitors YouTube streams for unexpected terminations and provides detailed analysis
 */

const fs = require('fs');
const path = require('path');

console.log('📺 YouTube Stream Monitor');
console.log('========================\n');

class YouTubeStreamMonitor {
  constructor() {
    this.monitoringActive = false;
    this.streamData = new Map();
    this.logFile = path.join(__dirname, 'logs', 'youtube-monitor.log');
  }

  async startMonitoring() {
    console.log('🚀 Starting YouTube stream monitoring...\n');
    this.monitoringActive = true;

    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Monitor every 30 seconds
    const monitorInterval = setInterval(async () => {
      if (!this.monitoringActive) {
        clearInterval(monitorInterval);
        return;
      }

      try {
        await this.checkStreams();
      } catch (error) {
        this.log(`ERROR: Monitoring failed: ${error.message}`);
      }
    }, 30000); // 30 seconds

    // Initial check
    await this.checkStreams();

    console.log('✅ YouTube stream monitoring started');
    console.log('📊 Monitoring every 30 seconds');
    console.log(`📝 Logs saved to: ${this.logFile}`);
    console.log('🛑 Press Ctrl+C to stop monitoring\n');

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping YouTube stream monitoring...');
      this.monitoringActive = false;
      process.exit(0);
    });
  }

  async checkStreams() {
    try {
      const { db } = require('./db/database');
      
      // Get all live YouTube streams
      const youtubeStreams = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, platform, rtmp_url, status, start_time, duration, created_at
          FROM streams 
          WHERE status = 'live' 
          AND (platform = 'YouTube' OR rtmp_url LIKE '%youtube.com%')
          ORDER BY start_time DESC
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      const currentTime = new Date();
      
      for (const stream of youtubeStreams) {
        const streamId = stream.id;
        const startTime = new Date(stream.start_time);
        const runningTime = Math.floor((currentTime - startTime) / 1000 / 60); // minutes
        
        // Store/update stream data
        const previousData = this.streamData.get(streamId);
        const currentData = {
          id: streamId,
          title: stream.title,
          startTime: startTime,
          runningTime: runningTime,
          duration: stream.duration,
          lastChecked: currentTime,
          status: 'live'
        };

        this.streamData.set(streamId, currentData);

        // Check for potential issues
        await this.analyzeStream(currentData, previousData);
      }

      // Clean up data for streams that are no longer live
      const liveStreamIds = new Set(youtubeStreams.map(s => s.id));
      for (const [streamId, data] of this.streamData.entries()) {
        if (!liveStreamIds.has(streamId)) {
          this.log(`STREAM_ENDED: ${data.title} (${streamId}) - Total runtime: ${data.runningTime} minutes`);
          this.streamData.delete(streamId);
        }
      }

      // Display current status
      this.displayStatus(youtubeStreams);

    } catch (error) {
      this.log(`ERROR: Failed to check streams: ${error.message}`);
    }
  }

  async analyzeStream(currentData, previousData) {
    const { id, title, runningTime, duration } = currentData;

    // Check for duration-based termination risk
    if (duration && duration > 0) {
      const remainingTime = duration - runningTime;
      if (remainingTime <= 30 && remainingTime > 0) {
        this.log(`WARNING: ${title} (${id}) will auto-terminate in ${remainingTime} minutes due to duration limit`);
        console.log(`⚠️  WARNING: Stream "${title}" will auto-terminate in ${remainingTime} minutes`);
      }
    }

    // Check for long-running streams (potential YouTube disconnection risk)
    if (runningTime > 720) { // 12 hours
      this.log(`LONG_RUNNING: ${title} (${id}) has been running for ${runningTime} minutes (${Math.floor(runningTime/60)} hours)`);
      
      if (runningTime % 60 === 0) { // Log every hour after 12 hours
        console.log(`📊 Long-running stream: "${title}" - ${Math.floor(runningTime/60)} hours`);
      }
    }

    // Check for streams approaching common YouTube limits
    const hoursRunning = Math.floor(runningTime / 60);
    if ([12, 18, 24].includes(hoursRunning) && runningTime % 60 < 2) {
      this.log(`YOUTUBE_LIMIT_APPROACHING: ${title} (${id}) approaching ${hoursRunning}h mark - YouTube may disconnect`);
      console.log(`⚠️  YouTube Limit Alert: "${title}" approaching ${hoursRunning}h mark`);
    }

    // Log milestone hours
    if (previousData && Math.floor(runningTime / 60) > Math.floor(previousData.runningTime / 60)) {
      const hours = Math.floor(runningTime / 60);
      this.log(`MILESTONE: ${title} (${id}) reached ${hours} hour${hours > 1 ? 's' : ''} of streaming`);
      console.log(`🎉 Milestone: "${title}" reached ${hours} hour${hours > 1 ? 's' : ''}`);
    }
  }

  displayStatus(streams) {
    // Clear console and show current status
    process.stdout.write('\x1B[2J\x1B[0f'); // Clear screen
    
    console.log('📺 YouTube Stream Monitor');
    console.log('========================\n');
    
    if (streams.length === 0) {
      console.log('📭 No active YouTube streams found\n');
    } else {
      console.log(`📊 Monitoring ${streams.length} active YouTube stream${streams.length > 1 ? 's' : ''}:\n`);
      
      streams.forEach((stream, index) => {
        const startTime = new Date(stream.start_time);
        const runningTime = Math.floor((new Date() - startTime) / 1000 / 60);
        const hours = Math.floor(runningTime / 60);
        const minutes = runningTime % 60;
        
        let durationInfo = '';
        if (stream.duration && stream.duration > 0) {
          const remaining = stream.duration - runningTime;
          durationInfo = remaining > 0 
            ? ` (${remaining}min remaining)` 
            : ' (EXCEEDED DURATION!)';
        } else {
          durationInfo = ' (indefinite)';
        }
        
        console.log(`${index + 1}. "${stream.title}"`);
        console.log(`   Runtime: ${hours}h ${minutes}m${durationInfo}`);
        console.log(`   Started: ${startTime.toLocaleString()}`);
        console.log('');
      });
    }
    
    console.log(`Last check: ${new Date().toLocaleTimeString()}`);
    console.log('🔄 Next check in 30 seconds...\n');
    console.log('🛑 Press Ctrl+C to stop monitoring');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  async generateReport() {
    console.log('📋 Generating YouTube Stream Report...\n');
    
    try {
      const { db } = require('./db/database');
      
      // Get recent YouTube streams
      const recentStreams = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, platform, rtmp_url, status, start_time, end_time, duration, created_at
          FROM streams 
          WHERE (platform = 'YouTube' OR rtmp_url LIKE '%youtube.com%')
          AND created_at > datetime('now', '-7 days')
          ORDER BY created_at DESC
          LIMIT 50
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      console.log(`📊 YouTube Stream Report (Last 7 Days)`);
      console.log(`=====================================\n`);
      console.log(`Total YouTube streams: ${recentStreams.length}\n`);

      const statusCounts = {};
      const durationStats = [];
      
      recentStreams.forEach(stream => {
        statusCounts[stream.status] = (statusCounts[stream.status] || 0) + 1;
        
        if (stream.start_time && stream.end_time) {
          const duration = (new Date(stream.end_time) - new Date(stream.start_time)) / 1000 / 60;
          durationStats.push(duration);
        }
      });

      console.log('📈 Status Distribution:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`   ${status}: ${count}`);
      });

      if (durationStats.length > 0) {
        const avgDuration = durationStats.reduce((a, b) => a + b, 0) / durationStats.length;
        const maxDuration = Math.max(...durationStats);
        
        console.log('\n⏱️  Duration Statistics:');
        console.log(`   Average: ${Math.round(avgDuration)} minutes (${Math.round(avgDuration/60)} hours)`);
        console.log(`   Maximum: ${Math.round(maxDuration)} minutes (${Math.round(maxDuration/60)} hours)`);
      }

      // Check for streams with duration limits
      const streamsWithLimits = recentStreams.filter(s => s.duration && s.duration > 0);
      if (streamsWithLimits.length > 0) {
        console.log(`\n⚠️  Streams with Duration Limits: ${streamsWithLimits.length}`);
        console.log('   Recommendation: Remove duration limits for indefinite YouTube streaming');
      }

    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  }
}

// Command line interface
if (require.main === module) {
  const monitor = new YouTubeStreamMonitor();
  
  const command = process.argv[2];
  
  if (command === 'report') {
    monitor.generateReport().catch(console.error);
  } else {
    monitor.startMonitoring().catch(console.error);
  }
}

module.exports = YouTubeStreamMonitor;
