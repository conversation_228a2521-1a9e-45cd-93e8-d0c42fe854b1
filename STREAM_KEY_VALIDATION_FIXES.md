# Stream Key Validation Fixes

## Problem Description

Users were encountering "Creation Failed - Please use a different stream key. This one is already in use." errors even when using unique stream keys that should not be in use.

## Root Causes Identified

1. **Overly Broad Validation**: The original `isStreamKeyInUse` method was checking all streams regardless of their status, including old inactive streams.

2. **No Status Filtering**: Streams with status 'error' or very old 'offline' streams were still being considered as "in use".

3. **Lack of Debugging Information**: No way to see what streams were causing conflicts.

4. **No Cleanup Mechanism**: Old inactive streams were accumulating in the database.

5. **Cache Invalidation Issues**: Stream deletions weren't properly invalidating related cache entries.

## Implemented Fixes

### 1. Enhanced `isStreamKeyInUse` Method

**File**: `models/Stream.js`

- Added configurable options for different validation modes
- Added status filtering to exclude inactive streams by default
- Added debug mode for detailed logging
- Added global vs user-specific checking options

```javascript
static async isStreamKeyInUse(streamKey, userId, excludeId = null, options = {}) {
  const {
    globalCheck = false,        // Check across all users
    includeInactive = false,    // Include offline/error streams
    debugMode = false          // Enable detailed logging
  } = options;
  
  // Only checks active statuses by default: 'offline', 'live', 'scheduled', 'starting'
  // Excludes old 'error' streams and other inactive states
}
```

### 2. New Debug Method

**File**: `models/Stream.js`

Added `getStreamsWithKey` method to get detailed information about streams using a specific key:

```javascript
static async getStreamsWithKey(streamKey, userId = null, options = {})
```

### 3. Stream Cleanup Functionality

**File**: `models/Stream.js`

Added `cleanupOldStreams` method to remove old inactive streams:

```javascript
static async cleanupOldStreams(options = {}) {
  const {
    olderThanDays = 30,     // Remove streams older than X days
    statusesToClean = ['error', 'offline'], // Only clean these statuses
    dryRun = false          // If true, only count what would be deleted
  } = options;
}
```

### 4. Enhanced Stream Creation Logic

**File**: `app.js`

- Updated stream creation to use enhanced validation
- Added debug logging for development environments
- Added detailed conflict information when validation fails

### 5. Improved Cache Invalidation

**File**: `models/Stream.js`

- Enhanced stream deletion to properly invalidate cache entries
- Prevents stale cache data from affecting validation

### 6. Admin Debug Endpoints

**File**: `app.js`

Added two new admin endpoints for debugging and maintenance:

#### `/api/admin/debug-stream-key` (POST)
- Debug specific stream key conflicts
- Shows all validation modes and results
- Lists all streams using the key
- Can trigger cleanup

#### `/api/admin/cleanup-streams` (POST)
- Clean up old inactive streams
- Supports dry-run mode
- Configurable age threshold

## Testing

### Automated Test Script

Created `test-stream-key-validation.js` to verify all fixes work correctly:

```bash
node test-stream-key-validation.js
```

### Manual Testing Steps

1. **Test Unique Key Acceptance**:
   - Create a stream with a genuinely unique key
   - Should succeed without errors

2. **Test Duplicate Key Rejection**:
   - Try to create another stream with the same key
   - Should fail with appropriate error

3. **Test Old Stream Cleanup**:
   - Create streams and set them to 'error' status
   - Run cleanup to remove old streams
   - Verify keys become available again

4. **Test Debug Endpoints** (Admin only):
   ```bash
   # Debug a specific stream key
   curl -X POST /api/admin/debug-stream-key \
     -H "Content-Type: application/json" \
     -d '{"streamKey": "your-test-key", "userId": "user-id"}'
   
   # Cleanup old streams (dry run)
   curl -X POST /api/admin/cleanup-streams \
     -H "Content-Type: application/json" \
     -d '{"olderThanDays": 30, "dryRun": true}'
   ```

## Configuration Options

### Environment Variables

- `NODE_ENV=production` - Disables debug logging
- `NODE_ENV=development` - Enables detailed debug logging

### Default Behavior Changes

- **Before**: Checked all streams regardless of status
- **After**: Only checks active streams ('offline', 'live', 'scheduled', 'starting')
- **Impact**: Reduces false positives from old inactive streams

## Monitoring and Maintenance

### Regular Cleanup

Consider running stream cleanup periodically:

```javascript
// Clean up streams older than 30 days with error/offline status
await Stream.cleanupOldStreams({
  olderThanDays: 30,
  statusesToClean: ['error', 'offline'],
  dryRun: false
});
```

### Debug Logging

In development mode, detailed logs show:
- SQL queries being executed
- Parameters being used (stream keys are hidden)
- Validation results
- Conflicting stream information

## Expected Results

After implementing these fixes:

1. ✅ **Reduced False Positives**: Users should no longer get "already in use" errors for genuinely unique keys
2. ✅ **Better Debugging**: Admins can investigate stream key conflicts
3. ✅ **Automatic Cleanup**: Old inactive streams don't interfere with new stream creation
4. ✅ **Improved Performance**: More efficient queries with proper status filtering
5. ✅ **Better Cache Management**: Proper cache invalidation prevents stale data issues

## Rollback Plan

If issues occur, the changes can be rolled back by:

1. Reverting the `isStreamKeyInUse` method to its original form
2. Removing the new admin endpoints
3. Restoring the original stream creation validation logic

The database schema remains unchanged, so no data migration is needed for rollback.
