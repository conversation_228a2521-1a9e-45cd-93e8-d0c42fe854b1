#!/usr/bin/env node

/**
 * 24-Hour Streaming Verification Tool
 * Comprehensive monitoring to ensure streams can run continuously
 */

const fs = require('fs');
const path = require('path');
const { db } = require('./db/database');

console.log('🔍 24-Hour Streaming Verification Tool');
console.log('======================================\n');

class StreamingVerification {
  constructor() {
    this.logFile = path.join(__dirname, 'logs', '24h-streaming-verification.log');
    this.monitoringActive = false;
    this.streamData = new Map();
    this.verificationResults = {
      totalStreamsMonitored: 0,
      longestStreamDuration: 0,
      terminationEvents: [],
      hourlyMilestones: new Map(),
      systemHealth: []
    };
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    
    // Ensure log directory exists
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(this.logFile, logEntry + '\n');
  }

  async startVerification() {
    console.log('🚀 Starting 24-hour streaming verification...\n');
    this.monitoringActive = true;

    // Initial system check
    await this.performSystemCheck();

    // Monitor every 60 seconds for detailed tracking
    const monitorInterval = setInterval(async () => {
      if (!this.monitoringActive) {
        clearInterval(monitorInterval);
        return;
      }

      try {
        await this.checkStreams();
        await this.checkSystemHealth();
      } catch (error) {
        this.log(`ERROR: Monitoring failed: ${error.message}`);
      }
    }, 60000); // 60 seconds

    // Generate hourly reports
    const reportInterval = setInterval(() => {
      if (!this.monitoringActive) {
        clearInterval(reportInterval);
        return;
      }
      this.generateHourlyReport();
    }, 3600000); // 1 hour

    this.log('24-hour streaming verification started');
    console.log('📊 Monitoring active streams every 60 seconds...');
    console.log('📈 Generating hourly reports...');
    console.log('📝 Logs are being written to:', this.logFile);
    console.log('Press Ctrl+C to stop verification\n');
  }

  async performSystemCheck() {
    console.log('🔧 PERFORMING INITIAL SYSTEM CHECK...\n');

    // Check 1: Verify scheduler duration checking is disabled
    try {
      const schedulerPath = './services/schedulerService.js';
      const content = fs.readFileSync(schedulerPath, 'utf8');
      
      if (content.includes('// const durationInterval = setInterval(checkStreamDurations')) {
        console.log('✅ Scheduler duration checking is DISABLED');
        this.log('SYSTEM_CHECK: Scheduler duration checking disabled - GOOD');
      } else if (content.includes('setInterval(checkStreamDurations')) {
        console.log('🚨 WARNING: Scheduler duration checking is still ACTIVE!');
        this.log('SYSTEM_CHECK: Scheduler duration checking ACTIVE - CRITICAL ISSUE');
      }
    } catch (error) {
      console.log('❌ Could not verify scheduler configuration');
    }

    // Check 2: Verify cleanup interval
    try {
      const streamingPath = './services/streamingService.js';
      const content = fs.readFileSync(streamingPath, 'utf8');
      const match = content.match(/CLEANUP_INTERVAL\s*=\s*(\d+)/);
      
      if (match) {
        const intervalMs = parseInt(match[1]);
        const intervalHours = intervalMs / (1000 * 60 * 60);
        console.log(`✅ Cleanup interval: ${intervalHours} hours`);
        this.log(`SYSTEM_CHECK: Cleanup interval set to ${intervalHours} hours`);
      }
    } catch (error) {
      console.log('❌ Could not verify cleanup interval');
    }

    // Check 3: Verify no duration limits in database
    try {
      const streamsWithDuration = await new Promise((resolve, reject) => {
        db.all(`
          SELECT COUNT(*) as count
          FROM streams 
          WHERE duration IS NOT NULL AND duration > 0
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows[0]?.count || 0);
        });
      });

      if (streamsWithDuration === 0) {
        console.log('✅ No duration limits found in database');
        this.log('SYSTEM_CHECK: No duration limits in database - GOOD');
      } else {
        console.log(`🚨 WARNING: ${streamsWithDuration} streams still have duration limits!`);
        this.log(`SYSTEM_CHECK: ${streamsWithDuration} streams with duration limits - NEEDS FIX`);
      }
    } catch (error) {
      console.log('❌ Could not check database for duration limits');
    }

    console.log('\n📊 System check completed\n');
  }

  async checkStreams() {
    try {
      const liveStreams = await this.getLiveStreams();
      const currentTime = new Date();
      
      for (const stream of liveStreams) {
        const streamId = stream.id;
        const startTime = new Date(stream.start_time);
        const runningTimeMinutes = Math.floor((currentTime - startTime) / 1000 / 60);
        const runningTimeHours = runningTimeMinutes / 60;
        
        // Store/update stream data
        const previousData = this.streamData.get(streamId);
        const currentData = {
          id: streamId,
          title: stream.title,
          startTime: startTime,
          runningTimeMinutes: runningTimeMinutes,
          runningTimeHours: runningTimeHours,
          lastChecked: currentTime,
          status: 'live'
        };

        this.streamData.set(streamId, currentData);

        // Track longest running stream
        if (runningTimeMinutes > this.verificationResults.longestStreamDuration) {
          this.verificationResults.longestStreamDuration = runningTimeMinutes;
        }

        // Check for hourly milestones
        const currentHour = Math.floor(runningTimeHours);
        const previousHour = previousData ? Math.floor(previousData.runningTimeHours) : -1;
        
        if (currentHour > previousHour && currentHour > 0) {
          this.log(`MILESTONE: "${stream.title}" reached ${currentHour} hour${currentHour > 1 ? 's' : ''} of continuous streaming`);
          console.log(`🎉 MILESTONE: "${stream.title}" - ${currentHour} hour${currentHour > 1 ? 's' : ''} continuous!`);
          
          // Track milestone
          if (!this.verificationResults.hourlyMilestones.has(currentHour)) {
            this.verificationResults.hourlyMilestones.set(currentHour, []);
          }
          this.verificationResults.hourlyMilestones.get(currentHour).push({
            streamId: streamId,
            title: stream.title,
            timestamp: currentTime
          });
        }

        // Alert for critical milestones
        if ([1, 2, 6, 12, 24].includes(currentHour) && currentHour > previousHour) {
          console.log(`🚀 CRITICAL MILESTONE: "${stream.title}" has been streaming for ${currentHour} hours continuously!`);
        }
      }

      // Check for terminated streams
      for (const [streamId, data] of this.streamData.entries()) {
        const stillExists = liveStreams.some(s => s.id === streamId);
        if (!stillExists && data.status === 'live') {
          // Stream was terminated
          const terminationEvent = {
            streamId: streamId,
            title: data.title,
            startTime: data.startTime,
            runningTimeMinutes: data.runningTimeMinutes,
            runningTimeHours: data.runningTimeHours,
            terminatedAt: currentTime
          };
          
          this.verificationResults.terminationEvents.push(terminationEvent);
          this.log(`TERMINATION: "${data.title}" terminated after ${data.runningTimeMinutes} minutes (${data.runningTimeHours.toFixed(1)} hours)`);
          
          // Check if this was a critical termination
          if (data.runningTimeMinutes >= 55 && data.runningTimeMinutes <= 65) {
            console.log(`🚨 CRITICAL: Stream terminated at ~1 hour mark! (${data.runningTimeMinutes} minutes)`);
            this.log(`CRITICAL_TERMINATION: 1-hour termination bug detected - ${data.runningTimeMinutes} minutes`);
          }
          
          // Update local data
          data.status = 'terminated';
        }
      }

      // Update total streams monitored
      this.verificationResults.totalStreamsMonitored = Math.max(
        this.verificationResults.totalStreamsMonitored,
        liveStreams.length
      );

    } catch (error) {
      this.log(`ERROR: Failed to check streams: ${error.message}`);
    }
  }

  async checkSystemHealth() {
    // Simple system health check
    const memoryUsage = process.memoryUsage();
    const healthData = {
      timestamp: new Date(),
      memoryUsage: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) // MB
      },
      activeStreams: this.streamData.size
    };

    this.verificationResults.systemHealth.push(healthData);

    // Keep only last 24 hours of health data
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    this.verificationResults.systemHealth = this.verificationResults.systemHealth.filter(
      h => h.timestamp > oneDayAgo
    );
  }

  generateHourlyReport() {
    const now = new Date();
    console.log(`\n📊 HOURLY REPORT - ${now.toLocaleString()}`);
    console.log('='.repeat(50));
    
    const activeStreams = Array.from(this.streamData.values()).filter(s => s.status === 'live');
    
    console.log(`📈 Active Streams: ${activeStreams.length}`);
    console.log(`⏱️  Longest Stream: ${Math.floor(this.verificationResults.longestStreamDuration / 60)} hours ${this.verificationResults.longestStreamDuration % 60} minutes`);
    console.log(`🚨 Terminations: ${this.verificationResults.terminationEvents.length}`);
    
    if (activeStreams.length > 0) {
      console.log('\n🎯 Current Active Streams:');
      activeStreams.forEach((stream, index) => {
        const hours = Math.floor(stream.runningTimeHours);
        const minutes = Math.floor((stream.runningTimeHours % 1) * 60);
        console.log(`   ${index + 1}. "${stream.title}" - ${hours}h ${minutes}m`);
      });
    }
    
    console.log('\n');
  }

  async getLiveStreams() {
    return new Promise((resolve, reject) => {
      db.all(`
        SELECT id, title, start_time, duration, user_id, status
        FROM streams 
        WHERE status = 'live'
        ORDER BY start_time ASC
      `, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  generateFinalReport() {
    console.log('\n📊 24-HOUR STREAMING VERIFICATION REPORT');
    console.log('=========================================\n');

    console.log(`📈 Total Streams Monitored: ${this.verificationResults.totalStreamsMonitored}`);
    console.log(`⏱️  Longest Continuous Stream: ${Math.floor(this.verificationResults.longestStreamDuration / 60)} hours ${this.verificationResults.longestStreamDuration % 60} minutes`);
    console.log(`🚨 Total Terminations: ${this.verificationResults.terminationEvents.length}`);

    if (this.verificationResults.terminationEvents.length > 0) {
      console.log('\n🚨 TERMINATION EVENTS:');
      this.verificationResults.terminationEvents.forEach((event, index) => {
        console.log(`${index + 1}. "${event.title}"`);
        console.log(`   Duration: ${Math.floor(event.runningTimeHours)} hours ${event.runningTimeMinutes % 60} minutes`);
        console.log(`   Terminated: ${event.terminatedAt.toLocaleString()}`);
        console.log('');
      });
    }

    // Check if 24-hour streaming is verified
    if (this.verificationResults.longestStreamDuration >= 1440) { // 24 hours
      console.log('🎉 SUCCESS: 24-hour continuous streaming VERIFIED!');
    } else if (this.verificationResults.longestStreamDuration >= 60) { // 1 hour
      console.log('✅ PROGRESS: Streams can run beyond 1 hour - 24/7 capability likely achieved');
    } else {
      console.log('⚠️  WARNING: No streams have run longer than 1 hour yet');
    }
  }

  stopVerification() {
    this.monitoringActive = false;
    this.log('24-hour streaming verification stopped');
    this.generateFinalReport();
  }
}

// Handle graceful shutdown
const verification = new StreamingVerification();

process.on('SIGINT', () => {
  console.log('\n🛑 Stopping verification...');
  verification.stopVerification();
  process.exit(0);
});

process.on('SIGTERM', () => {
  verification.stopVerification();
  process.exit(0);
});

// Start verification
verification.startVerification().catch(error => {
  console.error('Failed to start verification:', error);
  process.exit(1);
});
