#!/usr/bin/env node

/**
 * Analyze Verification Log Timestamps
 * Detailed analysis of the specific log entries showing 8-hour duration
 */

const fs = require('fs');

console.log('🔍 Verification Log Analysis');
console.log('============================\n');

function analyzeLogTimestamps() {
  try {
    // Read the verification log
    const logContent = fs.readFileSync('./logs/24h-streaming-verification.log', 'utf8');
    const logLines = logContent.split('\n').filter(line => line.trim());
    
    console.log('📊 VERIFICATION LOG ENTRIES:');
    console.log('============================');
    
    logLines.forEach((line, index) => {
      console.log(`${index + 1}. ${line}`);
    });
    
    console.log('\n🔍 DETAILED TIMESTAMP ANALYSIS:');
    console.log('===============================');
    
    // Extract the specific entries about MusicCow
    const milestoneEntry = logLines.find(line => line.includes('MILESTONE') && line.includes('MusicCow'));
    const terminationEntry = logLines.find(line => line.includes('TERMINATION') && line.includes('MusicCow'));
    
    if (milestoneEntry && terminationEntry) {
      console.log('Found MusicCow entries:');
      console.log(`Milestone: ${milestoneEntry}`);
      console.log(`Termination: ${terminationEntry}`);
      
      // Extract timestamps
      const milestoneTimestamp = milestoneEntry.match(/\[(.*?)\]/)[1];
      const terminationTimestamp = terminationEntry.match(/\[(.*?)\]/)[1];
      
      console.log(`\nTimestamp Analysis:`);
      console.log(`Milestone logged at: ${milestoneTimestamp}`);
      console.log(`Termination logged at: ${terminationTimestamp}`);
      
      // Calculate the time between log entries
      const milestoneTime = new Date(milestoneTimestamp);
      const terminationTime = new Date(terminationTimestamp);
      const logTimeDiff = Math.floor((terminationTime - milestoneTime) / 1000 / 60); // minutes
      
      console.log(`Time between log entries: ${logTimeDiff} minutes`);
      
      // Extract the reported duration from termination entry
      const durationMatch = terminationEntry.match(/(\d+) minutes \(([\d.]+) hours\)/);
      if (durationMatch) {
        const reportedMinutes = parseInt(durationMatch[1]);
        const reportedHours = parseFloat(durationMatch[2]);
        
        console.log(`\nReported Duration Analysis:`);
        console.log(`Reported: ${reportedMinutes} minutes (${reportedHours} hours)`);
        console.log(`Log time difference: ${logTimeDiff} minutes`);
        
        if (logTimeDiff < 120 && reportedMinutes > 400) { // Log entries less than 2 hours apart but reporting 6+ hours
          console.log(`🚨 CRITICAL DISCREPANCY DETECTED!`);
          console.log(`   - Log entries are only ${logTimeDiff} minutes apart`);
          console.log(`   - But system reported ${reportedMinutes} minutes of streaming`);
          console.log(`   - This suggests the start_time in database was incorrect`);
          
          // Calculate what the start_time would have been
          const calculatedStartTime = new Date(terminationTime.getTime() - (reportedMinutes * 60 * 1000));
          console.log(`   - For ${reportedMinutes} minutes duration, start_time would be: ${calculatedStartTime.toISOString()}`);
          
          // Check if this matches any of the earlier log timestamps
          const systemCheckTime = new Date('2025-07-28T20:25:33.779Z'); // From first log entry
          const startTimeDiff = Math.abs(calculatedStartTime - systemCheckTime) / 1000 / 60;
          
          if (startTimeDiff < 5) {
            console.log(`   🎯 LIKELY CAUSE: start_time was set to system check time instead of actual stream start`);
            console.log(`   - System check: ${systemCheckTime.toISOString()}`);
            console.log(`   - Calculated start: ${calculatedStartTime.toISOString()}`);
            console.log(`   - Difference: ${startTimeDiff.toFixed(1)} minutes`);
          }
        }
      }
    } else {
      console.log('❌ Could not find MusicCow entries in log');
    }
    
    console.log('\n📋 ANALYSIS SUMMARY:');
    console.log('====================');
    
    // Check for patterns in all log entries
    const allTimestamps = logLines.map(line => {
      const match = line.match(/\[(.*?)\]/);
      return match ? new Date(match[1]) : null;
    }).filter(Boolean);
    
    if (allTimestamps.length > 1) {
      const firstTimestamp = allTimestamps[0];
      const lastTimestamp = allTimestamps[allTimestamps.length - 1];
      const totalLogTime = Math.floor((lastTimestamp - firstTimestamp) / 1000 / 60);
      
      console.log(`Total monitoring time: ${totalLogTime} minutes`);
      console.log(`First log entry: ${firstTimestamp.toISOString()}`);
      console.log(`Last log entry: ${lastTimestamp.toISOString()}`);
      
      if (totalLogTime < 120 && logLines.some(line => line.includes('479 minutes'))) {
        console.log(`\n🚨 CONFIRMED DISCREPANCY:`);
        console.log(`- Monitoring ran for only ${totalLogTime} minutes`);
        console.log(`- But reported stream duration of 479 minutes (8 hours)`);
        console.log(`- This proves the duration calculation is using incorrect start_time`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Log analysis failed: ${error.message}`);
  }
}

function investigateStartTimeIssue() {
  console.log('\n🔍 INVESTIGATING START_TIME ISSUE:');
  console.log('==================================');
  
  console.log('Possible causes of incorrect start_time:');
  console.log('1. start_time set during stream creation instead of when stream goes live');
  console.log('2. start_time not updated when stream actually starts outputting');
  console.log('3. Timezone conversion issues in start_time storage');
  console.log('4. Database corruption affecting timestamp fields');
  console.log('5. Race condition where start_time is set before stream is ready');
  
  console.log('\nTo verify the actual issue:');
  console.log('1. Check when start_time is set in Stream.updateStatus()');
  console.log('2. Compare start_time with actual stream start in logs');
  console.log('3. Monitor real-time stream creation vs start_time setting');
  console.log('4. Verify timezone handling in getCurrentUTC()');
}

function provideSolution() {
  console.log('\n🔧 RECOMMENDED SOLUTION:');
  console.log('========================');
  
  console.log('Based on the analysis, the issue appears to be:');
  console.log('- start_time is being set incorrectly (too early)');
  console.log('- This causes duration calculations to show inflated times');
  console.log('- The 59-minute termination may still be occurring');
  
  console.log('\nImmediate actions needed:');
  console.log('1. Fix start_time setting to use actual stream start time');
  console.log('2. Add validation to ensure start_time accuracy');
  console.log('3. Implement real-time duration monitoring');
  console.log('4. Test with actual stream to verify fix');
  
  console.log('\nThis explains why:');
  console.log('- Logs show 8 hours but stream was only 1 hour');
  console.log('- The 59-minute termination issue may not be actually fixed');
  console.log('- Duration calculations are unreliable for diagnosis');
}

// Run the analysis
analyzeLogTimestamps();
investigateStartTimeIssue();
provideSolution();
