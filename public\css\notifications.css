/* Modern Notification System Styles */

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 99999;
  max-width: 400px;
  pointer-events: none;
}

.toast {
  background: linear-gradient(135deg, rgba(37, 37, 37, 0.95), rgba(18, 18, 18, 0.95));
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
  border-left: 4px solid;
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast.hide {
  transform: translateX(100%);
  opacity: 0;
}

.toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.toast-success {
  border-left-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(37, 37, 37, 0.95));
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(16, 185, 129, 0.1);
}

.toast-error {
  border-left-color: #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(37, 37, 37, 0.95));
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(239, 68, 68, 0.1);
}

.toast-warning {
  border-left-color: #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(37, 37, 37, 0.95));
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(245, 158, 11, 0.1);
}

.toast-info {
  border-left-color: #ad6610;
  background: linear-gradient(135deg, rgba(173, 102, 16, 0.15), rgba(37, 37, 37, 0.95));
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 30px rgba(173, 102, 16, 0.1);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-top: 2px;
}

.toast-success .toast-icon {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.toast-error .toast-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.toast-warning .toast-icon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.toast-info .toast-icon {
  background: rgba(173, 102, 16, 0.2);
  color: #ad6610;
}

.toast-body {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.3;
}

.toast-message {
  color: #d1d5db;
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.toast-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 16px;
  line-height: 1;
}

.toast-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

/* Progress bar for auto-dismiss */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0 0 12px 12px;
  transform-origin: left;
  animation: progress 5s linear forwards;
}

@keyframes progress {
  from { transform: scaleX(1); }
  to { transform: scaleX(0); }
}

/* Modal Dialogs */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 50000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-dialog {
  background: linear-gradient(135deg, #252525 0%, #121212 100%);
  border-radius: 20px;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(173, 102, 16, 0.2);
  backdrop-filter: blur(20px);
}

.modal-overlay.show .modal-dialog {
  transform: scale(1) translateY(0);
}

.modal-header {
  padding: 28px 28px 20px;
  border-bottom: 1px solid rgba(173, 102, 16, 0.2);
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(18, 18, 18, 0.5);
  backdrop-filter: blur(10px);
}

.modal-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.modal-success .modal-icon {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.modal-error .modal-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.modal-warning .modal-icon {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.modal-info .modal-icon {
  background: rgba(173, 102, 16, 0.2);
  color: #ad6610;
}

.modal-question .modal-icon {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
}

.modal-body {
  padding: 20px 28px 28px;
  background: rgba(37, 37, 37, 0.3);
}

.modal-message {
  color: #e5e5e5;
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.modal-footer {
  padding: 20px 28px 28px;
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  background: rgba(18, 18, 18, 0.3);
  border-top: 1px solid rgba(173, 102, 16, 0.1);
}

.modal-btn {
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  min-width: 100px;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modal-btn-primary {
  background: linear-gradient(135deg, #ad6610 0%, #d17f14 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(173, 102, 16, 0.3);
}

.modal-btn-primary:hover {
  background: linear-gradient(135deg, #d17f14 0%, #ad6610 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(173, 102, 16, 0.4);
}

.modal-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.modal-btn-primary:hover::before {
  left: 100%;
}

.modal-btn-secondary {
  background: rgba(45, 45, 45, 0.8);
  color: #e5e5e5;
  border: 2px solid rgba(173, 102, 16, 0.3);
  backdrop-filter: blur(10px);
}

.modal-btn-secondary:hover {
  background: rgba(173, 102, 16, 0.1);
  color: #ffffff;
  border-color: rgba(173, 102, 16, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(173, 102, 16, 0.2);
}

.modal-btn-danger {
  background: #ef4444;
  color: white;
}

.modal-btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Loading spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Input styles for modals */
.modal-input {
  width: 100%;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  transition: all 0.2s;
}

.modal-input:focus {
  outline: none;
  border-color: #ad6610;
  background: rgba(255, 255, 255, 0.1);
}

.modal-input::placeholder {
  color: #9ca3af;
}

/* Textarea styles */
.modal-textarea {
  min-height: 80px;
  resize: vertical;
}

/* Checkbox styles */
.modal-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #ad6610;
}

/* Select styles */
.modal-select {
  width: 100%;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

.modal-select:focus {
  outline: none;
  border-color: #ad6610;
}

.modal-select option {
  background: #1f2937;
  color: #ffffff;
}

/* Form group styles */
.modal-form-group {
  margin-bottom: 16px;
}

.modal-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.modal-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #9ca3af;
}

/* Error states */
.modal-input.error,
.modal-select.error,
.modal-textarea.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.modal-error-text {
  margin-top: 4px;
  font-size: 12px;
  color: #ef4444;
}

/* Success states */
.modal-input.success,
.modal-select.success,
.modal-textarea.success {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .toast-container {
    left: 20px;
    right: 20px;
    max-width: none;
  }

  .modal-dialog {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-btn {
    width: 100%;
  }
}
