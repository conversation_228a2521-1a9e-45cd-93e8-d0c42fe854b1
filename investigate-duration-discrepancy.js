#!/usr/bin/env node

/**
 * Duration Discrepancy Investigation
 * Investigates why logs show 8 hours when actual stream was only 1 hour
 */

const { db } = require('./db/database');
const fs = require('fs');

console.log('🔍 Duration Discrepancy Investigation');
console.log('====================================\n');

class DurationDiscrepancyInvestigator {
  constructor() {
    this.findings = [];
  }

  async investigateTimestampIssues() {
    console.log('1️⃣ INVESTIGATING TIMESTAMP CALCULATION ISSUES...\n');

    // Check database timestamps
    await this.checkDatabaseTimestamps();
    
    // Check timezone handling
    await this.checkTimezoneHandling();
    
    // Check verification script logic
    await this.checkVerificationLogic();
    
    // Simulate duration calculations
    await this.simulateDurationCalculations();
  }

  async checkDatabaseTimestamps() {
    console.log('📊 CHECKING DATABASE TIMESTAMPS:');
    console.log('================================');

    try {
      const recentStreams = await new Promise((resolve, reject) => {
        db.all(`
          SELECT id, title, status, start_time, end_time, created_at, updated_at,
                 datetime(start_time) as start_formatted,
                 datetime(updated_at) as updated_formatted,
                 datetime(created_at) as created_formatted
          FROM streams 
          WHERE updated_at > datetime('now', '-2 days')
          ORDER BY updated_at DESC
          LIMIT 10
        `, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        });
      });

      if (recentStreams.length === 0) {
        console.log('❌ No recent streams found');
        return;
      }

      console.log(`Found ${recentStreams.length} recent streams:\n`);

      recentStreams.forEach((stream, index) => {
        console.log(`${index + 1}. "${stream.title}" (${stream.id})`);
        console.log(`   Status: ${stream.status}`);
        console.log(`   Raw start_time: ${stream.start_time}`);
        console.log(`   Raw updated_at: ${stream.updated_at}`);
        console.log(`   Raw created_at: ${stream.created_at}`);
        console.log(`   Formatted start_time: ${stream.start_formatted}`);
        console.log(`   Formatted updated_at: ${stream.updated_formatted}`);
        console.log(`   Formatted created_at: ${stream.created_formatted}`);

        // Calculate duration using different methods
        if (stream.start_time && stream.updated_at) {
          const startTime = new Date(stream.start_time);
          const endTime = new Date(stream.updated_at);
          const durationMs = endTime - startTime;
          const durationMinutes = Math.floor(durationMs / 1000 / 60);
          const durationHours = durationMinutes / 60;

          console.log(`   📊 CALCULATED DURATION:`);
          console.log(`      Start: ${startTime.toISOString()}`);
          console.log(`      End: ${endTime.toISOString()}`);
          console.log(`      Duration: ${durationMinutes} minutes (${durationHours.toFixed(1)} hours)`);

          // Check for suspicious durations
          if (durationMinutes > 300) { // More than 5 hours
            console.log(`   🚨 SUSPICIOUS: Duration seems too long for actual stream`);
            this.findings.push({
              type: 'SUSPICIOUS_DURATION',
              stream: stream.title,
              calculatedDuration: durationMinutes,
              issue: 'Duration calculation may be incorrect'
            });
          }

          // Check if start_time is much earlier than created_at
          const createdTime = new Date(stream.created_at);
          const startCreatedDiff = Math.abs(startTime - createdTime) / 1000 / 60; // minutes
          if (startCreatedDiff > 60) { // More than 1 hour difference
            console.log(`   ⚠️  WARNING: start_time is ${startCreatedDiff.toFixed(1)} minutes different from created_at`);
            this.findings.push({
              type: 'TIMESTAMP_MISMATCH',
              stream: stream.title,
              startCreatedDiff: startCreatedDiff,
              issue: 'start_time and created_at have large difference'
            });
          }
        }
        console.log('');
      });

    } catch (error) {
      console.log(`❌ Database check failed: ${error.message}`);
    }
  }

  async checkTimezoneHandling() {
    console.log('\n2️⃣ CHECKING TIMEZONE HANDLING:');
    console.log('==============================');

    try {
      // Test timezone conversion functions
      const { getCurrentUTC } = require('./utils/timezone');
      
      const currentUTC = await getCurrentUTC();
      const currentLocal = new Date();
      
      console.log(`Current UTC (from function): ${currentUTC}`);
      console.log(`Current Local (from Date): ${currentLocal.toISOString()}`);
      console.log(`System timezone offset: ${currentLocal.getTimezoneOffset()} minutes`);

      // Check if there's a timezone offset issue
      const utcTime = new Date(currentUTC);
      const timeDiff = Math.abs(utcTime - currentLocal) / 1000 / 60; // minutes
      
      if (timeDiff > 5) { // More than 5 minutes difference
        console.log(`⚠️  WARNING: UTC function and local time differ by ${timeDiff.toFixed(1)} minutes`);
        this.findings.push({
          type: 'TIMEZONE_DISCREPANCY',
          timeDiff: timeDiff,
          issue: 'UTC time function may have timezone issues'
        });
      } else {
        console.log(`✅ Timezone handling appears correct (${timeDiff.toFixed(1)} min difference)`);
      }

    } catch (error) {
      console.log(`❌ Timezone check failed: ${error.message}`);
    }
  }

  async checkVerificationLogic() {
    console.log('\n3️⃣ CHECKING VERIFICATION SCRIPT LOGIC:');
    console.log('======================================');

    try {
      // Read the verification script
      const verificationScript = fs.readFileSync('./verify-24h-streaming.js', 'utf8');
      
      // Check the duration calculation logic
      const durationCalcRegex = /Math\.floor\(\(currentTime - startTime\) \/ 1000 \/ 60\)/g;
      const matches = verificationScript.match(durationCalcRegex);
      
      if (matches) {
        console.log(`✅ Found ${matches.length} duration calculation(s) in verification script`);
        console.log(`   Formula: Math.floor((currentTime - startTime) / 1000 / 60)`);
        console.log(`   This calculates: (milliseconds difference) / 1000 / 60 = minutes`);
      }

      // Check for any hardcoded timestamps or test data
      if (verificationScript.includes('MusicCow') || verificationScript.includes('479')) {
        console.log(`🚨 CRITICAL: Verification script contains hardcoded test data!`);
        this.findings.push({
          type: 'HARDCODED_TEST_DATA',
          issue: 'Verification script may contain fake test data'
        });
      } else {
        console.log(`✅ No hardcoded test data found in verification script`);
      }

    } catch (error) {
      console.log(`❌ Verification script check failed: ${error.message}`);
    }
  }

  async simulateDurationCalculations() {
    console.log('\n4️⃣ SIMULATING DURATION CALCULATIONS:');
    console.log('====================================');

    // Simulate the exact calculation used in verification script
    console.log('Testing duration calculation with different scenarios:\n');

    const testCases = [
      {
        name: 'Normal 1-hour stream',
        startTime: new Date('2025-07-28T20:00:00.000Z'),
        endTime: new Date('2025-07-28T21:00:00.000Z'),
        expectedMinutes: 60
      },
      {
        name: 'Timezone issue simulation (start in different timezone)',
        startTime: new Date('2025-07-28T13:00:00.000Z'), // 1 PM UTC = 8 PM WIB
        endTime: new Date('2025-07-28T21:00:00.000Z'),   // 9 PM UTC = 4 AM WIB next day
        expectedMinutes: 480 // This would show 8 hours if timezone is wrong
      },
      {
        name: 'Database corruption simulation',
        startTime: new Date('2025-07-28T12:25:33.779Z'), // From log timestamp
        endTime: new Date('2025-07-28T21:30:34.185Z'),   // From log timestamp
        expectedMinutes: 545 // ~9 hours
      }
    ];

    testCases.forEach((testCase, index) => {
      const { startTime, endTime, expectedMinutes, name } = testCase;
      
      // Use the same calculation as verification script
      const calculatedMinutes = Math.floor((endTime - startTime) / 1000 / 60);
      const calculatedHours = calculatedMinutes / 60;
      
      console.log(`${index + 1}. ${name}:`);
      console.log(`   Start: ${startTime.toISOString()}`);
      console.log(`   End: ${endTime.toISOString()}`);
      console.log(`   Calculated: ${calculatedMinutes} minutes (${calculatedHours.toFixed(1)} hours)`);
      console.log(`   Expected: ${expectedMinutes} minutes`);
      
      if (Math.abs(calculatedMinutes - expectedMinutes) < 5) {
        console.log(`   ✅ Calculation matches expected result`);
      } else {
        console.log(`   🚨 MISMATCH: Calculation differs from expected by ${Math.abs(calculatedMinutes - expectedMinutes)} minutes`);
        
        if (calculatedMinutes === 479) {
          console.log(`   🎯 MATCH: This produces the exact 479 minutes (8 hours) from the log!`);
          this.findings.push({
            type: 'DURATION_CALCULATION_MATCH',
            testCase: name,
            calculatedMinutes: calculatedMinutes,
            issue: 'This scenario reproduces the 8-hour log entry'
          });
        }
      }
      console.log('');
    });
  }

  generateReport() {
    console.log('\n📋 INVESTIGATION REPORT:');
    console.log('========================');

    if (this.findings.length === 0) {
      console.log('✅ No critical issues found in duration calculations');
      return;
    }

    console.log(`🚨 Found ${this.findings.length} potential issues:\n`);

    this.findings.forEach((finding, index) => {
      console.log(`${index + 1}. ${finding.type}:`);
      if (finding.stream) console.log(`   Stream: ${finding.stream}`);
      if (finding.calculatedDuration) console.log(`   Duration: ${finding.calculatedDuration} minutes`);
      if (finding.timeDiff) console.log(`   Time Difference: ${finding.timeDiff.toFixed(1)} minutes`);
      if (finding.testCase) console.log(`   Test Case: ${finding.testCase}`);
      console.log(`   Issue: ${finding.issue}`);
      console.log('');
    });

    // Provide recommendations
    console.log('🔧 RECOMMENDATIONS:');
    console.log('===================');

    const hasTimezoneIssues = this.findings.some(f => f.type === 'TIMEZONE_DISCREPANCY');
    const hasTimestampMismatch = this.findings.some(f => f.type === 'TIMESTAMP_MISMATCH');
    const hasSuspiciousDuration = this.findings.some(f => f.type === 'SUSPICIOUS_DURATION');

    if (hasTimezoneIssues) {
      console.log('1. Fix timezone handling in getCurrentUTC() function');
    }
    if (hasTimestampMismatch) {
      console.log('2. Investigate why start_time differs significantly from created_at');
    }
    if (hasSuspiciousDuration) {
      console.log('3. Verify actual stream durations against database calculations');
    }

    console.log('4. Add real-time stream monitoring to verify actual vs calculated durations');
    console.log('5. Implement duration validation checks in logging system');
  }
}

async function main() {
  const investigator = new DurationDiscrepancyInvestigator();
  
  try {
    await investigator.investigateTimestampIssues();
    investigator.generateReport();
    
  } catch (error) {
    console.error('❌ Investigation failed:', error);
    process.exit(1);
  }
}

main().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
