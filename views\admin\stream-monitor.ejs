<% layout('layout') -%>

<!-- Header -->
<div class="bg-dark-800 border-b border-gray-700 p-6 -mx-6 -mt-6 mb-6">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">Stream Monitor</h1>
      <p class="text-gray-400 mt-1">Monitor and control FFmpeg processes and user streams (manual refresh)</p>
    </div>
    <div class="flex items-center space-x-4">
      <span class="text-sm text-gray-400">Active Streams: <span id="active-count" class="text-primary font-semibold"><%= totalActiveStreams %></span></span>
      <button id="sync-status-btn" onclick="syncStreamStatus()" class="btn-secondary">
        <i class="ti ti-refresh-dot mr-2"></i>
        Sync Status
      </button>
      <button id="refresh-btn" onclick="refreshStreamData()" class="btn-primary">
        <i class="ti ti-refresh mr-2"></i>
        Refresh
      </button>
    </div>
  </div>
</div>

<!-- Active Streams Section -->
<div class="card-enhanced p-6 mb-8">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-lg font-semibold text-white">Active FFmpeg Processes</h3>
      <p class="text-gray-400 text-sm">Currently running streams with real-time monitoring</p>
    </div>
    <div class="flex items-center space-x-3">
      <span id="last-updated" class="text-sm text-gray-400">Last updated: Just now</span>
    </div>
  </div>

  <div id="active-streams-container">
    <% if (activeStreams.length === 0) { %>
      <div class="text-center py-12">
        <i class="ti ti-video-off text-gray-500 text-4xl mb-4"></i>
        <p class="text-gray-400 text-lg">No active streams</p>
        <p class="text-gray-500 text-sm">All FFmpeg processes are currently stopped</p>
      </div>
    <% } else { %>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <% activeStreams.forEach(function(stream) { %>
          <div class="bg-dark-700 rounded-lg p-6 border border-gray-600" data-stream-id="<%= stream.id %>">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h4 class="text-white font-semibold"><%= stream.title %></h4>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                    Live
                  </span>
                </div>
                <div class="text-sm text-gray-400 space-y-1">
                  <p><i class="ti ti-user mr-2"></i><%= stream.user.username %> (<%= stream.user.email %>)</p>
                  <p><i class="ti ti-brand-<%= stream.platform.toLowerCase() %> mr-2"></i><%= stream.platform %></p>
                  <p><i class="ti ti-clock mr-2"></i>Started: <span class="stream-start-time">Just now</span></p>
                </div>
              </div>
              <div class="flex space-x-2">
                <button onclick="stopStream('<%= stream.id %>')" class="btn-danger btn-sm">
                  <i class="ti ti-square mr-1"></i>
                  Stop
                </button>
                <button onclick="showStreamLogs('<%= stream.id %>')" class="btn-secondary btn-sm">
                  <i class="ti ti-file-text mr-1"></i>
                  Logs
                </button>
              </div>
            </div>
            
            <!-- Stream Details -->
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-400">Status:</span>
                <span class="text-green-400 font-medium ml-2"><%= stream.status %></span>
              </div>
              <div>
                <span class="text-gray-400">Process ID:</span>
                <span class="text-white font-mono ml-2">Active</span>
              </div>
            </div>

            <!-- Recent Logs Preview -->
            <% if (stream.logs && stream.logs.length > 0) { %>
              <div class="mt-4 pt-4 border-t border-gray-600">
                <p class="text-gray-400 text-xs mb-2">Recent logs:</p>
                <div class="bg-dark-800 rounded p-3 text-xs font-mono text-gray-300 max-h-20 overflow-y-auto">
                  <% stream.logs.slice(-3).forEach(function(log) { %>
                    <div class="mb-1"><%= typeof log === 'string' ? log : JSON.stringify(log) %></div>
                  <% }); %>
                </div>
              </div>
            <% } %>
          </div>
        <% }); %>
      </div>
    <% } %>
  </div>
</div>

<!-- All Streams Management -->
<div class="card-enhanced p-6">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-lg font-semibold text-white">Stream Management</h3>
      <p class="text-gray-400 text-sm">Control all user streams</p>
    </div>
    <div class="flex items-center space-x-3">
      <form method="GET" action="/admin/stream-monitor" class="relative">
        <input type="text" name="search" id="stream-search" placeholder="Search streams..."
               value="<%= search || '' %>"
               class="bg-dark-700 border border-gray-600 text-white pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-primary w-64">
        <i class="ti ti-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
        <% if (search) { %>
          <a href="/admin/stream-monitor" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white">
            <i class="ti ti-x"></i>
          </a>
        <% } %>
      </form>
      <select id="status-filter" class="bg-dark-700 border border-gray-600 text-white px-3 py-2 rounded-lg" onchange="filterStreams()">
        <option value="">All Status</option>
        <option value="live">Live</option>
        <option value="offline">Offline</option>
        <option value="scheduled">Scheduled</option>
        <option value="error">Error</option>
      </select>
      <button onclick="startAllStreams()" class="btn-primary" id="start-all-btn">
        <i class="ti ti-player-play mr-2"></i>
        Start All
      </button>
    </div>
  </div>

  <div class="overflow-x-auto">
    <table class="w-full">
      <thead>
        <tr class="border-b border-gray-700">
          <th class="text-left py-3 px-4 text-gray-400 font-medium">Stream</th>
          <th class="text-left py-3 px-4 text-gray-400 font-medium">User</th>
          <th class="text-left py-3 px-4 text-gray-400 font-medium">Platform</th>
          <th class="text-left py-3 px-4 text-gray-400 font-medium">Status</th>
          <th class="text-left py-3 px-4 text-gray-400 font-medium">Actions</th>
        </tr>
      </thead>
      <tbody id="streams-table-body">
        <% allStreams.forEach(function(stream) { %>
          <tr class="border-b border-gray-700 hover:bg-dark-700 stream-row"
              data-stream-id="<%= stream.id %>"
              data-status="<%= stream.status %>"
              data-search="<%= stream.title.toLowerCase() %> <%= stream.user.username.toLowerCase() %>">
            <td class="py-4 px-4">
              <div>
                <p class="text-white font-medium"><%= stream.title %></p>
                <p class="text-gray-400 text-sm">ID: <%= stream.id.substring(0, 8) %>...</p>
              </div>
            </td>
            <td class="py-4 px-4">
              <div>
                <p class="text-white"><%= stream.user.username %></p>
                <p class="text-gray-400 text-sm"><%= stream.user.email %></p>
              </div>
            </td>
            <td class="py-4 px-4">
              <div class="flex items-center space-x-2">
                <i class="ti ti-brand-<%= stream.platform.toLowerCase() %> text-lg"></i>
                <span class="text-white"><%= stream.platform %></span>
              </div>
            </td>
            <td class="py-4 px-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                         <%= stream.status === 'live' ? 'bg-green-100 text-green-800' :
                             stream.status === 'offline' ? 'bg-gray-100 text-gray-800' :
                             stream.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                             stream.status === 'error' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800' %>">
                <% if (stream.status === 'live') { %>
                  <div class="w-2 h-2 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                <% } %>
                <%= stream.status.charAt(0).toUpperCase() + stream.status.slice(1) %>
              </span>
            </td>
            <td class="py-4 px-4">
              <div class="flex space-x-2">
                <% if (stream.status === 'live') { %>
                  <button onclick="stopStream('<%= stream.id %>')" class="btn-danger btn-sm">
                    <i class="ti ti-square mr-1"></i>
                    Stop
                  </button>
                <% } else if (stream.status === 'offline' || stream.status === 'error') { %>
                  <button onclick="startStream('<%= stream.id %>')" class="btn-primary btn-sm">
                    <i class="ti ti-play mr-1"></i>
                    Start
                  </button>
                <% } %>
                <button onclick="showStreamDetails('<%= stream.id %>')" class="btn-secondary btn-sm">
                  <i class="ti ti-info-circle mr-1"></i>
                  Details
                </button>
              </div>
            </td>
          </tr>
        <% }); %>
      </tbody>
    </table>

    <!-- No results message -->
    <% if (allStreams.length === 0) { %>
      <div class="p-8 text-center">
        <div class="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
          <i class="ti ti-video text-2xl text-gray-400"></i>
        </div>
        <% if (search) { %>
          <h3 class="text-lg font-medium text-white mb-2">No streams found</h3>
          <p class="text-gray-400 mb-4">No streams match your search criteria "<%= search %>"</p>
          <a href="/admin/stream-monitor" class="inline-flex items-center gap-2 text-primary hover:text-primary-light">
            <i class="ti ti-arrow-left"></i>
            Clear search and show all streams
          </a>
        <% } else { %>
          <h3 class="text-lg font-medium text-white mb-2">No streams found</h3>
          <p class="text-gray-400">No streams have been created yet.</p>
        <% } %>
      </div>
    <% } %>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="p-6 border-t border-gray-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          <% if (search) { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalStreams) %> of <%= totalStreams %> streams
            <span class="text-primary">matching "<%= search %>"</span>
          <% } else { %>
            Showing <%= ((currentPage - 1) * limit) + 1 %> to <%= Math.min(currentPage * limit, totalStreams) %> of <%= totalStreams %> streams
          <% } %>
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Button -->
          <% if (hasPrevPage) { %>
            <a href="/admin/stream-monitor?page=<%= prevPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-left"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-left"></i>
            </span>
          <% } %>

          <!-- Page Numbers -->
          <%
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            if (endPage - startPage < 4 && startPage > 1) {
              startPage = Math.max(1, endPage - 4);
            }
          %>

          <% for (let i = startPage; i <= endPage; i++) { %>
            <% if (i === currentPage) { %>
              <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-primary text-white font-medium">
                <%= i %>
              </span>
            <% } else { %>
              <a href="/admin/stream-monitor?page=<%= i %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
                 class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
                <%= i %>
              </a>
            <% } %>
          <% } %>

          <!-- Next Button -->
          <% if (hasNextPage) { %>
            <a href="/admin/stream-monitor?page=<%= nextPage %><%= search ? '&search=' + encodeURIComponent(search) : '' %>"
               class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 hover:bg-dark-600 text-gray-400 hover:text-white transition-colors">
              <i class="ti ti-chevron-right"></i>
            </a>
          <% } else { %>
            <span class="w-9 h-9 flex items-center justify-center rounded-lg bg-dark-700 text-gray-500 cursor-not-allowed">
              <i class="ti ti-chevron-right"></i>
            </span>
          <% } %>
        </div>
      </div>
    </div>
  <% } %>
</div>

<!-- Stream Logs Modal -->
<div id="logs-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-hidden">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Stream Logs</h3>
        <button onclick="closeLogsModal()" class="text-gray-400 hover:text-white">
          <i class="ti ti-x text-xl"></i>
        </button>
      </div>
      <div id="logs-content" class="bg-dark-900 rounded p-4 font-mono text-sm text-gray-300 h-96 overflow-y-auto">
        <!-- Logs will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
// Manual refresh only - no auto-refresh to avoid interrupting admin actions

function refreshStreamData() {
  // Full page refresh to ensure all data is updated
  window.location.reload();
}

async function syncStreamStatus() {
  const syncBtn = document.getElementById('sync-status-btn');
  const originalText = syncBtn.innerHTML;

  // Disable button and show loading state
  syncBtn.disabled = true;
  syncBtn.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Syncing...';

  try {
    const response = await fetch('/api/admin/sync-stream-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.success) {
      showToast('Stream statuses synchronized successfully - click Refresh to see updates', 'success');
      // Manual refresh - admin can click refresh button to see updated status
    } else {
      showToast(result.error || 'Failed to sync stream statuses', 'error');
    }
  } catch (error) {
    showToast('Error syncing stream statuses', 'error');
    console.error('Sync stream status error:', error);
  } finally {
    // Re-enable button
    syncBtn.disabled = false;
    syncBtn.innerHTML = originalText;
  }
}

// Manual refresh only - no auto-refresh to avoid interrupting admin actions
// Full page refresh ensures all data is properly updated

async function startStream(streamId) {
  try {
    const response = await fetch(`/admin/api/stream/${streamId}/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      showToast('Stream started successfully', 'success');
      // Manual refresh - admin can click refresh button if needed
    } else {
      showToast(result.error || 'Failed to start stream', 'error');
    }
  } catch (error) {
    showToast('Error starting stream', 'error');
  }
}

async function stopStream(streamId) {
  if (!confirm('Are you sure you want to stop this stream?')) {
    return;
  }

  try {
    const response = await fetch(`/admin/api/stream/${streamId}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.success) {
      showToast('Stream stopped successfully', 'success');
      // Manual refresh - admin can click refresh button if needed
    } else {
      showToast(result.error || 'Failed to stop stream', 'error');
    }
  } catch (error) {
    showToast('Error stopping stream', 'error');
  }
}

async function startAllStreams() {
  if (!confirm('Are you sure you want to start all offline streams? This may consume significant server resources.')) {
    return;
  }

  const startAllBtn = document.getElementById('start-all-btn');
  const originalText = startAllBtn.innerHTML;

  // Disable button and show loading state
  startAllBtn.disabled = true;
  startAllBtn.innerHTML = '<i class="ti ti-loader animate-spin mr-2"></i>Starting...';

  try {
    // Get all offline streams
    const offlineStreams = document.querySelectorAll('.stream-row[data-status="offline"], .stream-row[data-status="error"]');
    const streamIds = Array.from(offlineStreams).map(row => row.getAttribute('data-stream-id'));

    if (streamIds.length === 0) {
      showToast('No offline streams to start', 'info');
      return;
    }

    showToast(`Starting ${streamIds.length} streams...`, 'info');

    let successCount = 0;
    let failCount = 0;

    // Start streams in batches to avoid overwhelming the server
    const batchSize = 3;
    for (let i = 0; i < streamIds.length; i += batchSize) {
      const batch = streamIds.slice(i, i + batchSize);

      const promises = batch.map(async (streamId) => {
        try {
          const response = await fetch(`/admin/api/stream/${streamId}/start`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          const result = await response.json();

          if (result.success) {
            successCount++;
          } else {
            failCount++;
            console.error(`Failed to start stream ${streamId}:`, result.error);
          }
        } catch (error) {
          failCount++;
          console.error(`Error starting stream ${streamId}:`, error);
        }
      });

      await Promise.all(promises);

      // Small delay between batches
      if (i + batchSize < streamIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Show results
    if (successCount > 0 && failCount === 0) {
      showToast(`Successfully started ${successCount} streams`, 'success');
    } else if (successCount > 0 && failCount > 0) {
      showToast(`Started ${successCount} streams, ${failCount} failed`, 'warning');
    } else {
      showToast(`Failed to start ${failCount} streams`, 'error');
    }

    // Manual refresh - admin can click refresh button to see updated status

  } catch (error) {
    showToast('Error starting streams', 'error');
    console.error('Start all streams error:', error);
  } finally {
    // Re-enable button
    startAllBtn.disabled = false;
    startAllBtn.innerHTML = originalText;
  }
}

function showStreamLogs(streamId) {
  // For now, show a placeholder - in production you'd fetch real logs
  document.getElementById('logs-content').innerHTML = 'Loading logs...';
  document.getElementById('logs-modal').classList.remove('hidden');
  
  // Simulate loading logs
  setTimeout(() => {
    document.getElementById('logs-content').innerHTML = `
      <div>Stream ${streamId} logs:</div>
      <div>FFmpeg started successfully</div>
      <div>Connecting to RTMP server...</div>
      <div>Stream is live</div>
    `;
  }, 500);
}

function closeLogsModal() {
  document.getElementById('logs-modal').classList.add('hidden');
}

function showStreamDetails(streamId) {
  // Redirect to stream details or show modal
  showToast('Stream details feature coming soon', 'info');
}

// Auto-submit search form with debounce
let searchTimeout;
document.getElementById('stream-search').addEventListener('input', function(e) {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    e.target.form.submit();
  }, 500); // 500ms debounce
});

function filterStreams() {
  const statusFilter = document.getElementById('status-filter').value;
  const rows = document.querySelectorAll('.stream-row');

  rows.forEach(row => {
    const status = row.getAttribute('data-status');

    const matchesStatus = !statusFilter || status === statusFilter;

    if (matchesStatus) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

function showToast(message, type = 'info') {
  // Simple toast notification
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
    type === 'success' ? 'bg-green-600' :
    type === 'error' ? 'bg-red-600' :
    type === 'warning' ? 'bg-yellow-600' :
    'bg-blue-600'
  }`;
  toast.textContent = message;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.remove();
  }, 3000);
}

// Initialize - no auto-refresh, manual control only
document.addEventListener('DOMContentLoaded', function() {
  // Page loaded - admin can manually refresh when needed
});
</script>
